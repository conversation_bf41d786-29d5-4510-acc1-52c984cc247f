﻿using Datasoft.HR.Core.Common;
using Microsoft.Extensions.Hosting;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Datasoft.HR.Migration
{
    public static class Tools
    {
        public static IMongoCollection<TSource> GetTenantMongoCollection<TSource>() where TSource : BaseEntity, new()
        {
            var mongoClient = ServiceLocator.ResolveService<IMongoClient>();
            var loginUser = ServiceLocator.ResolveService<LoginUser>();
            var environment = ServiceLocator.ResolveService<IHostEnvironment>();

            var dbName = $"HRTenant{loginUser.CompanyId}-Period{loginUser.PeriodId}Db";

            if (!environment.IsProduction())
                dbName = environment.EnvironmentName.Replace("Deploy", string.Empty).Replace("Remote", string.Empty) + "-" + dbName;

            var db = mongoClient.GetDatabase(dbName);
            return db.GetCollection<TSource>(typeof(TSource).Name.ToLowerInvariant());
        }

        public static IMongoCollection<TSource> GetAdminMongoCollection<TSource>() where TSource : BaseEntity, new()
        {
            var mongoClient = ServiceLocator.ResolveService<IMongoClient>();
            var environment = ServiceLocator.ResolveService<IHostEnvironment>();

            var dbName = $"HRAdminDb";

            if (!environment.IsProduction())
                dbName = environment.EnvironmentName.Replace("Deploy", string.Empty).Replace("Remote", string.Empty) + "-" + dbName;

            var db = mongoClient.GetDatabase(dbName);
            return db.GetCollection<TSource>(typeof(TSource).Name.ToLowerInvariant());
        }

        public static TSource FillEntity<TSource>(this TSource entity) where TSource : BaseEntity, new()
        {
            var _loginUser = ServiceLocator.ResolveService<LoginUser>();
            if (entity == null)
                return entity;

            if (string.IsNullOrWhiteSpace(entity.Id))
            {
                entity.Id = ObjectId.GenerateNewId().ToString();
                entity.CreatedAt = DateTime.Now;
                entity.CreatedBy = _loginUser?.UserId;
            }

            entity.LastModifiedAt = DateTime.Now;
            entity.LastModifiedBy = _loginUser?.UserId;


            if (typeof(IBranchedEntity).IsAssignableFrom(typeof(TSource)))
                ((IBranchedEntity)entity).BranchId = _loginUser?.BranchId;

            return entity;
        }
        public static IEnumerable<TSource> FillEntities<TSource>(this IEnumerable<TSource> entities) where TSource : BaseEntity, new()
        {
            return entities?.Select(entity => FillEntity(entity));
        }
    }
}
