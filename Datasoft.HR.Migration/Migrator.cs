﻿using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Options;
using DatasoftCommon.CacheServices.Redis;
using MongoDB.Driver;

namespace Datasoft.HR.Migration
{
    public class Migrator
    {
        private readonly IEnumerable<ITenantMigration> _tenantMigrations;
        private readonly IEnumerable<IAdminMigration> _adminMigrations;
        private readonly IClientSessionHandle _clientSessionHandle;
        private readonly RedisLockService _redisLockService;
        private readonly LoginUser _loginUser;

        public Migrator(IEnumerable<ITenantMigration> tenantMigrations,
            IEnumerable<IAdminMigration> adminMigrations,
            IClientSessionHandle clientSessionHandle,
            RedisLockService redisLockService,
            LoginUser loginUser)
        {
            _tenantMigrations = tenantMigrations;
            _adminMigrations = adminMigrations;
            _clientSessionHandle = clientSessionHandle;
            _redisLockService = redisLockService;
            _loginUser = loginUser;
        }

        public async Task MigrateAdminAsync()
        {
            var cacheKey = $"MigrateAdminDb";

            if (!_redisLockService.LightIsGreen(cacheKey, TimeSpan.FromDays(10)))
                return;

            var collection = Tools.GetAdminMongoCollection<MigrationHistory>();
            var executedMigrations = collection
                .AsQueryable()
                .Select(x => x.MigrationName)
                .ToList();
            var migrations = _adminMigrations.Where(m => !executedMigrations.Contains(m.MigrationName)).OrderBy(x => x.MigrationCreateDate);

            foreach (var migration in migrations)
            {
                try
                {
                    _clientSessionHandle.StartTransaction();

                    await migration.MigrateAsync();
                    var entity = Tools.FillEntity(new MigrationHistory
                    {
                        MigrationName = migration.MigrationName,
                        MigrationCreateDate = migration.MigrationCreateDate,
                    });

                    await collection.InsertOneAsync(entity);
                    await _clientSessionHandle.CommitTransactionAsync();
                }
                catch
                {
                    await _clientSessionHandle.AbortTransactionAsync();
                    throw;
                }
            }
        }

        public async Task MigrateTenantAsync()
        {
            if (!_loginUser.IsAuthenticated)
                return;

            var cacheKey = $"MigrateTenant{_loginUser.CompanyId}-{(_loginUser.BranchId ?? 0)}-{_loginUser.PeriodId}";

            if (!_redisLockService.LightIsGreen(cacheKey, TimeSpan.FromDays(10)))
                return;

            var collection = Tools.GetTenantMongoCollection<MigrationHistory>();
            var executedMigrations = collection
                .AsQueryable()
                .Where(x => x.BranchId == _loginUser.BranchId)
                .Select(x => x.MigrationName)
                .ToList();

            var migrations = _tenantMigrations.Where(m => !executedMigrations.Contains(m.MigrationName)).OrderBy(x => x.MigrationCreateDate);

            foreach (var migration in migrations)
            {
                try
                {
                    _clientSessionHandle.StartTransaction();

                    await migration.MigrateAsync();
                    var entity = Tools.FillEntity(new MigrationHistory
                    {
                        MigrationName = migration.MigrationName,
                        MigrationCreateDate = migration.MigrationCreateDate,
                    });

                    await collection.InsertOneAsync(entity);
                    await _clientSessionHandle.CommitTransactionAsync();
                }
                catch
                {
                    await _clientSessionHandle.AbortTransactionAsync();
                    _redisLockService.UnLock(cacheKey);
                    throw;
                }
            }
        }
    }
}
