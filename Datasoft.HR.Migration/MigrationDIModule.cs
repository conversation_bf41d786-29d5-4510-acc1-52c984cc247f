﻿using Microsoft.Extensions.DependencyInjection;

namespace Datasoft.HR.Migration
{
    public static class MigrationDIModule
    {
        public static void Inject(IServiceCollection services)
        {
            services.AddTransient<Migrator>();

            string assemblyName = "Datasoft.HR.Migration";
            var assembly = AppDomain.CurrentDomain.Load(assemblyName);
            var types = assembly.GetTypes().Where(p => typeof(ITenantMigration).IsAssignableFrom(p) && !p.IsInterface).ToArray();

            foreach (var type in types)
                services.AddTransient(typeof(ITenantMigration), type);


            types = assembly.GetTypes().Where(p => typeof(IAdminMigration).IsAssignableFrom(p) && !p.IsInterface).ToArray();

            foreach (var type in types)
                services.AddTransient(typeof(IAdminMigration), type);
        }
    }
}
