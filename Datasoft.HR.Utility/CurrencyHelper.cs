﻿using System;
using System.Globalization;

namespace Datasoft.HR.Utility
{
    public static class CurrencyHelper
    {
        public static decimal ToRound(this decimal value, int decimals = 2)
        {
            return Math.Round(value, decimals, MidpointRounding.AwayFromZero);
        }

        public static decimal ToRound(this decimal? value, int decimals = 2)
        {
            if (!value.HasValue)
                return 0;

            return value.Value.ToRound(decimals);
        }

        public static string ToRoundedString(this decimal d, string seperator, bool showZero = false)
        {
            if (d <= 0)
                return showZero ? "0.00" : null;

            if (!string.IsNullOrEmpty(seperator))
                return d.ToRound(2).ToString(seperator);

            var result = d.ToRound(2).ToString("0.00", CultureInfo.InvariantCulture);
            return result;
        }

        public static string ToRoundedString(this decimal? d, string seperator, bool showZero = false)
        {
            if (!d.HasValue || d <= 0)
                return showZero ? "0.00" : null;

            var result = d.Value.ToRoundedString(seperator);
            return result;
        }

        public static string ToRoundedString(this decimal d, CultureInfo culture, bool showZero = false)
        {
            if (d <= 0)
                return showZero ? 0m.ToString("N2", culture) : null;
            var result = d.ToString("N2", culture);
            return result;
        }

        public static string ToRoundedStringNegative(this decimal d, CultureInfo culture,int round = 2)
        {
            var result = d.ToString("N"+round.ToString(), culture);
            return result;
        }

        /// <summary>
        /// BDP Programında kullanılan veriyi çevirmek için default cultureInfo
        /// </summary>
        public static CultureInfo BDPCultureInfo()
        {
            var culture = new CultureInfo("tr-TR");
            culture.NumberFormat.NumberDecimalDigits = 2;
            culture.NumberFormat.NumberDecimalSeparator = ".";
            
            return culture;
        }
        public static CultureInfo BDPCultureInfoV2()
        {
            var culture = new CultureInfo("tr-TR");
            culture.NumberFormat.NumberDecimalDigits = 2;
            
            culture.NumberFormat.NumberDecimalSeparator = "/";

            return culture;
        }
    }
}
