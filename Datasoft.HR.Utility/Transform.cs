﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Datasoft.HR.Utility
{
    public static class Transform
    {
        /// <summary>
        /// Tek karakterli ve ifadeler için regex için başına \ ifadesi ekler.
        /// </summary>
        /// <param name="input"><PERSON><PERSON><PERSON>ar <PERSON></param>
        /// <returns>Regex ifadesi</returns>
        private static string AddsPrefixForSingleCharacters(this char input)
        {
            return char.IsLetterOrDigit(input) || char.IsWhiteSpace(input) ? input.ToString() : @"\" + input.ToString();
        }

        private static string FormatText(this string input, params string[] groupAlpha)
        {
            if (string.IsNullOrWhiteSpace(input))
                return input;

            input = string.Concat(input.Select(_char => groupAlpha.FirstOrDefault(alpha => alpha.Contains(_char)) ?? _char.AddsPrefixForSingleCharacters()));
            return input;
        }

        public static string ToRegexEqualText(this string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return input;

            return $"^{FormatText(input, "[İi]", "[Iı]")}$";
        }

        public static string ToRegexContainsText(this string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return input;

            return $"{FormatText(input, "[İi]", "[Iı]")}";
        }

        public static string ToRegexContainsText(this IEnumerable<string> inputs)
        {
            inputs = inputs.Select(input => $"({FormatText(input, "[İi]", "[Iı]")})").ToArray();
            return $"({string.Join("|", inputs)})";
        }

        public static string CapitalizeFirstLetter(this string s)
        {
            if (string.IsNullOrEmpty(s))
                return s;
            if (s.Length == 1)
                return s.ToUpper();
            return s.Remove(1).ToUpper() + s.Substring(1);
        }

        public static string RemoveWithoutUpperAlphaNumeric(this string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return text;

            var allowedCharacters = "ABCÇDEFGHIİJKLMNOÖPRSŞTUÜVYZXWQ0123456789";
            text = string.Join(string.Empty, text.Select(c => allowedCharacters.Contains(c) ? c : ' '));
            text = string.Join(" ", text.Split(' ').Where(t => !string.IsNullOrWhiteSpace(t)));
            return text;
        }

        public static string ToUpperTurkish(this string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return text;

            var turkishCharacters = "çğıiöşü";
            var replacementCharacters = "ÇĞIİÖŞÜ";

            for (int i = 0; i < turkishCharacters.Length; i++)
                text = text.Replace(turkishCharacters[i], replacementCharacters[i]);

            text = text.ToUpper();

            return text;
        }
    }
}
