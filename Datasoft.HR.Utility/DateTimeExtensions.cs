﻿using System;

namespace Datasoft.HR.Utility
{
    public static class DateTimeExtensions
    {
        public static DateTime GetDateWithDayStart(this DateTime entity)
        {
            return entity.Date;
        }

        /// <summary>
        /// Saati 23:59:59 yapar.
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public static DateTime GetEndOfTheDay(this DateTime entity)
        {
            return entity.Date.AddDays(1).AddMilliseconds(-1);
        }
    }
}
