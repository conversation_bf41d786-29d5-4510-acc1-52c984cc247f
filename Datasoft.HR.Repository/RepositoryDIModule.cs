﻿using Datasoft.HR.Repository.Helpers;
using Datasoft.HR.Repository.Repositories.Implementations;
using Datasoft.HR.Repository.Repositories.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Datasoft.HR.Repository
{
    public static class RepositoryDIModule
    {
        public static void Inject(IServiceCollection services, IConfiguration configuration)
        {
            // helpers
            services.AddTransient<BaseEntityHelper>();

            // repositories
            // TODO: Auto injection yapılacak
            services.AddTransient<IExampleRepository, ExampleRepository>();
            services.AddTransient<IGeneralSettingRepository, GeneralSettingRepository>();
            services.AddTransient<IBranchRepository, BranchRepository>();
        }
    }
}
