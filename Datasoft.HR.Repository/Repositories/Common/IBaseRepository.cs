﻿using Datasoft.HR.Core.Common;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using System.Linq.Expressions;

namespace Datasoft.HR.Repository.Repositories.Common
{
    public interface IBaseRepository<TSource> where TSource : BaseEntity
    {
        IMongoQueryable<TSource> GetAllWithoutBranchFilter();
        IMongoQueryable<TSource> GetAllWithDeletedWithoutBranchFilter();
        IMongoQueryable<TSource> GetAll();
        IMongoQueryable<TSource> GetAllWithDeleted();
        IMongoQueryable<TDestination> GetAll<TDestination>(Expression<Func<TSource, TDestination>> @select);
        IMongoQueryable<TDestination> GetAllWithDeleted<TDestination>(Expression<Func<TSource, TDestination>> @select);
        IMongoQueryable<TDestination> GetAll<TDestination>();
        IMongoQueryable<TDestination> GetAllWithDeleted<TDestination>();

        IMongoQueryable<TSource> Where(Expression<Func<TSource, bool>> predicate);
        IMongoQueryable<TDestination> Where<TDestination>(Expression<Func<TSource, bool>> predicate, Expression<Func<TSource, TDestination>> select);
        IMongoQueryable<TDestination> Where<TDestination>(Expression<Func<TSource, bool>> predicate);
        IMongoQueryable<TSource> WhereWithDeleted(Expression<Func<TSource, bool>> predicate);
        IMongoQueryable<TDestination> WhereWithDeleted<TDestination>(Expression<Func<TSource, bool>> predicate, Expression<Func<TSource, TDestination>> select);
        IMongoQueryable<TDestination> WhereWithDeleted<TDestination>(Expression<Func<TSource, bool>> predicate);
        IFindFluent<TSource, TSource> Find(FilterDefinition<TSource> filterDefination, FindOptions findOptions = null);
        IMongoQueryable<TSource> GetById(string id) => Where(x => x.Id == id);
        IMongoQueryable<TDestination> GetById<TDestination>(string id, Expression<Func<TSource, TDestination>> @select);
        IMongoQueryable<TDestination> GetById<TDestination>(string id);
        IMongoQueryable<TSource> GetByIdWithDeleted(string id);
        IMongoQueryable<TDestination> GetByIdWithDeleted<TDestination>(string id, Expression<Func<TSource, TDestination>> @select);
        IMongoQueryable<TDestination> GetByIdWithDeleted<TDestination>(string id);

        Task<TSource> FirstOrDefaultAsync() => GetAll().FirstOrDefaultAsync();
        Task<TDestination> FirstOrDefaultAsync<TDestination>(Expression<Func<TSource, TDestination>> @select);
        Task<TDestination> FirstOrDefaultAsync<TDestination>();
        Task<TDestination> FirstOrDefaultAsync<TDestination>(Expression<Func<TSource, bool>> predicate);
        Task<TDestination> FirstOrDefaultAsync<TDestination>(Expression<Func<TSource, bool>> predicate, Expression<Func<TSource, TDestination>> @select);
        TSource FirstOrDefault();
        TDestination FirstOrDefault<TDestination>(Expression<Func<TSource, TDestination>> @select);
        TDestination FirstOrDefault<TDestination>();

        Task InsertAsync(TSource entity);
        Task InsertRangeAsync(IEnumerable<TSource> entities);

        Task<ReplaceOneResult> UpsertAsync(TSource entity);
        Task<BulkWriteResult<TSource>> SetSingleAsync(TSource entity);
        Task<ReplaceOneResult> UpdateAsync(TSource entity);
        Task<BulkWriteResult<TSource>> UpdateWhereAsync(FilterDefinition<TSource> filter, UpdateDefinition<TSource> update);
        Task<BulkWriteResult<TSource>> UpdateRangeAsync(IEnumerable<TSource> entities);
        Task<BulkWriteResult<TSource>> UpdateRangeOnlyFieldsAsync(Expression<Func<TSource, bool>> filter, UpdateDefinition<TSource> updateDefinition);
        Task<BulkWriteResult<TSource>> UpdateRangeWithoutAsync(IEnumerable<TSource> entities, params string[] names);
        Task<BulkWriteResult<TSource>> UpdateOnlyFieldsAsync(string id, UpdateDefinition<TSource> updateDefinition);
        Task<UpdateResult> UpdateOnlyFields(TSource entity, params string[] names);
        Task<UpdateResult> UpdateWithoutFields(TSource entity, params string[] names);

        Task<UpdateResult> DeleteAsync(string id);
        Task<UpdateResult> DeleteManyAsync(IEnumerable<string> idList);
        Task<UpdateResult> DeleteAsync(Expression<Func<TSource, bool>> predicate);
        Task<DeleteResult> HardDeleteAsync(string id);
        Task<DeleteResult> HardDeleteAsync(Expression<Func<TSource, bool>> predicate);

        BulkWriteResult<TSource> BulkWrite(List<WriteModel<TSource>> bulkOperations, bool hardDelete = false);
        Task<BulkWriteResult<TSource>> BulkWriteAsync(List<WriteModel<TSource>> bulkOperations, bool hardDelete = false);

        long Count(Expression<Func<TSource, bool>> predicate = null);
        Task<long> CountAsync(Expression<Func<TSource, bool>> predicate = null);
        long CountWithDeleted(Expression<Func<TSource, bool>> predicate = null);
        Task<long> CountWithDeletedAsync(Expression<Func<TSource, bool>> predicate = null);

        bool Any(FilterDefinition<TSource> filterDefinition = null);
        Task<bool> AnyAsync(FilterDefinition<TSource> filterDefinition = null);
        bool AnyWithDeleted(FilterDefinition<TSource> filterDefinition = null);
        Task<bool> AnyWithDeletedAsync(FilterDefinition<TSource> filterDefinition = null);
        IAggregateFluent<TSource> Aggregate();
        void SetBranchedEntity(long? branchId);
    }
}
