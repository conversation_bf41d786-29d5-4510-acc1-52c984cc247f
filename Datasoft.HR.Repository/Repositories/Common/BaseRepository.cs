﻿
using AutoMapper;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Migration;
using Datasoft.HR.Repository.Helpers;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Linq.Expressions;
using System.Reflection;

namespace Datasoft.HR.Repository.Repositories.Common
{
    public abstract class BaseRepository<TSource> : IBaseRepository<TSource>
        where TSource : BaseEntity
    {
        protected readonly IClientSessionHandle _clientSessionHandle;
        private readonly IMongoCollection<TSource> _collection;
        protected readonly IMapper _mapper;
        protected readonly LoginUser _loginUser;
        protected long? _branchId => _loginUser?.BranchId;
        protected bool _isBranchedEntity => typeof(IBranchedEntity).IsAssignableFrom(typeof(TSource));
        FilterDefinition<TSource> _branchFilterDefination => _isBranchedEntity ? Builders<TSource>.Filter.Eq(nameof(IBranchedEntity.BranchId), _branchId) : Builders<TSource>.Filter.Empty;
        FilterDefinition<TSource> _isNotDeletedFilterDefination => Builders<TSource>.Filter.Where(x => !x.IsDeleted);

        AggregateOptions aggregateOptions = new AggregateOptions { Collation = new Collation("tr", strength: CollationStrength.Secondary) };

        private readonly BaseEntityHelper _baseEntityHelper;
        public BaseRepository(IServiceProvider serviceProvider, bool isMasterDb = false, bool isAdminDb = false)
        {
            serviceProvider.GetRequiredService<Migrator>().MigrateTenantAsync().Wait();

            _mapper = serviceProvider.GetService<IMapper>();
            var mongoClient = serviceProvider.GetService<IMongoClient>();
            _loginUser = serviceProvider.GetService<LoginUser>();
            _baseEntityHelper = serviceProvider.GetService<BaseEntityHelper>();
            _clientSessionHandle = serviceProvider.GetService<IClientSessionHandle>();
            var environment = serviceProvider.GetService<IHostEnvironment>();

            var dbName = isAdminDb ? "HRAdminDb" : isMasterDb ? $"HRTenant{_loginUser.CompanyId}Db" : $"HRTenant{_loginUser.CompanyId}-Period{_loginUser.PeriodId}Db";

            if (!environment.IsProduction())
                dbName = environment.EnvironmentName.Replace("Deploy", string.Empty).Replace("Remote", string.Empty) + "-" + dbName;

            // Satış preprod canlı database'nin kopyasına baktığı için böyle  bir kod yazdım
            // Satış ortamı canlıya çıkınca burası silinebilir
            if (environment.EnvironmentName.Contains("SalesPreprod"))
                dbName = dbName.Replace("SalesPreprod", "Production");

            var db = mongoClient.GetDatabase(dbName);
            _collection = db.GetCollection<TSource>(typeof(TSource).Name.ToLowerInvariant());
        }
        public virtual IMongoQueryable<TDestination> ProjectTo<TDestination>(IMongoQueryable query)
        {
            return (IMongoQueryable<TDestination>)_mapper.ProjectTo<TDestination>(query);
        }

        public virtual IMongoQueryable<TSource> GetAllWithoutBranchFilter() => GetAllWithDeletedWithoutBranchFilter().Where(x => !x.IsDeleted);
        public virtual IMongoQueryable<TSource> GetAllWithDeletedWithoutBranchFilter() => _collection.AsQueryable(_clientSessionHandle, aggregateOptions);
        public virtual IMongoQueryable<TSource> GetAll() => GetAllWithDeleted().Where(x => !x.IsDeleted);
        public virtual IMongoQueryable<TSource> GetAllWithDeleted() => FilterWithBranch(_collection.AsQueryable(_clientSessionHandle, aggregateOptions));
        public virtual IMongoQueryable<TDestination> GetAll<TDestination>(Expression<Func<TSource, TDestination>> @select) => GetAll().Select(@select);
        public virtual IMongoQueryable<TDestination> GetAllWithDeleted<TDestination>(Expression<Func<TSource, TDestination>> @select) => GetAllWithDeleted().Select(@select);
        public virtual IMongoQueryable<TDestination> GetAll<TDestination>() => ProjectTo<TDestination>(GetAll());
        public virtual IMongoQueryable<TDestination> GetAllWithDeleted<TDestination>() => ProjectTo<TDestination>(GetAllWithDeleted());

        public virtual IMongoQueryable<TSource> Where(Expression<Func<TSource, bool>> predicate) => GetAll().Where(predicate);
        public virtual IMongoQueryable<TDestination> Where<TDestination>(Expression<Func<TSource, bool>> predicate, Expression<Func<TSource, TDestination>> select) => Where(predicate).Select(select);
        public virtual IMongoQueryable<TDestination> Where<TDestination>(Expression<Func<TSource, bool>> predicate) => ProjectTo<TDestination>(Where(predicate));
        public virtual IMongoQueryable<TSource> WhereWithDeleted(Expression<Func<TSource, bool>> predicate) => GetAllWithDeleted().Where(predicate);
        public virtual IMongoQueryable<TDestination> WhereWithDeleted<TDestination>(Expression<Func<TSource, bool>> predicate, Expression<Func<TSource, TDestination>> select) => WhereWithDeleted(predicate).Select(select);
        public virtual IMongoQueryable<TDestination> WhereWithDeleted<TDestination>(Expression<Func<TSource, bool>> predicate) => ProjectTo<TDestination>(WhereWithDeleted(predicate));

        public IFindFluent<TSource, TSource> Find(FilterDefinition<TSource> filterDefination, FindOptions findOptions = null) => _collection.Find(_clientSessionHandle, _branchFilterDefination & _isNotDeletedFilterDefination & filterDefination, findOptions);

        public virtual IMongoQueryable<TSource> GetById(string id) => Where(x => x.Id == id);
        public virtual IMongoQueryable<TDestination> GetById<TDestination>(string id, Expression<Func<TSource, TDestination>> @select) => Where(x => x.Id == id, select);
        public virtual IMongoQueryable<TDestination> GetById<TDestination>(string id) => Where<TDestination>(x => x.Id == id);
        public virtual IMongoQueryable<TSource> GetByIdWithDeleted(string id) => WhereWithDeleted(x => x.Id == id);
        public virtual IMongoQueryable<TDestination> GetByIdWithDeleted<TDestination>(string id, Expression<Func<TSource, TDestination>> @select) => WhereWithDeleted(x => x.Id == id, select);
        public virtual IMongoQueryable<TDestination> GetByIdWithDeleted<TDestination>(string id) => WhereWithDeleted<TDestination>(x => x.Id == id);

        //public virtual Task<TSource> FirstOrDefaultAsync() => GetAll().FirstOrDefaultAsync();
        public virtual Task<TDestination> FirstOrDefaultAsync<TDestination>(Expression<Func<TSource, TDestination>> @select) => GetAll(select).FirstOrDefaultAsync();
        public virtual Task<TDestination> FirstOrDefaultAsync<TDestination>() => GetAll<TDestination>().FirstOrDefaultAsync();
        public virtual Task<TDestination> FirstOrDefaultAsync<TDestination>(Expression<Func<TSource, bool>> predicate) => Where<TDestination>(predicate).FirstOrDefaultAsync();
        public virtual Task<TDestination> FirstOrDefaultAsync<TDestination>(Expression<Func<TSource, bool>> predicate, Expression<Func<TSource, TDestination>> @select) => Where<TDestination>(predicate, select).FirstOrDefaultAsync();
        public TSource FirstOrDefault() => GetAll().FirstOrDefault();
        public TDestination FirstOrDefault<TDestination>(Expression<Func<TSource, TDestination>> @select) => GetAll(select).FirstOrDefault();
        public TDestination FirstOrDefault<TDestination>() => GetAll<TDestination>().FirstOrDefault();

        public virtual Task InsertAsync(TSource entity) => _collection.InsertOneAsync(_clientSessionHandle, _baseEntityHelper.FillBaseEntity(entity));
        public virtual Task InsertRangeAsync(IEnumerable<TSource> entities) => _collection.InsertManyAsync(_clientSessionHandle, _baseEntityHelper.FillBaseEntities(entities));

        public virtual Task<ReplaceOneResult> UpsertAsync(TSource entity)
        {
            _baseEntityHelper.FillBaseEntity(entity);
            var filter = _branchFilterDefination & Builders<TSource>.Filter.Eq(x => x.Id, entity.Id);
            return _collection.ReplaceOneAsync(_clientSessionHandle, filter, entity, new ReplaceOptions
            {
                IsUpsert = true
            });
        }

        public virtual Task<BulkWriteResult<TSource>> SetSingleAsync(TSource entity)
        {
            _baseEntityHelper.FillBaseEntity(entity);
            var opeations = new List<WriteModel<TSource>>
            {
                new DeleteManyModel<TSource>(_branchFilterDefination),
                new InsertOneModel<TSource>(entity)
            };

            return _collection.BulkWriteAsync(_clientSessionHandle, opeations, new BulkWriteOptions { IsOrdered = true });
        }

        public virtual Task<ReplaceOneResult> UpdateAsync(TSource entity)
        {
            var filter = _branchFilterDefination & Builders<TSource>.Filter.Eq(x => x.Id, entity.Id);
            return _collection.ReplaceOneAsync(_clientSessionHandle, filter, _baseEntityHelper.FillBaseEntity(entity));
        }

        public virtual Task<BulkWriteResult<TSource>> UpdateWhereAsync(FilterDefinition<TSource> filter, UpdateDefinition<TSource> update)
        {
            return BulkWriteAsync(new List<WriteModel<TSource>>
            {
                new UpdateManyModel<TSource>(filter,update)
            });
        }

        public virtual Task<BulkWriteResult<TSource>> UpdateRangeAsync(IEnumerable<TSource> entities)
        {
            var updateOperations = entities
                .Select(entity => new ReplaceOneModel<TSource>
                (
                    _branchFilterDefination & Builders<TSource>.Filter.Eq(x => x.Id, entity.Id),
                    entity
                )).ToList<WriteModel<TSource>>();

            return BulkWriteAsync(updateOperations);
        }

        public virtual Task<BulkWriteResult<TSource>> UpdateRangeOnlyFieldsAsync(Expression<Func<TSource, bool>> filter, UpdateDefinition<TSource> updateDefinition)
        {
            var bulkOperations = new List<WriteModel<TSource>> {
                new UpdateManyModel<TSource>(filter, updateDefinition)
            };

            bulkOperations = _baseEntityHelper.FillBaseEntities(bulkOperations, false);
            return _collection.BulkWriteAsync(_clientSessionHandle, bulkOperations, new BulkWriteOptions()
            {
                IsOrdered = true
            });
        }

        public virtual Task<BulkWriteResult<TSource>> UpdateRangeWithoutAsync(IEnumerable<TSource> entities, params string[] names)
        {
            if (names == null || names.Length == 0)
                throw new Exception("Arguments required 'names' for 'UpdateWithoutFields'");

            entities = _baseEntityHelper.FillBaseEntities(entities);

            var ignoreList = new string[]
            {
                nameof(BaseEntity.CreatedAt),
                nameof(BaseEntity.CreatedBy)
            };

            var updateOperations = new List<WriteModel<TSource>>();

            foreach (var item in entities)
            {
                var json = JsonConvert.SerializeObject(item, new JsonSerializerSettings()
                {
                    ContractResolver = new UpdateWithoutPropertiesResolver(names),
                });
                var bson = BsonDocument.Parse(json);
                var setBsonDocument = new BsonDocument(bson.Where(doc => doc.Value != BsonNull.Value));
                var unsetBsonDocument = new BsonDocument(bson.Where(doc => doc.Value == BsonNull.Value && !ignoreList.Contains(doc.Name)));

                var update = Builders<TSource>.Update.Combine(
                    new BsonDocument("$set", setBsonDocument),
                    new BsonDocument("$unset", unsetBsonDocument)
                    );

                updateOperations.Add(new UpdateOneModel<TSource>(_branchFilterDefination & Builders<TSource>.Filter.Eq(x => x.Id, item.Id), update));
            }

            return BulkWriteAsync(updateOperations);
        }

        public virtual Task<BulkWriteResult<TSource>> UpdateOnlyFieldsAsync(string id, UpdateDefinition<TSource> updateDefinition)
        {
            var bulkOperations = new List<WriteModel<TSource>> {
                new UpdateOneModel<TSource>(Builders<TSource>.Filter.Eq(x=>x.Id,id), updateDefinition)
            };

            bulkOperations = _baseEntityHelper.FillBaseEntities(bulkOperations, false);
            return _collection.BulkWriteAsync(_clientSessionHandle, bulkOperations, new BulkWriteOptions()
            {
                IsOrdered = true
            });
        }


        public virtual Task<UpdateResult> UpdateOnlyFields(TSource entity, params string[] names)
        {
            if (names == null || names.Length == 0)
                throw new Exception("Arguments required 'names' for 'UpdateOnlyFields'");

            _baseEntityHelper.FillBaseEntity(entity);
            names = names.Union(new[] { nameof(entity.LastModifiedAt), nameof(entity.LastModifiedBy) }).ToArray();

            var json = JsonConvert.SerializeObject(entity, new JsonSerializerSettings()
            {
                ContractResolver = new UpdateOnlyPropertiesResolver(names),
            });

            var bson = BsonDocument.Parse(json);
            var setBsonDocument = new BsonDocument(bson.Where(doc => doc.Value != BsonNull.Value));
            var unsetBsonDocument = new BsonDocument(bson.Where(doc => doc.Value == BsonNull.Value));

            var update = Builders<TSource>.Update.Combine(
                new BsonDocument("$set", setBsonDocument),
                new BsonDocument("$unset", unsetBsonDocument)
                );

            var filter = _branchFilterDefination & Builders<TSource>.Filter.Eq(x => x.Id, entity.Id);

            return _collection.UpdateOneAsync(_clientSessionHandle, filter, update);
        }

        public virtual Task<UpdateResult> UpdateWithoutFields(TSource entity, params string[] names)
        {
            if (names == null || names.Length == 0)
                throw new Exception("Arguments required 'names' for 'UpdateWithoutFields'");

            _baseEntityHelper.FillBaseEntity(entity);
            names = names.Union(new[] { nameof(entity.CreatedAt), nameof(entity.CreatedBy) }).ToArray();

            var json = JsonConvert.SerializeObject(entity, new JsonSerializerSettings()
            {
                ContractResolver = new UpdateWithoutPropertiesResolver(names)
            });
            var bson = BsonDocument.Parse(json);
            var setBsonDocument = new BsonDocument(bson.Where(doc => doc.Value != BsonNull.Value));
            var unsetBsonDocument = new BsonDocument(bson.Where(doc => doc.Value == BsonNull.Value));

            var update = Builders<TSource>.Update.Combine(
                new BsonDocument("$set", setBsonDocument),
                new BsonDocument("$unset", unsetBsonDocument)
                );

            var filter = _branchFilterDefination & Builders<TSource>.Filter.Eq(x => x.Id, entity.Id);

            return _collection.UpdateOneAsync(_clientSessionHandle, filter, update);
        }

        public virtual Task<UpdateResult> DeleteAsync(string id)
        {
            var updateDefinitions = new List<UpdateDefinition<TSource>>
            {
                Builders<TSource>.Update.SetValue(x => x.IsDeleted, true),
                Builders<TSource>.Update.SetValue(x => x.LastModifiedAt, DateTime.Now),
                Builders<TSource>.Update.SetValue(x => x.LastModifiedBy, _loginUser?.UserId)
            };

            var update = Builders<TSource>.Update.Combine(updateDefinitions);
            var filter = _branchFilterDefination & Builders<TSource>.Filter.Eq(x => x.Id, id);
            return _collection.UpdateOneAsync(_clientSessionHandle, filter, update);
        }

        public virtual Task<UpdateResult> DeleteManyAsync(IEnumerable<string> idList)
        {
            var updateDefinitions = new List<UpdateDefinition<TSource>>
            {
                Builders<TSource>.Update.SetValue(x => x.IsDeleted, true),
                Builders<TSource>.Update.SetValue(x => x.LastModifiedAt, DateTime.Now),
                Builders<TSource>.Update.SetValue(x => x.LastModifiedBy, _loginUser?.UserId)
            };

            var update = Builders<TSource>.Update.Combine(updateDefinitions);

            var filter = _branchFilterDefination & Builders<TSource>.Filter.Where(x => idList.Contains(x.Id));

            return _collection.UpdateManyAsync(_clientSessionHandle, filter, update);
        }

        public virtual Task<UpdateResult> DeleteAsync(Expression<Func<TSource, bool>> predicate)
        {
            var updateDefinitions = new List<UpdateDefinition<TSource>>
            {
                Builders<TSource>.Update.SetValue(x => x.IsDeleted, true),
                Builders<TSource>.Update.SetValue(x => x.LastModifiedAt, DateTime.Now),
                Builders<TSource>.Update.SetValue(x => x.LastModifiedBy, _loginUser?.UserId)
            };

            var update = Builders<TSource>.Update.Combine(updateDefinitions);
            var filter = _branchFilterDefination & predicate;
            return _collection.UpdateManyAsync(_clientSessionHandle, filter, update);
        }

        public virtual Task<DeleteResult> HardDeleteAsync(string id) => _collection.DeleteOneAsync(_clientSessionHandle, _branchFilterDefination & Builders<TSource>.Filter.Eq(x => x.Id, id));

        public virtual Task<DeleteResult> HardDeleteAsync(Expression<Func<TSource, bool>> predicate) => _collection.DeleteOneAsync(_clientSessionHandle, _branchFilterDefination & predicate);

        public BulkWriteResult<TSource> BulkWrite(List<WriteModel<TSource>> bulkOperations, bool hardDelete = false)
        {
            bulkOperations = _baseEntityHelper.FillBaseEntities(bulkOperations, hardDelete);
            return _collection.BulkWrite(_clientSessionHandle, bulkOperations, new BulkWriteOptions()
            {
                IsOrdered = true
            });
        }

        public virtual Task<BulkWriteResult<TSource>> BulkWriteAsync(List<WriteModel<TSource>> bulkOperations, bool hardDelete = false)
        {
            bulkOperations = _baseEntityHelper.FillBaseEntities(bulkOperations, hardDelete);
            return _collection.BulkWriteAsync(_clientSessionHandle, bulkOperations, new BulkWriteOptions()
            {
                IsOrdered = true
            });
        }

        public long Count(Expression<Func<TSource, bool>> predicate = null)
        {
            var filter = _branchFilterDefination & Builders<TSource>.Filter.Where(x => !x.IsDeleted);
            filter = predicate != null ? predicate & filter : filter;

            return _collection.CountDocuments(_clientSessionHandle, filter);
        }

        public virtual Task<long> CountAsync(Expression<Func<TSource, bool>> predicate = null)
        {
            var filter = _branchFilterDefination & Builders<TSource>.Filter.Where(x => !x.IsDeleted);
            filter = predicate != null ? predicate & filter : filter;

            return _collection.CountDocumentsAsync(_clientSessionHandle, filter);
        }

        public long CountWithDeleted(Expression<Func<TSource, bool>> predicate = null)
        {
            var filter = _branchFilterDefination;

            if (predicate != null)
                filter = filter & predicate;

            return _collection.CountDocuments(_clientSessionHandle, filter);
        }

        public virtual Task<long> CountWithDeletedAsync(Expression<Func<TSource, bool>> predicate = null)
        {
            var filter = _branchFilterDefination;

            if (predicate != null)
                filter = filter & predicate;

            return _collection.CountDocumentsAsync(_clientSessionHandle, filter);
        }

        public bool Any(FilterDefinition<TSource> filterDefinition = null)
        {
            var filter = _branchFilterDefination & Builders<TSource>.Filter.Eq(x => x.IsDeleted, false);
            if (filterDefinition != null)
                filter = filter & filterDefinition;

            return _collection.Aggregate()
                .Match(filter)
                .Limit(1)
                .Project(new Dictionary<string, int>
                {
                    { "Id", 1 }
                }.ToBsonDocument())
                .Any();
        }

        public virtual Task<bool> AnyAsync(FilterDefinition<TSource> filterDefinition = null)
        {
            var filter = _branchFilterDefination & Builders<TSource>.Filter.Eq(x => x.IsDeleted, false);
            if (filterDefinition != null)
                filter = filter & filterDefinition;

            return _collection.Aggregate()
                .Match(filter)
                .Limit(1)
                .Project(new Dictionary<string, int>
                {
                    { "Id", 1 }
                }.ToBsonDocument())
                .AnyAsync();
        }

        public bool AnyWithDeleted(FilterDefinition<TSource> filterDefinition = null)
        {
            var filter = _branchFilterDefination;
            if (filterDefinition != null)
                filter = filter & filterDefinition;

            return _collection.Aggregate()
                .Match(filter)
                .Limit(1)
                .Project(new Dictionary<string, int>
                {
                    { "Id", 1 }
                }.ToBsonDocument())
                .Any();
        }

        public virtual Task<bool> AnyWithDeletedAsync(FilterDefinition<TSource> filterDefinition = null)
        {
            var filter = _branchFilterDefination;
            if (filterDefinition != null)
                filter = filter & filterDefinition;

            return _collection.Aggregate()
                .Match(filter)
                .Limit(1)
                .Project(new Dictionary<string, int>
                {
                    { "Id", 1 }
                }.ToBsonDocument())
                .AnyAsync();
        }

        public IAggregateFluent<TSource> Aggregate()
        {
            var aggregate = _collection.Aggregate(_clientSessionHandle).Match(x => !x.IsDeleted);
            if (_isBranchedEntity)
                aggregate = aggregate.Match(x => ((IBranchedEntity)x).BranchId == _branchId);

            return aggregate;
        }

        public void SetBranchedEntity(long? branchId)
        {
            if (_isBranchedEntity)
            {
                var update = new List<WriteModel<TSource>> {
                        new UpdateManyModel<TSource>(
                            Builders<TSource>.Filter.Empty,
                            Builders<TSource>.Update.SetValue(x => ((IBranchedEntity)x).BranchId, branchId)
                            )
                    };

                _collection.BulkWrite(update);
            }
        }

        private IMongoQueryable<TSource> FilterWithBranch(IMongoQueryable<TSource> query)
        {
            if (_isBranchedEntity)
                return query.Where(x => ((IBranchedEntity)x).BranchId == _branchId);

            return query;
        }
    }

    public class UpdateWithoutPropertiesResolver : DefaultContractResolver
    {
        private readonly HashSet<string> propNames;
        public UpdateWithoutPropertiesResolver(IEnumerable<string> withoutPropertie) => propNames = new HashSet<string>(withoutPropertie);

        protected override JsonProperty CreateProperty(MemberInfo member, MemberSerialization memberSerialization)
        {
            JsonProperty property = base.CreateProperty(member, memberSerialization);
            property.NullValueHandling = NullValueHandling.Include;

            if (propNames.Contains(property.PropertyName))
                property.ShouldSerialize = _ => false;

            return property;
        }
    }

    public class UpdateOnlyPropertiesResolver : DefaultContractResolver
    {
        private readonly HashSet<string> propNames;
        public UpdateOnlyPropertiesResolver(IEnumerable<string> onlyPropertiesR) => propNames = new HashSet<string>(onlyPropertiesR);

        protected override JsonProperty CreateProperty(MemberInfo member, MemberSerialization memberSerialization)
        {
            JsonProperty property = base.CreateProperty(member, memberSerialization);
            property.NullValueHandling = NullValueHandling.Include;

            if (!propNames.Contains(property.PropertyName))
                property.ShouldSerialize = _ => false;

            return property;
        }
    }
}
