﻿using Datasoft.HR.Core.Entities.Examples;
using Datasoft.HR.Repository.Repositories.Common;
using Datasoft.HR.Repository.Repositories.Interfaces;

namespace Datasoft.HR.Repository.Repositories.Implementations
{
    public class ExampleRepository : BaseRepository<ExampleEntity>, IExampleRepository
    {
        public ExampleRepository(IServiceProvider serviceProvider, bool isMasterDb = false, bool isAdminDb = false) : base(serviceProvider, isMasterDb, isAdminDb)
        {
        }
    }
}
