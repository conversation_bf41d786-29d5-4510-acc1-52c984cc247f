﻿using Datasoft.HR.Core.Entities.Company;
using Datasoft.HR.Repository.Repositories.Common;
using Datasoft.HR.Repository.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Repository.Repositories.Implementations
{
    public class BranchSettingRepository : BaseRepository<BranchSetting>, IBranchSettingRepository
    {
        public BranchSettingRepository(IServiceProvider serviceProvider) : base(serviceProvider) { }
    }
}
