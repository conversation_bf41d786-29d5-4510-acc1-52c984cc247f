﻿using Datasoft.HR.Core.Entities.Branch;
using Datasoft.HR.DTO.Branch;
using Datasoft.HR.Repository.Repositories.Common;
using MongoDB.Driver.Linq;
using System.Text.RegularExpressions;


namespace Datasoft.HR.Repository.Repositories.Implementations
{
    public class BranchRepository : BaseRepository<Branch> , IBranchRepository
    {
        public BranchRepository(IServiceProvider serviceProvider)
            : base(serviceProvider, isMasterDb: false, isAdminDb: false)
        {
        }

        public IMongoQueryable<Branch> Search(BranchQueryDto searchQueryDto)
        {
            var query = GetAll();

            // Etiketlere göre filtreleme
            if (searchQueryDto.Tags != null && searchQueryDto.Tags.Any())
            {
                foreach (var tag in searchQueryDto.Tags)
                {
                    query = query.Where(x => Regex.IsMatch(x.TagsJSON, tag, RegexOptions.IgnoreCase));
                }
            }

            // Şube kodu başlangıç - bitiş
            if (!string.IsNullOrWhiteSpace(searchQueryDto.BranchCodeStart))
            {
                query = query.Where(x => string.Compare(x.GeneralInformation.BranchCode, searchQueryDto.BranchCodeStart, StringComparison.OrdinalIgnoreCase) >= 0);
            }

            if (!string.IsNullOrWhiteSpace(searchQueryDto.BranchCodeEnd))
            {
                query = query.Where(x => string.Compare(x.GeneralInformation.BranchCode, searchQueryDto.BranchCodeEnd, StringComparison.OrdinalIgnoreCase) <= 0);
            }

            // Şube adı listesi
            if (searchQueryDto.BranchNames != null && searchQueryDto.BranchNames.Any())
            {
                var branchNamesLower = searchQueryDto.BranchNames.Select(n => n.ToLowerInvariant()).ToList();
                query = query.Where(x => branchNamesLower.Contains(x.GeneralInformation.BranchName.ToLower()));
            }

            // Şube türü
            if (searchQueryDto.BranchType.HasValue)
            {
                query = query.Where(x => x.GeneralInformation.BranchType == searchQueryDto.BranchType);
            }

            // Mersis numaraları
            if (searchQueryDto.MersisNumbers != null && searchQueryDto.MersisNumbers.Any())
            {
                var mersisNumbersLower = searchQueryDto.MersisNumbers.Select(n => n.ToLowerInvariant()).ToList();
                query = query.Where(x => mersisNumbersLower.Contains(x.GeneralInformation.MersisNo.ToLower()));
            }

            // Ülke
            if (searchQueryDto.Country != null)
            {
                query = query.Where(x => x.Address.Country.Id == searchQueryDto.Country.Id);
            }

            // Şehir
            if (searchQueryDto.City != null)
            {
                query = query.Where(x => x.Address.City.Id == searchQueryDto.City.Id);
            }

            // İlçe
            if (searchQueryDto.County != null)
            {
                query = query.Where(x => x.Address.County.Id == searchQueryDto.County.Id);
            }

            // Aktiflik durumu
            if (searchQueryDto.IsActive != null && searchQueryDto.IsActive.Any())
            {
                query = query.Where(x => searchQueryDto.IsActive.Contains(x.AdditionalInformation.IsActive));
            }

            return query;
        }
    }

}
