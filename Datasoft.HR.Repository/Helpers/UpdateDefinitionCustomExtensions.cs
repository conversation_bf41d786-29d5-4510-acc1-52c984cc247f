﻿using MongoDB.Driver;
using System.Linq.Expressions;

namespace Datasoft.HR.Repository.Helpers
{
    public static class UpdateDefinitionCustomExtensions
    {
        /// <summary>
        /// property de<PERSON><PERSON>ni set eder, değer null ise property'yi db'den siler
        /// </summary>
        /// <typeparam name="TField"></typeparam>
        /// <typeparam name="TDocument"></typeparam>
        /// <param name="builder"></param>
        /// <param name="field"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public static UpdateDefinition<TDocument> SetValue<TField, TDocument>(this UpdateDefinition<TDocument> update, Expression<Func<TDocument, TField>> field, TField value)
        {
            if (value == null)
                return Builders<TDocument>.Update.Combine(update, Builders<TDocument>.Update.Unset(new ExpressionFieldDefinition<TDocument>(field)));
            else
                return Builders<TDocument>.Update.Combine(update, Builders<TDocument>.Update.Set(field, value));
        }
        public static UpdateDefinition<TDocument> SetValue<TField, TDocument>(this UpdateDefinition<TDocument> update, FieldDefinition<TDocument, TField> field, TField value)
        {
            if (value == null)
                return Builders<TDocument>.Update.Combine(update, Builders<TDocument>.Update.Unset(field));
            else
                return Builders<TDocument>.Update.Combine(update, Builders<TDocument>.Update.Set(field, value));
        }
    }
}
