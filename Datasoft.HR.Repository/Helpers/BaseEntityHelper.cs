﻿using Datasoft.HR.Core.Common;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Driver;
using System.Text.Json;
using Datasoft.HR.Repository.Helpers;

namespace Datasoft.HR.Repository.Helpers
{
    public class BaseEntityHelper
    {
        private readonly LoginUser _loginUser;
        public BaseEntityHelper(LoginUser loginUser)
        {
            _loginUser = loginUser;
        }

        public TSubEntity FillBaseEntity<TSubEntity>(TSubEntity entity, Action<TSubEntity> action = null) where TSubEntity : BaseEntity
        {
            var isBranchedEntity = typeof(IBranchedEntity).IsAssignableFrom(typeof(TSubEntity));

            if (entity == null)
                return entity;

            if (string.IsNullOrWhiteSpace(entity.Id))
            {
                entity.Id = ObjectId.GenerateNewId().ToString();
                entity.CreatedAt = entity.CreatedAt ?? DateTime.Now;
                entity.CreatedBy = _loginUser?.UserId;
            }

            entity.LastModifiedAt = entity.LastModifiedAt ?? DateTime.Now;
            entity.LastModifiedBy = _loginUser?.UserId;


            if (isBranchedEntity)
                ((IBranchedEntity)entity).BranchId = _loginUser?.BranchId;

            if (action != null)
                action.Invoke(entity);

            return entity;
        }

        public IEnumerable<TEntity> FillBaseEntities<TEntity>(IEnumerable<TEntity> entities, Action<TEntity> action = null) where TEntity : BaseEntity
        {
            return entities?.Select(entity => FillBaseEntity(entity, action));
        }

        public List<WriteModel<TEntity>> FillBaseEntities<TEntity>(List<WriteModel<TEntity>> bulkOperations, bool hardDelete = false) where TEntity : BaseEntity
        {
            var isBranchedEntity = typeof(IBranchedEntity).IsAssignableFrom(typeof(TEntity));
            var branchId = _loginUser?.BranchId;

            var mainFilter = Builders<TEntity>.Filter.Eq(x => x.IsDeleted, false);
            if (isBranchedEntity)
                mainFilter = mainFilter & Builders<TEntity>.Filter.Eq(nameof(IBranchedEntity.BranchId), branchId);

            // soft delete update defination
            var isDeletedUpdateDefination = Builders<TEntity>.Update.SetValue(x => x.IsDeleted, true)
                .SetValue(x => x.LastModifiedAt, DateTime.Now)
                .SetValue(x => x.LastModifiedBy, _loginUser?.UserId);

            for (int i = 0; i < bulkOperations.Count; i++)
            {
                var op = bulkOperations[i];

                switch (op.ModelType)
                {
                    case WriteModelType.InsertOne:
                        {
                            var entity = ((InsertOneModel<TEntity>)op).Document;
                            FillBaseEntity(entity);
                            bulkOperations[i] = new InsertOneModel<TEntity>(entity);
                        }
                        break;
                    case WriteModelType.DeleteOne:
                        {
                            var deleteOneOperation = (DeleteOneModel<TEntity>)op;
                            var filter = deleteOneOperation.Filter & mainFilter;

                            if (hardDelete)
                                bulkOperations[i] = new DeleteOneModel<TEntity>(filter)
                                {
                                    Collation = deleteOneOperation.Collation,
                                    Hint = deleteOneOperation.Hint
                                };
                            else
                                bulkOperations[i] = new UpdateOneModel<TEntity>(filter, isDeletedUpdateDefination)
                                {
                                    Collation = deleteOneOperation.Collation,
                                    Hint = deleteOneOperation.Hint
                                };
                        }
                        break;
                    case WriteModelType.DeleteMany:
                        {
                            var deleteManyOperation = (DeleteManyModel<TEntity>)op;
                            var filter = deleteManyOperation.Filter & mainFilter;

                            if (hardDelete)
                                bulkOperations[i] = new DeleteManyModel<TEntity>(filter)
                                {
                                    Collation = deleteManyOperation.Collation,
                                    Hint = deleteManyOperation.Hint
                                };
                            else
                                bulkOperations[i] = new UpdateManyModel<TEntity>(filter, isDeletedUpdateDefination)
                                {
                                    Collation = deleteManyOperation.Collation,
                                    Hint = deleteManyOperation.Hint
                                };
                        }
                        break;
                    case WriteModelType.ReplaceOne:
                        {
                            var replaceOneOperation = (ReplaceOneModel<TEntity>)op;
                            var entity = BsonSerializer.Deserialize<TEntity>(replaceOneOperation.Replacement.ToBsonDocument());
                            FillBaseEntity(entity);

                            var filter = replaceOneOperation.Filter & mainFilter;

                            bulkOperations[i] = new ReplaceOneModel<TEntity>(filter, entity)
                            {
                                IsUpsert = replaceOneOperation.IsUpsert,
                                Hint = replaceOneOperation.Hint,
                                Collation = replaceOneOperation.Collation
                            };
                        }
                        break;
                    case WriteModelType.UpdateOne:
                        {
                            var updateOneOperation = (UpdateOneModel<TEntity>)op;
                            var filter = updateOneOperation.Filter & mainFilter;

                            var update = updateOneOperation.Update
                                            .SetValue(x => x.LastModifiedAt, DateTime.Now)
                                            .SetValue(x => x.LastModifiedBy, _loginUser?.UserId)
                                            .SetOnInsert(x => x.CreatedAt, DateTime.Now)
                                            .SetOnInsert(x => x.CreatedBy, _loginUser?.UserId);

                            bulkOperations[i] = new UpdateOneModel<TEntity>(filter, update)
                            {
                                IsUpsert = updateOneOperation.IsUpsert,
                                Hint = updateOneOperation.Hint,
                                Collation = updateOneOperation.Collation,
                                ArrayFilters = updateOneOperation.ArrayFilters
                            };
                        }
                        break;
                    case WriteModelType.UpdateMany:
                        {
                            var updateManyOperation = (UpdateManyModel<TEntity>)op;
                            var filter = updateManyOperation.Filter & mainFilter;

                            var update = updateManyOperation.Update
                                            .SetValue(x => x.LastModifiedAt, DateTime.Now)
                                            .SetValue(x => x.LastModifiedBy, _loginUser?.UserId)
                                            .SetOnInsert(x => x.CreatedAt, DateTime.Now)
                                            .SetOnInsert(x => x.CreatedBy, _loginUser?.UserId);

                            bulkOperations[i] = new UpdateManyModel<TEntity>(filter, update)
                            {
                                IsUpsert = updateManyOperation.IsUpsert,
                                Hint = updateManyOperation.Hint,
                                Collation = updateManyOperation.Collation,
                                ArrayFilters = updateManyOperation.ArrayFilters
                            };
                        }
                        break;
                    default:
                        break;
                }
            }

            return bulkOperations;
        }

        public TSubEntity FillBaseSubEntity<TSubEntity>(TSubEntity entity, Action<TSubEntity> action = null) where TSubEntity : BaseSubEntity
        {
            var isBranchedEntity = typeof(IBranchedEntity).IsAssignableFrom(typeof(TSubEntity));

            if (entity == null)
                return entity;

            if (string.IsNullOrWhiteSpace(entity.SubId))
            {
                entity.SubId = ObjectId.GenerateNewId().ToString();
                entity.CreatedAt = entity.CreatedAt ?? DateTime.Now;
                entity.CreatedBy = _loginUser?.UserId;
            }

            entity.LastModifiedAt = entity.LastModifiedAt ?? DateTime.Now;
            entity.LastModifiedBy = _loginUser?.UserId;

            if (isBranchedEntity)
                ((IBranchedEntity)entity).BranchId = _loginUser?.BranchId;

            if (action != null)
                action.Invoke(entity);

            return entity;
        }

        public IEnumerable<TSubEntity> FillBaseSubEntities<TSubEntity>(IEnumerable<TSubEntity> entities, Action<TSubEntity> action = null) where TSubEntity : BaseSubEntity
        {
            return entities?.Select(entity => FillBaseSubEntity(entity, action));
        }

        public List<WriteModel<TSubEntity>> FillBaseSubEntities<TSubEntity>(List<WriteModel<TSubEntity>> bulkOperations, bool hardDelete = false) where TSubEntity : BaseSubEntity
        {
            var isBranchedEntity = typeof(IBranchedEntity).IsAssignableFrom(typeof(TSubEntity));
            var branchId = _loginUser?.BranchId;
            var branchFilterDefination = isBranchedEntity ? Builders<TSubEntity>.Filter.Eq(nameof(IBranchedEntity.BranchId), branchId) : Builders<TSubEntity>.Filter.Empty;

            // soft delete update defination
            var isDeletedUpdateDefination = Builders<TSubEntity>.Update
                .SetValue(x => x.LastModifiedAt, DateTime.Now)
                .SetValue(x => x.LastModifiedBy, _loginUser?.UserId);

            for (int i = 0; i < bulkOperations.Count; i++)
            {
                var op = bulkOperations[i];

                switch (op.ModelType)
                {
                    case WriteModelType.InsertOne:
                        {
                            //var entity = JsonConvert.DeserializeObject<TEntity>(((InsertOneModel<TEntity>)op).Document.ToJson());
                            var entity = ((InsertOneModel<TSubEntity>)op).Document;
                            FillBaseSubEntity(entity);
                            bulkOperations[i] = new InsertOneModel<TSubEntity>(entity);
                        }
                        break;
                    case WriteModelType.DeleteOne:
                        {
                            var deleteOneOperation = (DeleteOneModel<TSubEntity>)op;
                            var filter = deleteOneOperation.Filter & branchFilterDefination;

                            if (hardDelete)
                                bulkOperations[i] = new DeleteOneModel<TSubEntity>(filter)
                                {
                                    Collation = deleteOneOperation.Collation,
                                    Hint = deleteOneOperation.Hint
                                };
                            else
                                bulkOperations[i] = new UpdateOneModel<TSubEntity>(filter, isDeletedUpdateDefination)
                                {
                                    Collation = deleteOneOperation.Collation,
                                    Hint = deleteOneOperation.Hint
                                };
                        }
                        break;
                    case WriteModelType.DeleteMany:
                        {
                            var deleteManyOperation = (DeleteManyModel<TSubEntity>)op;
                            var filter = deleteManyOperation.Filter & branchFilterDefination;

                            if (hardDelete)
                                bulkOperations[i] = new DeleteManyModel<TSubEntity>(filter)
                                {
                                    Collation = deleteManyOperation.Collation,
                                    Hint = deleteManyOperation.Hint
                                };
                            else
                                bulkOperations[i] = new UpdateManyModel<TSubEntity>(filter, isDeletedUpdateDefination)
                                {
                                    Collation = deleteManyOperation.Collation,
                                    Hint = deleteManyOperation.Hint
                                };
                        }
                        break;
                    case WriteModelType.ReplaceOne:
                        {
                            var replaceOneOperation = (ReplaceOneModel<TSubEntity>)op;
                            var entity = BsonSerializer.Deserialize<TSubEntity>(replaceOneOperation.Replacement.ToBsonDocument());
                            FillBaseSubEntity(entity);

                            var filter = replaceOneOperation.Filter & branchFilterDefination;

                            bulkOperations[i] = new ReplaceOneModel<TSubEntity>(filter, entity)
                            {
                                IsUpsert = replaceOneOperation.IsUpsert,
                                Hint = replaceOneOperation.Hint,
                                Collation = replaceOneOperation.Collation
                            };
                        }
                        break;
                    case WriteModelType.UpdateOne:
                        {
                            var updateOneOperation = (UpdateOneModel<TSubEntity>)op;
                            var filter = updateOneOperation.Filter & branchFilterDefination;

                            var update = updateOneOperation.Update
                                            .SetValue(x => x.LastModifiedAt, DateTime.Now)
                                            .SetValue(x => x.LastModifiedBy, _loginUser?.UserId)
                                            .SetOnInsert(x => x.CreatedAt, DateTime.Now)
                                            .SetOnInsert(x => x.CreatedBy, _loginUser?.UserId);

                            bulkOperations[i] = new UpdateOneModel<TSubEntity>(filter, update)
                            {
                                IsUpsert = updateOneOperation.IsUpsert,
                                Hint = updateOneOperation.Hint,
                                Collation = updateOneOperation.Collation,
                                ArrayFilters = updateOneOperation.ArrayFilters
                            };
                        }
                        break;
                    case WriteModelType.UpdateMany:
                        {
                            var updateManyOperation = (UpdateManyModel<TSubEntity>)op;
                            var filter = updateManyOperation.Filter & branchFilterDefination;

                            var update = updateManyOperation.Update
                                            .SetValue(x => x.LastModifiedAt, DateTime.Now)
                                            .SetValue(x => x.LastModifiedBy, _loginUser?.UserId)
                                            .SetOnInsert(x => x.CreatedAt, DateTime.Now)
                                            .SetOnInsert(x => x.CreatedBy, _loginUser?.UserId);

                            bulkOperations[i] = new UpdateManyModel<TSubEntity>(filter, update)
                            {
                                IsUpsert = updateManyOperation.IsUpsert,
                                Hint = updateManyOperation.Hint,
                                Collation = updateManyOperation.Collation,
                                ArrayFilters = updateManyOperation.ArrayFilters
                            };
                        }
                        break;
                    default:
                        break;
                }
            }

            return bulkOperations;
        }
    }
}
