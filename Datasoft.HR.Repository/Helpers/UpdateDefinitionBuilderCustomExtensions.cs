﻿using MongoDB.Driver;
using System.Linq.Expressions;

namespace Datasoft.HR.Repository.Helpers
{
    public static class UpdateDefinitionBuilderCustomExtensions
    {
        /// <summary>
        /// property değerini set eder, değer null ise property'yi db'den siler
        /// </summary>
        /// <typeparam name="TField"></typeparam>
        /// <typeparam name="TDocument"></typeparam>
        /// <param name="builder"></param>
        /// <param name="field"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public static UpdateDefinition<TDocument> SetValue<TField, TDocument>(this UpdateDefinitionBuilder<TDocument> builder, Expression<Func<TDocument, TField>> field, TField value)
        {
            if (value == null)
                return builder.Unset(new ExpressionFieldDefinition<TDocument>(field));
            else
                return builder.Set(field, value);
        }

        /// <summary>
        /// property değerini set eder, değer null ise property'yi db'den siler
        /// </summary>
        /// <typeparam name="TField"></typeparam>
        /// <typeparam name="TDocument"></typeparam>
        /// <param name="builder"></param>
        /// <param name="field"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public static UpdateDefinition<TDocument> SetValue<TField, TDocument>(this UpdateDefinitionBuilder<TDocument> builder, FieldDefinition<TDocument, TField> field, TField value)
        {
            if (value == null)
                return builder.Unset(field);
            else
                return builder.Set(field, value);
        }
    }
}
