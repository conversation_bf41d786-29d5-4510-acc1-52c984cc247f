﻿using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.DTO.Company
{
    public class CompanyBranchSearchResponseDto
    {
        public long CompanyId { get; set; }

        public string CompanyName { get; set; }

        public string CompanyCode { get; set; }

        public string IdentityNumber { get; set; }

        public bool HaveBranch { get; set; }
        public long? BranchId { get; set; }
        public string BranchName { get; set; }
        public string BranchCode { get; set; }

        /// <summary>
        /// Şirket Tiplerimiz
        /// </summary>
        public PreAccountancyCompanyType Type { get; set; }
    }
}
