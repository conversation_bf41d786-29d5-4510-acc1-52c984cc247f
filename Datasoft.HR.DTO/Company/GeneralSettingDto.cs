﻿using Datasoft.HR.Core.Entities.Common;
using Datasoft.HR.Core.Entities.Company;
using Datasoft.HR.Core.Entities.Parameters;
using Datasoft.HR.Core.Entities.Parameters.Regions;
using Datasoft.HR.Core.Entities.Parameters.Resources;
using Datasoft.HR.Core.Enums;
using Datasoft.HR.DTO.Parameters.Units;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.DTO.Company
{

    /// <summary>
    /// Şirket Genel Ayarları
    /// </summary>
    public class GeneralSettingDto
    {
        /// <summary>
        /// Şirket adı
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// Şirket Kodu
        /// </summary>
        public string CompanyCode { get; set; }

        /// <summary>
        /// Vergi Mevzuatı Ülkesi
        /// </summary>
        public Country TaxLegislationCountry { get; set; }

        /// <summary>
        /// Dil
        /// </summary>
        public Language Language { get; set; }

        /// <summary>
        /// Hukiki yapı
        /// </summary>
        public LegalStructure LegalStructure { get; set; }

        /// <summary>
        /// Şirket türü
        /// </summary>
        public CompanyType CompanyType { get; set; }

        /// <summary>
        /// Ortaklık türü
        /// </summary>
        public PartnerType PartnerType { get; set; }

        /// <summary>
        /// TCKN
        /// </summary>
        public string IdentityNumber { get; set; }

        /// <summary>
        /// VKN No
        /// </summary>
        public string TaxNumber { get; set; }

        /// <summary>
        /// Ünvanı
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Kişisel Ünvan -> Hukiki yapı gerçek ise
        /// </summary>
        public string PersonalTitle { get; set; }

        /// <summary>
        /// Adı -> Hukiki yapı gerçek ise
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Soyadı -> -> Hukiki yapı gerçek ise
        /// </summary>
        public string Surname { get; set; }

        /// <summary>
        /// Vergi Dairesi
        /// </summary>
        public TaxOffice TaxOffice { get; set; }
        
        /// <summary>
        /// Mernis No
        /// </summary>
        public string MernisNumber { get; set; }
        /*
        /// <summary>
        /// Şirket genel merkezi
        /// </summary>
        public string Headquarters { get; set; }

        /// <summary>
        /// Bağlı olduğu kurum
        /// </summary>
        public string AffiliatedInstitution { get; set; }

        /// <summary>
        /// Kuruluş bilgileri
        /// </summary>
        public EstablishmentInformation EstablishmentInformation { get; set; }

        /// <summary>
        /// Yapılan esas iş
        /// </summary>
        public ActivityCode ActivityCode { get; set; }

        /// <summary>
        /// Ticari sicil no
        /// </summary>
        public string TradeRegisterNumber { get; set; }

        /// <summary>
        /// Ticari sicil müdürlüğü
        /// </summary>
        public CommercialRegistryOffice CommercialRegistryOffice { get; set; }
        */
        /// <summary>
        /// Logo
        /// </summary>
        public string Logo { get; set; }

        /// <summary>
        /// Kaşe
        /// </summary>
        public string Stamp { get; set; }

        /// <summary>
        /// Adres
        /// </summary>
        public Address Address { get; set; }

        /// <summary>
        /// İletişim bilgisi
        /// </summary>
        public List<Contact> Contacts { get; set; }
        /*
        /// <summary>
        /// Para Birimleri
        /// </summary>
        public IEnumerable<CurrencyDto> Currencies { get; set; }
        */
        /// <summary>
        /// SMMM bilgileri
        /// </summary>
        public SMMMInformation SMMMInformation { get; set; }
        /*
        /// <summary>
        /// YMM Bilgileri
        /// </summary>
        public YMMInformation YMMInformation { get; set; }
        */
        /// <summary>
        /// Dönem Bilgisi -> Sadece bilgi db de tutulmaz servis yolu ile doldurulur
        /// </summary>
        public PeriodInfoDto PeriodInformation { get; set; }
        /*
        /// <summary>
        /// Şubeli mi ? -> Sadece bilgi db de tutulmaz servis yolu ile doldurulur
        /// </summary>
        public bool HaveBranch { get; set; }

        /// <summary>
        /// Şube sayısı -> Sadece bilgi db de tutulmaz servis yolu ile doldurulur
        /// </summary>
        public int BranchCount { get; set; }
        
        /// <summary>
        /// Kullanıcının sahip olduğu Turmob anahtarı
        /// </summary>
        public string TurmobCustomerKey { get; set; }
        

        /// <summary>
        /// Parasal değerleri ayırmak için gereken bilgiler.
        /// </summary>
        public DecimalSeparators Separators { get; set; }
        */
        /// <summary>
        /// SGK Bilgileri
        /// </summary>
        public SgkInformation SgkInformation { get; set; }
    }
}
