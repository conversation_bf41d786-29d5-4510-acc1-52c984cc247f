﻿using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.DTO.Company
{
    public class BranchInfoDto
    {
        public string CompanyName { get; set; }
        public PreAccountancyCompanyType CompanyType { get; set; }

        /// <summary>
        /// Şube No
        /// </summary>
        public string BranchCode { get; set; }

        /// <summary>
        /// Şube Kodu
        /// </summary>
        public string BranchEditableCode { get; set; }

        /// <summary>
        /// Şube adı
        /// </summary>
        public string BranchName { get; set; }

        public int TotalBranchCount { get; set; }
    }
}
