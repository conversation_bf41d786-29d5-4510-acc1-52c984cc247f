﻿using Datasoft.HR.Core.Entities.Branch;
using Datasoft.HR.Core.Entities.Company;
using Datasoft.HR.Core.Entities.Common;

namespace Datasoft.HR.DTO.Branch
{
    /// <summary>
    /// Şube DTO
    /// </summary>
    public class BranchDto
    {
        public string Id { get; set; }

        /// <summary>
        /// Şube genel bilgileri
        /// </summary>
        public BranchGeneralInformation GeneralInformation { get; set; }

        /// <summary>
        /// Şube SGK Bilgileri
        /// </summary>
        public SgkInformation SgkInformation { get; set; }

        /// <summary>
        /// Adres
        /// </summary>
        public Address Address { get; set; }

        /// <summary>
        /// SMMM bilgileri
        /// </summary>
        public SMMMInformation SMMMInformation { get; set; }

        /// <summary>
        /// Şubenin ek bilgileri
        /// </summary>
        public BranchAdditionalInformation AdditionalInformation { get; set; }

        /// <summary>
        /// Logo
        /// </summary>
        public string? Logo { get; set; }

        /// <summary>
        /// Kaşe
        /// </summary>
        public string? Stamp { get; set; }

        /// <summary>
        /// Etiketler
        /// </summary>
        public List<string> Tags { get; set; }
    }
}
