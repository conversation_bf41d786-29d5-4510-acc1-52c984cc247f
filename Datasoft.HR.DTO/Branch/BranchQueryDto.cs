﻿using Datasoft.HR.Core.Entities.Parameters.Regions;
using Datasoft.HR.Core.Enums;

namespace Datasoft.HR.DTO.Branch
{
    /// <summary>
    /// Şube arama kriterlerini temsil eder.
    /// </summary>
    public class BranchQueryDto
    {
        /// <summary>
        /// Etiket listesi
        /// </summary>
        public List<string>? Tags { get; set; }

        /// <summary>
        /// Şube kodunun başladığı değer
        /// </summary>
        public string? BranchCodeStart { get; set; }

        /// <summary>
        /// Şube kodunun bittiği değer
        /// </summary>
        public string? BranchCodeEnd { get; set; }

        /// <summary>
        /// Şube adı (birden fazla girilebilir)
        /// </summary>
        public List<string>? BranchNames { get; set; }

        /// <summary>
        /// Şube türü
        /// </summary>
        public BranchType? BranchType { get; set; }

        /// <summary>
        /// Mersis numarası (birden fazla girilebilir)
        /// </summary>
        public List<string>? MersisNumbers { get; set; }

        /// <summary>
        /// Ülke bilgisi
        /// </summary>
        public Country? Country { get; set; }

        /// <summary>
        /// Şehir bilgisi
        /// </summary>
        public City? City { get; set; }

        /// <summary>
        /// İlçe bilgisi
        /// </summary>
        public County? County { get; set; }

        /// <summary>
        /// Aktiflik durumu (birden fazla seçilebilir)
        /// </summary>
        public List<bool>? IsActive { get; set; }
    }
}
