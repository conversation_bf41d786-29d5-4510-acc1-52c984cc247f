﻿namespace Datasoft.HR.DTO.Common.Results
{

    public class ErrorModel
    {
        public List<string>? PropertyNames { get; set; }
        public Dictionary<string, object>? AttemptedValues { get; set; }
        public string? ErrorCode { get; set; }
        public string? ErrorMessage { get; set; }
        public object? ExpectedValue { get; set; }
        public Dictionary<string, object>? MessageContext { get; set; }
        public Dictionary<string, object>? IndexValues
        {
            get
            {
                return PropertyNames
                    ?.Select(GetIndexValues)
                    ?.Where(x => x != null)
                    ?.ToDictionary(x => x.Value.Key, x => x.Value.Value);
            }
        }

        private KeyValuePair<string, object>? GetIndexValues(string propertyName)
        {
            if (string.IsNullOrWhiteSpace(propertyName))
                return null;

            var indexValues = propertyName.Split(".").Select(s =>
            {
                var startIndex = s.IndexOf("[");
                var endIndex = s.IndexOf("]");
                var propertyIndex = -1;
                if (startIndex > -1)
                {
                    var indexValue = s.Substring(startIndex + 1, endIndex - startIndex - 1);
                    propertyIndex = int.Parse(indexValue);
                }
                return new
                {
                    PropertyName = startIndex > -1 ? s.Substring(0, startIndex) : s,
                    HasIndex = propertyIndex > -1,
                    PropertyIndex = propertyIndex,
                };
            }).ToList();

            return new KeyValuePair<string, object>(propertyName, indexValues);
        }
    }
}
