﻿using Datasoft.HR.Core.Common;

namespace Datasoft.HR.DTO.Common.Results
{
    public class Result
    {
        public bool IsSuccess { get; set; }
        public List<ErrorModel>? Errors { get; set; }
        public PagingOutput? PagingOutput { get; set; }

        public static Result CreateSuccessResult(PagingOutput? pagingOutput = null)
        {
            return new Result
            {
                IsSuccess = true,
                PagingOutput = pagingOutput
            };
        }

        public static Result CreateErrorResult(string errorCode, string? errorMessage = null)
        {
            return new Result
            {
                IsSuccess = false,
                Errors = new List<ErrorModel>()
                {
                    new ErrorModel
                    {
                        ErrorCode = errorCode,
                        ErrorMessage = errorMessage
                    }
                }
            };
        }

        public static Result CreateErrorResult(List<ErrorModel> errors)
        {
            return new Result
            {
                IsSuccess = false,
                Errors = errors
            };
        }
    }

    public class Result<T> : Result
    {
        public T? Data { get; set; }

        public static Result<T> CreateSuccessResult<T>(T? data, PagingOutput? pagingOutput = null)
        {
            return new Result<T>
            {
                Data = data,
                IsSuccess = true,
                PagingOutput = pagingOutput
            };
        }
        public static Result<T> CreateSuccessResult<T>(PagedData<T>? pagingOutput = null)
        {
            return new Result<T>
            {
                Data = pagingOutput.Data,
                IsSuccess = true,
                PagingOutput = pagingOutput == null ? null : new PagingOutput
                {
                    PageIndex = pagingOutput.PageIndex,
                    PageCount = pagingOutput.PageCount,
                    RecordCount = pagingOutput.RecordCount
                }
            };
        }
        public static Result<T> CreateErrorResult<T>(string errorCode, string? errorMessage = null)
        {
            return new Result<T>
            {
                Data = default,
                IsSuccess = false,
                Errors = new List<ErrorModel>()
                {
                    new ErrorModel
                    {
                        ErrorCode = errorCode,
                        ErrorMessage = errorMessage
                    }
                }
            };
        }

        public static new Result<T> CreateErrorResult(string errorCode, string? errorMessage = null)
        {
            return new Result<T>
            {
                Data = default,
                IsSuccess = false,
                Errors = new List<ErrorModel>()
                {
                    new ErrorModel
                    {
                        ErrorCode = errorCode,
                        ErrorMessage = errorMessage
                    }
                }
            };
        }

        public static Result<T> CreateErrorResult<T>(List<ErrorModel> errors)
        {
            return new Result<T>
            {
                Data = default,
                IsSuccess = false,
                Errors = errors
            };
        }
        public static Result<T> Cast<C>(Result<C> result)
        {
            return new Result<T>
            {
                Errors = result.Errors,
                PagingOutput = result.PagingOutput,
                IsSuccess = result.IsSuccess
            };
        }

        public static Result<T> MapFrom(Result result)
        {
            return new Result<T>
            {
                Data = default,
                IsSuccess = result.IsSuccess,
                Errors = result.Errors
            };
        }
    }
}
