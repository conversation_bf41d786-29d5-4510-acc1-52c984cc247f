﻿using System.ComponentModel.DataAnnotations;

namespace Datasoft.HR.Core.Common
{
    public class PagingInput
    {
        public bool EnablePaging { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
    }

    public class PagingInput<T> : IPagingInput
    {
        public long? TenantId { get; set; }

        [Required(AllowEmptyStrings = true)]
        public T Data { get; set; }
        public PagingInput PagingOptions { get; set; }
        public List<OrderInput> OrderInputs { get; set; }
    }
}
