﻿using Datasoft.HR.Core.Enums;
using System.Globalization;
using System.Security.Claims;

namespace Datasoft.HR.Core.Common
{
    public class LoginUser
    {
        private readonly ClaimsIdentity _identity;

        private IEnumerable<Claim> _claims => _identity?.Claims;

        public LoginUser(ClaimsIdentity identity)
        {
            _identity = identity;
            if (_claims == null)
                return;

            ServerId = ParseLong(_claims?.FirstOrDefault(x => x.Type == "sid")?.Value).GetValueOrDefault();
            CompanyId = ParseLong(_claims?.FirstOrDefault(x => x.Type == "cid")?.Value).GetValueOrDefault();
            CompanyName = _claims?.FirstOrDefault(x => x.Type == "companyName")?.Value;
            CompanyCode = _claims?.FirstOrDefault(x => x.Type == "companyCode")?.Value;

            UserType = Enum.Parse<UserType>(_claims?.FirstOrDefault(x => x.Type == "utype")?.Value);
            UserId = ParseLong(_claims?.FirstOrDefault(x => x.Type == "uid")?.Value).GetValueOrDefault();
            Name = _claims?.FirstOrDefault(x => x.Type == ClaimTypes.Name)?.Value;
            SurName = _claims?.FirstOrDefault(x => x.Type == ClaimTypes.GivenName)?.Value;
            Email = _claims?.FirstOrDefault(x => x.Type == ClaimTypes.Email)?.Value;

            BranchId = ParseLong(_claims?.FirstOrDefault(x => x.Type == "branch")?.Value);
            BranchName = _claims?.FirstOrDefault(x => x.Type == "branchName")?.Value;
            HaveBranch = _claims?.FirstOrDefault(x => x.Type == "haveBranch")?.Value == bool.TrueString;

            IsOwner = _claims?.FirstOrDefault(x => x.Type == "isOwner")?.Value == bool.TrueString;
            IsSystemUser = _claims?.FirstOrDefault(x => x.Type == "isSystem")?.Value == bool.TrueString;
            IsAuthenticated = _identity?.IsAuthenticated ?? false;

            PeriodId = ParseLong(_claims?.FirstOrDefault(x => x.Type == "periodid")?.Value).GetValueOrDefault();
            PeriodStart = ParseDate(_claims?.FirstOrDefault(x => x.Type == "periodStartDate")?.Value);
            PeriodEnd = ParseDate(_claims?.FirstOrDefault(x => x.Type == "periodEndDate")?.Value);
        }

        public UserType UserType { get; set; }
        public long UserId { get; set; }
        public string Name { get; set; }
        public string SurName { get; set; }
        public string Email { get; set; }

        public long ServerId { get; set; }
        public long CompanyId { get; set; }
        public string CompanyName { get; set; }
        public string CompanyCode { get; set; }

        public long? BranchId { get; set; }
        public string BranchName { get; set; }
        public bool HaveBranch { get; set; }

        public bool IsOwner { get; set; }
        public bool IsSystemUser { get; set; }
        public bool IsAuthenticated { get; set; }

        public long PeriodId { get; set; }
        public DateTime PeriodStart { get; set; }
        public DateTime PeriodEnd { get; set; }

        private static long? ParseLong(string value)
        {
            if (!string.IsNullOrWhiteSpace(value) && long.TryParse(value, out long result))
                return result;

            return default;
        }

        private static DateTime ParseDate(string value)
        {
            if (!string.IsNullOrWhiteSpace(value))
            {
                IFormatProvider fp = Thread.CurrentThread.CurrentCulture;
                if (DateTime.TryParseExact(value, "dd.MM.yyyy", fp, DateTimeStyles.None, out DateTime date))
                    return date;
            }

            return default;
        }
    }
}
