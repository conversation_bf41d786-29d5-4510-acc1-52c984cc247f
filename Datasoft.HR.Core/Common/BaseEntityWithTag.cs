﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Reflection;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Core.Common
{
    public abstract class BaseEntityWithTag : BaseEntity
    {
        protected BaseEntityWithTag()
        {
            var type = this.GetType();

            var attribute = (TagSearchPropertiesAttribute?)type.GetCustomAttribute(typeof(TagSearchPropertiesAttribute), true);
            if (attribute == null)
                throw new InvalidOperationException(type.Name + " class'ı " + nameof(TagSearchPropertiesAttribute) + " adlı attribute sahip olmak zorundadır");

            if (attribute.Names == null || attribute.Names.Count == 0)
                throw new InvalidOperationException(type.Name + " class'ı içinde bulunan" + nameof(TagSearchPropertiesAttribute) + " adlı attribute değer almak zorunda.");

            foreach (var name in attribute.Names)
            {
                if (type.GetProperty(name) == null)
                    throw new InvalidOperationException(type.Name + " class'ı " + nameof(TagSearchPropertiesAttribute) + " adlı attribute e verilen " + name + " değeri class içinde mevcut değil");
            }
        }

        public string? TagsJSON { get; set; }

        /// <summary>
        /// Bu alan db'de yok
        /// </summary>
        [NotMapped]
        public List<string>? Tags
        {
            get => string.IsNullOrWhiteSpace(TagsJSON) ? new List<string>() : JsonConvert.DeserializeObject<List<string>>(TagsJSON);
            set => TagsJSON = JsonConvert.SerializeObject(value);
        }
    }
    [AttributeUsage(AttributeTargets.Class)]
    public class TagSearchPropertiesAttribute : Attribute
    {
        public readonly IReadOnlyList<string> Names;
        public TagSearchPropertiesAttribute(params string[] properties)
        {
            Names = properties;
        }
    }
}
