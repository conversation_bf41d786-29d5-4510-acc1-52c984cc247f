﻿using Datasoft.HR.Core.Entities.Parameters.Resources;
using Datasoft.HR.Core.Interfaces;

namespace Datasoft.HR.Core.Entities.Parameters.Regions
{
    public class County : ITranslation
    {
        public long Id { get; set; }

        /// <summary>
        /// Kod
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Hal Künye Ko<PERSON>
        /// </summary>
        public string HalCode { get; set; }

        /// <summary>
        /// İlçe Adı
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Şehir Id
        /// </summary>
        public long CityId { get; set; }

        /// <summary>
        /// Şehir Adı
        /// </summary>
        public string CityName { get; set; }

        public string DeclarationCode { get; set; }

        /// <summary>
        /// Çeviriler
        /// </summary>
        public IEnumerable<LanguageTranslation> Translations { get; set; }
    }
}
