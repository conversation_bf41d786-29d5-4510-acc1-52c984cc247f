﻿using Datasoft.HR.Core.Entities.Parameters.Resources;
using Datasoft.HR.Core.Interfaces;

namespace Datasoft.HR.Core.Entities.Parameters.Regions
{
    public class State : ITranslation
    {
        public long Id { get; set; }

        /// <summary>
        /// Eyalet Adı
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Ülke Id
        /// </summary>
        public long CountryId { get; set; }

        /// <summary>
        /// Çeviriler
        /// </summary>
        public IEnumerable<LanguageTranslation> Translations { get; set; }
    }
}
