﻿using Datasoft.HR.Core.Entities.Parameters.Resources;
using Datasoft.HR.Core.Interfaces;

namespace Datasoft.HR.Core.Entities.Parameters.Regions
{
    public class Country : ITranslation
    {
        public long Id { get; set; }

        /// <summary>
        /// <PERSON><PERSON><PERSON>
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Iso Kodu - Örn : TR
        /// </summary>
        public string Iso { get; set; }

        /// <summary>
        /// <PERSON><PERSON><PERSON> - <PERSON> : TUR
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// tr-Tr
        /// </summary>
        public string DefaultCulture { get; set; }

        /// <summary>
        /// Telefon Kodu - Örn : +90
        /// </summary>
        public string PhoneCode { get; set; }

        /// <summary>
        /// Sayısal kod - Örn : 052
        /// </summary>
        public string NumberCode { get; set; }

        /// <summary>
        /// Eyaleti Varmı
        /// </summary>
        public bool HaveState { get; set; }

        /// <summary>
        /// Çeviriler
        /// </summary>
        public IEnumerable<LanguageTranslation> Translations { get; set; }
    }

}
