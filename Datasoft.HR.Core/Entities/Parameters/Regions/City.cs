﻿using Datasoft.HR.Core.Entities.Parameters.Resources;
using Datasoft.HR.Core.Interfaces;

namespace Datasoft.HR.Core.Entities.Parameters.Regions
{
    public class City : ITranslation
    {
        public long Id { get; set; }

        /// <summary>
        /// Kod
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Şehir Adı
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Eyalet Id
        /// </summary>
        public long? StateId { get; set; }

        /// <summary>
        /// Ülke Id
        /// </summary>
        public long? CountryId { get; set; }

        /// <summary>
        /// Çeviriler
        /// </summary>
        public IEnumerable<LanguageTranslation> Translations { get; set; }
    }
}
