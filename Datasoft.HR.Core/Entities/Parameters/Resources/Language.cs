﻿using Datasoft.HR.Core.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Core.Entities.Parameters.Resources
{
    public class Language : ITranslation
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public bool IsDefault { get; set; }
        public IEnumerable<LanguageTranslation> Translations { get; set; }
    }
}
