﻿using Datasoft.HR.Core.Entities.Parameters.Resources;
using Datasoft.HR.Core.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Core.Entities.Parameters.Units
{
    public class Currency : ITranslation
    {
        public long Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool International { get; set; }

        /// <summary>
        /// Fiyat V.S.B.S.	
        /// </summary>
        public short PriceNumberOfDigits { get; set; }

        /// <summary>
        /// Tutar V.S.B.S.
        /// </summary>
        public short AmountNumberOfDigits { get; set; }

        public bool IsDefault { get; set; }
        public IEnumerable<LanguageTranslation> Translations { get; set; }
    }
}
