﻿using Datasoft.HR.Core.Entities.Parameters;
using Datasoft.HR.Core.Enums;

namespace Datasoft.HR.Core.Entities.Branch
{
    /// <summary>
    /// Şubenin genel bilgilerinin tutulduğu sınıftır.
    /// </summary>
    public class BranchGeneralInformation
    {
        /// <summary>
        /// Şube kodu
        /// </summary>
        public string BranchCode { get; set; }

        /// <summary>
        /// Şube adı
        /// </summary>
        public string BranchName { get; set; }

        /// <summary>
        /// Mükellefiyetli ve Mükellefiyetsiz olmak üzere 2 seçenek vardır. 
        /// </summary>
        public BranchType BranchType { get; set; }

        /// <summary>
        /// Şube Türü = Mükellefiyetsiz olduğunda oluşur ve zorunlu alandır. 
        /// Şube kartındaki mükellefiyetli ve merkez şubelerden seçilir. 
        /// </summary>
        public Branch? AuthorizedBranch { get; set; }

        /// <summary>
        /// MersisNo
        /// </summary>
        public string? MersisNo { get; set; }

        /// <summary>
        /// Vergi dairesi
        /// </summary>
        public TaxOffice TaxOffice { get; set; }
    }
}
