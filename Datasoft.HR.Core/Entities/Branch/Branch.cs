﻿using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Common;
using Datasoft.HR.Core.Entities.Company;

namespace Datasoft.HR.Core.Entities.Branch
{
    /// <summary>
    /// Şube bilgileri
    /// </summary>
    [TagSearchProperties(nameof(Id))]
    public class Branch : BaseEntityWithTag
    {
        /// <summary>
        /// Şube genel bilgileri
        /// </summary>
        public BranchGeneralInformation GeneralInformation { get; set; }      
        /// <summary>
        /// Şube SGK Bilgileri
        /// </summary>
        public SgkInformation SgkInformation { get; set; }
        /// <summary>
        /// Adres
        /// </summary>
        public Address Address { get; set; }
        /// <summary>
        /// SMMM bilgileri
        /// </summary>
        public SMMMInformation SMMMInformation { get; set; }
        /// <summary>
        /// Şubenin ek bilgileri
        /// </summary>
        public BranchAdditionalInformation AdditionalInformation { get; set; }
        /// <summary>
        /// Logo
        /// </summary>
        /// 
        public string? Logo { get; set; }

        /// <summary>
        /// Kaşe
        /// </summary>
        public string? Stamp { get; set; }
    }
}
