﻿using Datasoft.HR.Core.Entities.Parameters;

namespace Datasoft.HR.Core.Entities.Company
{
    public class SgkInformation
    {
        /// <summary>
        /// Şirketin resmi olarak faaliyete başladığı tarih.
        /// </summary>
        public DateTime? CommencementDate { get; set; }

        /// <summary>
        /// Şirketin faaliyetinin sona erip ermediğini belirtir.
        /// </summary>
        public bool? IsDiscontinued { get; set; }

        /// <summary>
        /// Şirket faaliyetinin sona erdiği tarih. IsDiscontinued true ise zorunludur.
        /// </summary>
        public DateTime? DiscontinuationDate { get; set; }

        /// <summary>
        /// SGK tarafından verilen 4 haneli işyeri kodu.
        /// </summary>
        public string WorkplaceRegistrationCode { get; set; }

        /// <summary>
        /// Şirketin bağlı bulunduğu SGK müdürlüğü adı.
        /// </summary>
        public string SocialSecurityOfficeName { get; set; }

        /// <summary>
        /// SGK sicil numarası. Format: 1.23456.78.91.2345678.912.34
        /// </summary>
        public string SocialSecurityNumber { get; set; }

        /// <summary>
        /// SGK tarafından verilen ve bilgilerin doğruluğunu kontrol etmek için kullanılan 2 haneli numara.
        /// </summary>
        public string VerificationCode { get; set; }

        /// <summary>
        /// Taşeron firmaya ait SGK dosya numarası. (3 karakterli numerik)
        /// </summary>
        public string SubcontractorCompanyNumber { get; set; }

        /// <summary>
        /// İşyerinin faaliyet gösterdiği işkolu kodu (1–9).
        /// </summary>
        public int? OccupationalBranchCode { get; set; }

        /// <summary>
        /// İşyerinde esas olarak yapılan işin tanımı (faaliyet kodu).
        /// </summary>
        public ActivityCode ActivityCode { get; set; }
    }

}
