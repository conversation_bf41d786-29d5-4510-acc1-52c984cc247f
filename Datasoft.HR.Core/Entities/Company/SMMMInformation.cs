﻿using Datasoft.HR.Core.Entities.Common;

namespace Datasoft.HR.Core.Entities.Company
{
    public class SMMMInformation
    {
        /// <summary>
        /// Ad
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Soyad
        /// </summary>
        public string Surname { get; set; }

        /// <summary>
        /// Ünvan
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Ünvan
        /// </summary>
        public string PersonalTitle { get; set; }

        /// <summary>
        /// VKN No
        /// </summary>
        public string TaxNumber { get; set; }

        /// <summary>
        /// TCKN
        /// </summary>
        public string IdentityNumber { get; set; }

        /// <summary>
        /// Oda sicil no
        /// </summary>
        public string RegistrationNumber { get; set; }

        /// <summary>
        /// Kaşe
        /// </summary>
        public string StampNumber { get; set; }

        /// <summary>
        /// Ek a<PERSON>
        /// </summary>
        public string Annotation { get; set; }

        /// <summary>
        /// Sözleşme tarihi
        /// </summary>
        public DateTime ContractDate { get; set; }

        /// <summary>
        /// Sözleşme Numarası
        /// </summary>
        public string ContractNumber { get; set; }

        /// <summary>
        /// Sözleşme Çeşidi
        /// </summary>
        public string ContractType { get; set; }

        /// <summary>
        /// Adres
        /// </summary>
        public Address Address { get; set; }

        /// <summary>
        /// İletişim
        /// </summary>
        public Contact Contact { get; set; }
    }
}
