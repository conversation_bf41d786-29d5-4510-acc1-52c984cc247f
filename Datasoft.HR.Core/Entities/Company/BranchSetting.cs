﻿using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Common;
using Datasoft.HR.Core.Entities.Parameters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Core.Entities.Company
{
    /// <summary>
    /// Şube Genel Ayarları
    /// </summary>
    public class BranchSetting : BaseEntity, IBranchedEntity, IDataTransferEntity
    {
        /// <summary>
        /// Şube No
        /// </summary>
        public string BranchNo { get; set; }

        /// <summary>
        /// Şube Kodu
        /// </summary>
        public string BranchEditableCode { get; set; }

        /// <summary>
        /// Şube adı
        /// </summary>
        public string BranchName { get; set; }

        /// <summary>
        /// Bağlı olduğu kurum
        /// </summary>
        public string AffiliatedInstitution { get; set; }

        /// <summary>
        /// Kuruluş bilgileri
        /// </summary>
        public EstablishmentInformation EstablishmentInformation { get; set; }

        /// <summary>
        /// Yapılan esas iş
        /// </summary>
        public ActivityCode ActivityCode { get; set; }

        /// <summary>
        /// Ticari sicil no
        /// </summary>
        public string TradeRegisterNumber { get; set; }

        /// <summary>
        /// SGK İşyeri Sicil No
        /// </summary>
        public string SGKRegistrationNumber { get; set; }

        /// <summary>
        /// Ticari sicil müdürlüğü
        /// </summary>
        public CommercialRegistryOffice CommercialRegistryOffice { get; set; }

        /// <summary>
        /// Logo
        /// </summary>
        public string Logo { get; set; }

        /// <summary>
        /// Kaşe
        /// </summary>
        public string Stamp { get; set; }

        /// <summary>
        /// Adres
        /// </summary>
        public Address Address { get; set; }

        /// <summary>
        /// İletişim bilgisi
        /// </summary>
        public List<Contact> Contacts { get; set; }

        /// <summary>
        /// Şube Id
        /// </summary>
        public long? BranchId { get; set; }

        /// <summary>
        /// SMMM bilgileri
        /// </summary>
        public SMMMInformation SMMMInformation { get; set; }
        /// <summary>
        /// YMM Bilgileri
        /// </summary>
        public YMMInformation YMMInformation { get; set; }

        /// <summary>
        /// Firma merkezi
        /// </summary>
        public string Headquarters { get; set; }
    }

}
