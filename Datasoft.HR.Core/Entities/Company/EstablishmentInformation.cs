﻿using Datasoft.HR.Core.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Core.Entities.Company
{
    /// <summary>
    /// Kuruluş bilgileri
    /// </summary>
    public class EstablishmentInformation
    {
        /// <summary>
        /// Kuruluş numarası
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// Kuruluş tarihi
        /// </summary>
        public DateTime? Date { get; set; }

        /// <summary>
        /// Kuruluş tipi
        /// </summary>
        public EstablishmentType? EstablishmentType { get; set; }

        /// <summary>
        /// Terk
        /// </summary>
        public bool Leave { get; set; }

        /// <summary>
        /// Terk tarihi
        /// </summary>
        public DateTime? LeaveDate { get; set; }

        /// <summary>
        /// Terk sebebi
        /// </summary>
        public LeaveReason? LeaveReason { get; set; }
    }
}
