﻿namespace Datasoft.HR.Core.Entities.Common
{
    public class Contact
    {
        public long? ReferenceId { get; set; }
        public long? ReservedContactId { get; set; }
        public long? Id { get; set; }

        /// <summary>
        /// V<PERSON><PERSON><PERSON>lan mı ?
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// Ünvan
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Adı
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Soyadı
        /// </summary>
        public string Surname { get; set; }

        /// <summary>
        /// Sabit telefon ülke kodu
        /// </summary>
        public string PhoneCode { get; set; }

        /// <summary>
        /// Sabit telefon no
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// Email
        /// </summary>

        public string Email { get; set; }

        /// <summary>
        /// İnternet adresi
        /// </summary>
        public string WebsiteUri { get; set; }

        /// <summary>
        /// Cep telefonu ülke kodu
        /// </summary>
        public string MobilePhoneCode { get; set; }

        /// <summary>
        /// Cep telefonu no
        /// </summary>
        public string MobilePhone { get; set; }

        /// <summary>
        /// Fax alan ülke kodu
        /// </summary>
        public string FaxNoCode { get; set; }

        /// <summary>
        /// Fax no
        /// </summary>
        public string FaxNo { get; set; }

        /// <summary>
        /// Kep adresi
        /// </summary>
        public string KepAdress { get; set; }
    }
}
