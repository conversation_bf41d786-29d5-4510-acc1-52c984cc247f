﻿using Datasoft.HR.Core.Entities.Parameters.Regions;

namespace Datasoft.HR.Core.Entities.Common
{
    public class Address
    {
        public long? Id { get; set; }

        /// <summary>
        /// Varsayılan adres mi ?
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// Ülke
        /// </summary>
        public Country Country { get; set; }

        /// <summary>
        /// Eyalet
        /// </summary>
        public State State { get; set; }

        /// <summary>
        /// Şehir
        /// </summary>
        public City City { get; set; }

        /// <summary>
        /// İlçe
        /// </summary>
        public County County { get; set; }

        /// <summary>
        /// Mahalle
        /// </summary>
        public string Neighborhood { get; set; }

        /// <summary>
        /// Cadde
        /// </summary>
        public string Avenue { get; set; }

        /// <summary>
        /// Sokak
        /// </summary>
        public string Street { get; set; }

        /// <summary>
        /// Bina No
        /// </summary>
        public string BuildingNo { get; set; }

        /// <summary>
        /// Apartman No
        /// </summary>
        public string ApartmentNumber { get; set; }

        /// <summary>
        /// Posta Kodu
        /// </summary>
        public string PostCode { get; set; }

        /// <summary>
        /// Adres Kodu (UAVT)
        /// </summary>
        public string AddressCode { get; set; }

        /// <summary>
        /// Ek Açıklama
        /// </summary>
        public string Annotation { get; set; }
    }
}
