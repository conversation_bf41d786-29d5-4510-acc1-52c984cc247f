﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Core.Enums
{
    public enum BranchType
    {
        /// <summary>
        /// Seçili <PERSON>ğil
        /// </summary>
        [Description("")]
        None = 0,

        /// <summary>
        /// Taxable (Mükellefiyetli)
        /// </summary>
        [Description("Taxable")]
        Taxable = 1,

        /// <summary>
        /// Non-Taxable (Mükellefiyetsiz)
        /// </summary>
        [Description("Non-Taxable")]
        NonTaxable = 2
    }
}
