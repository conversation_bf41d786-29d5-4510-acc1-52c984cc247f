@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.budgetSummaryCard {
  padding-inline: 1.375rem;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--color-gray-250);
  @include financial-status-card;
}
.bubble {
  width: 100%;
  max-width: 15rem;
  aspect-ratio: 1 / 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--color-blue-750);
}
.bubbleContent {
  display: flex;
  flex-direction: column;
}
.title {
  @include font-14-500;
  color: var(--color-white);
}
.value {
  @include font-32-600;
  color: var(--color-white);
}
.smallFontSize {
  @include font-24-600;
}
.status {
  @include font-18-600;
}

// colorVariant
.info {
  color: var(--color-white);
}
.success {
  color: var(--color-green-300);
}
.warning {
  color: var(--color-yellow-250);
}
.danger {
  color: var(--color-red-500);
}
