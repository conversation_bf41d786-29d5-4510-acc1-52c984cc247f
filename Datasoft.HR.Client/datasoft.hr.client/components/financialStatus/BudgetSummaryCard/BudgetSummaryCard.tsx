"use client";

import { BudgetSummaryStatusColorVariant, IBudgetSummaryCard } from "./BudgetSummaryCard.types";

import styles from "./BudgetSummaryCard.module.scss";
import { BudgetSummaryStatus, BudgetSummaryStatusLabels } from "@/types/BudgetSummaryStatus";
import Col from "@/components/grid/Col";
import { ColumnSize } from "@/components/grid/Col/Col.types";
import classNames from "classnames";

// BudgetSummaryCard component
const BudgetSummaryCard = (props: IBudgetSummaryCard) => {
  // destructuring props
  const { title, value, status } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers
  /**
   * <AUTHOR>
   * @var valueCharTreshold
   * @description char count which based on the most common usage of money-based expressions. ₺123.456 > 8 chars
   */
  const valueCharTreshold: number = 8;

  /**
   * <AUTHOR>
   * @var hasTooManyCharsInValue
   * @description sets font-size of value if value includes too many chars to fit value on screen properly
   * @returns {boolean}
   */
  const hasTooManyCharsInValue: boolean = Boolean(value.length > valueCharTreshold);

  /**
   * <AUTHOR>
   * @function getStatusColorVariant
   * @description sets colorVariant by status
   * @returns {BudgetSummaryStatusColorVariant}
   */
  const getStatusColorVariant = (): BudgetSummaryStatusColorVariant => {
    switch (status) {
      case BudgetSummaryStatus.creditor:
        return BudgetSummaryStatusColorVariant.success;
      case BudgetSummaryStatus.debtor:
        return BudgetSummaryStatusColorVariant.danger;
      default:
        return BudgetSummaryStatusColorVariant.info;
    }
  };
  // render
  return (
    <Col columnSize={ColumnSize.ColumnSize3} className={styles.budgetSummaryCard}>
      <div className={styles.bubble}>
        <div className={styles.bubbleContent}>
          <div className={styles.title}>{title}</div>
          <div className={classNames(styles.value, hasTooManyCharsInValue && styles.smallFontSize)}>{value}</div>
          <div className={classNames(styles.status, styles[getStatusColorVariant()])}>{BudgetSummaryStatusLabels[status]}</div>
        </div>
      </div>
    </Col>
  );
};

// export
export default BudgetSummaryCard;
