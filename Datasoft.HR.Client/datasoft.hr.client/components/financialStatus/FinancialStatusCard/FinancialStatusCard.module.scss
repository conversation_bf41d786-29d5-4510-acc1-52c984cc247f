@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.financialStatusCard {
  @include financial-status-card;
}
.header {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: $gap-medium;
  background-color: var(--color-gray-100);
  border-radius: $border-radius-xxxlarge;
}
.title {
  @include font-14-500;
  color: var(--color-gray-1000);
}
.value {
  @include font-24-600;
  color: var(--color-gray-1250);
}
.body {
  display: flex;
  flex-direction: column;
  gap: $gap-xlarge;
}
