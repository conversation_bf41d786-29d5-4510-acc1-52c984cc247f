"use client";

import { IFinancialStatusCard } from "./FinancialStatusCard.types";

import styles from "./FinancialStatusCard.module.scss";
import FinancialStatusItem from "./components/FinancialStatusItem";
import Col from "@/components/grid/Col";
import { ColumnSize } from "@/components/grid/Col/Col.types";

// FinancialStatusCard component
const FinancialStatusCard = (props: IFinancialStatusCard) => {
  // destructuring props
  const { id, title, value, items, columnSize = ColumnSize.ColumnSize3 } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <Col columnSize={columnSize} className={styles.financialStatusCard}>
      <div className={styles.header}>
        <div className={styles.title}>{title}</div>
        <div className={styles.value}>{value}</div>
      </div>
      <div className={styles.body}>
        {items.map((item, index) => (
          <FinancialStatusItem key={`FinancialStatusItem${id}${index}`} {...item} />
        ))}
      </div>
    </Col>
  );
};

// export
export default FinancialStatusCard;
