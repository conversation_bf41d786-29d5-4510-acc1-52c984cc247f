@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.financialStatusItem {
  display: flex;
  gap: $gap-medium;
  color: var(--color-gray-1250);
}
.dot {
  width: 0.625rem;
  height: 0.625rem;
  background-color: var(--color-white);
  border-radius: $border-radius-xlarge;
  margin-top: 0.25rem;
}
.titleContainer {
  display: flex;
  flex-direction: column;
  gap: $gap-xxsmall;
}
.title {
  @include font-12-500;
}
.value {
  @include font-20-500;
}

// colorVariant
.primary {
  background-color: var(--color-blue-1200);
}
.secondary {
  background-color: var(--color-teal-100);
}
.success {
  background-color: var(--color-green-600);
}
.warning {
  background-color: var(--color-yellow-250);
}
.danger {
  background-color: var(--color-red-450);
}
.info {
  background-color: var(--color-indigo-300);
}
