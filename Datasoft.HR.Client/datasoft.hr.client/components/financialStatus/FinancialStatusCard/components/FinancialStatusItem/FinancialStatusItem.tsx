"use client";

import { IFinancialStatusItem } from "./FinancialStatusItem.types";

import styles from "./FinancialStatusItem.module.scss";
import classNames from "classnames";

// FinancialStatusItem component
const FinancialStatusItem = (props: IFinancialStatusItem) => {
  // destructuring props
  const { title, value, colorVariant, onClick } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <div className={styles.financialStatusItem} onClick={onClick}>
      <div className={classNames(styles.dot, styles[colorVariant])}></div>
      <div className={styles.titleContainer}>
        <div className={styles.title}>{title}</div>
        <div className={styles.value}>{value}</div>
      </div>
    </div>
  );
};

// export
export default FinancialStatusItem;
