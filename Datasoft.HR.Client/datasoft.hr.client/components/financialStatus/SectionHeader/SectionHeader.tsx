"use client";

import { ISectionHeader } from "./SectionHeader.types";

import styles from "./SectionHeader.module.scss";
import Col from "@/components/grid/Col";
import { ColumnSize } from "@/components/grid/Col/Col.types";

// SectionHeader component
const SectionHeader = (props: ISectionHeader) => {
  // destructuring props
  const { title, controls } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <Col columnSize={ColumnSize.ColumnSize12} className={styles.sectionHeader}>
      <div className={styles.title}>{title}</div>
      <div className={styles.controls}>{controls}</div>
    </Col>
  );
};

// export
export default SectionHeader;
