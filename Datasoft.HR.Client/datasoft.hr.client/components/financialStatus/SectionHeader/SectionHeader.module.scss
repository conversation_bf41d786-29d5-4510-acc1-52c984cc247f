@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.sectionHeader {
  display: flex;
  padding: 1.5rem 2rem;
  gap: $gap-giant;
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-250);

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
  }
}
.title {
  display: flex;
  align-items: center;
  flex-grow: 1;
  @include font-22-500;
  color: var(--color-black);
}
.controls {
  display: flex;
  justify-content: flex-end;
  align-items: center;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    gap: $gap-giant;
  }
}
