@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.financialStatsDonutChartCard {
  display: flex;
  flex-direction: column;
  gap: $gap-giant;
  padding: 1rem 2rem;
  background-color: var(--color-white);
}
.title {
  @include font-20-500;
  color: var(--color-black);
}
.chart {
  * {
    outline: none;
  }
  :global(.recharts-legend-item) {
    margin-right: 0 !important;
    display: flex !important;
    align-items: center;
    gap: $gap-medium;
  }
  :global(.recharts-surface) {
    margin-right: 0 !important;
  }
  :global(.recharts-legend-item-text) {
    display: inline-block !important;
    width: 8.75rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    @include font-14-400;
    color: var(--color-gray-1000) !important;
  }
}
.chartTooltipWrapper {
  background-color: var(--color-white) !important;
  border: 1px solid var(--color-gray-250) !important;
  border-radius: $border-radius-default !important;
  @include font-12-400;
}
