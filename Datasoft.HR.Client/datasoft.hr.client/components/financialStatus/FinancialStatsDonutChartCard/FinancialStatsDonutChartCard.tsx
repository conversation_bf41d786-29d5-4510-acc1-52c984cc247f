"use client";

import { ChartColorPalette, chartColorPaletteGroups, IFinancialStatsDonutChartCard } from "./FinancialStatsDonutChartCard.types";
import { PieChart, Pie, Cell, Legend, Tooltip } from "recharts";
import styles from "./FinancialStatsDonutChartCard.module.scss";
import Col from "@/components/grid/Col";
import { ColumnSize } from "@/components/grid/Col/Col.types";
import ChartCenterContent from "./components/ChartCenterContent";

// FinancialStatsDonutChartCard component
const FinancialStatsDonutChartCard = (props: IFinancialStatsDonutChartCard) => {
  // destructuring props
  const { data, title, text, value, chartColorPalette = ChartColorPalette.default } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  const formatChartTooltipText = (value: number, name: string) => {
    return [`${value}`, `${name}`];
  };
  // render
  return (
    <Col columnSize={ColumnSize.ColumnSize6} className={styles.financialStatsDonutChartCard}>
      <div className={styles.title}>{title}</div>
      <PieChart className={styles.chart} width={428} height={240}>
        <Pie data={data} cx="50%" cy="50%" innerRadius={90} outerRadius={120} dataKey="value">
          {data.map((entry, index) => (
            <Cell
              key={`cell-${index}`}
              fill={chartColorPaletteGroups[chartColorPalette][index % chartColorPaletteGroups[chartColorPalette].length]}
              stroke=""
            />
          ))}
        </Pie>
        <ChartCenterContent text={text} value={value} />
        <Legend verticalAlign="middle" alignmentBaseline="middle" align="right" layout="vertical" width={156} iconType="circle" iconSize={8} />
        <Tooltip formatter={formatChartTooltipText} wrapperClassName={styles.chartTooltipWrapper} />
      </PieChart>
    </Col>
  );
};

// export
export default FinancialStatsDonutChartCard;
