"use client";

import styles from "./ChartCenterContent.module.scss";
import { IChartCenterContent } from "./ChartCenterContent.types";

// ChartCenterContent component
const ChartCenterContent = (props: IChartCenterContent) => {
  // destructuring props
  const { text, value } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <foreignObject x="80" y="80" width={110} height={80} overflow="visible">
      <div className={styles.chartCenterContent}>
        <div className={styles.text}>{text}</div>
        <div className={styles.value}>{value}</div>
      </div>
    </foreignObject>
  );
};

// export
export default ChartCenterContent;
