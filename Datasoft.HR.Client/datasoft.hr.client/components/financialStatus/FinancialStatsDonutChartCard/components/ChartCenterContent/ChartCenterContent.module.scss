@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.chartCenterContent {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: $gap-xxsmall;
  text-align: center;
}
.text {
  @include font-14-500;
  color: var(--color-gray-1000);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.value {
  @include font-28-600;
  color: var(--color-gray-1250);
}
