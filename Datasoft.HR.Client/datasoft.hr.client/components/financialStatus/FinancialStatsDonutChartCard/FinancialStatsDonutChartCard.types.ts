export interface IFinancialStatsDonutChartCard {
  title: string;
  text: string;
  value: string;
  data: IChartData[];
  chartColorPalette?: ChartColorPalette;
}

type TColorPaletteGroups = {
  [key in `${keyof typeof ChartColorPalette}`]: string[];
};

export interface IChartData {
  name: string;
  value: number;
}

export enum ChartColorPalette {
  default = "default",
  pastel = "pastel",
  vivid = "vivid",
  dimmed = "dimmed",
}

export const chartColorPaletteGroups: TColorPaletteGroups = {
  default: ["#B3C83C", "#00A4E9", "#F24B40", "#FD6191", "#F5912F", "#EDDB5C"],
  pastel: ["#FFEA67", "#B5AFD4", "#FFC8E2", "#69A5CD", "#A4D15E", "#BA73B4"],
  vivid: ["#FF9828", "#E012A9", "#00ADFB", "#13E588", "#E5D02E", "#941CEE"],
  dimmed: ["#FD6191", "#E5D02E", "#00B496", "#FF9828", "#941CEE", "#00ADFB"],
};
