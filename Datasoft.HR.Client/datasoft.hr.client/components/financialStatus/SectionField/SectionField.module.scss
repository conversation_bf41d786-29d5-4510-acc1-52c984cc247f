@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.sectionField {
  display: flex;
  flex-direction: column;
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-gray-250);
}
.header {
  display: flex;
  padding: 1.5rem 2rem;
  gap: 2rem;
}
.icon {
  width: 4rem;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-gray-750);
  @include font-36-400;
  background-color: var(--color-gray-100);
  border: 1px solid var(--color-gray-250);
  border-radius: $border-radius-xxxlarge;
}
.title {
  display: flex;
  align-items: center;
  flex-grow: 1;
  @include font-16-500;
  color: var(--color-gray-1000);
}
.value {
  @include font-32-600;
  color: var(--color-gray-1250);
}
.body {
  display: flex;
  flex-wrap: wrap;
}
