"use client";

import { ISectionField } from "./SectionField.types";

import styles from "./SectionField.module.scss";
import Col from "@/components/grid/Col";
import { ColumnSize } from "@/components/grid/Col/Col.types";

// SectionField component
const SectionField = (props: ISectionField) => {
  // destructuring props
  const { title, value, icon, children } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <Col columnSize={ColumnSize.ColumnSize12} className={styles.sectionField}>
      <div className={styles.header}>
        <div className={styles.icon}>{icon}</div>
        <div className={styles.titleContainer}>
          <div className={styles.title}>{title}</div>
          <div className={styles.value}>{value}</div>
        </div>
      </div>
      <div className={styles.body}>{children}</div>
    </Col>
  );
};

// export
export default SectionField;
