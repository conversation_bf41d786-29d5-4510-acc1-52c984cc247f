"use client";

import { IRangeValue } from "./RangeValue.types";

import styles from "./RangeValue.module.scss";

import { DateFormats } from "@/utils/dateFormatter/enums";
import { dateFormatter } from "@/utils/dateFormatter";
import Icon from "@/components/common/Icon";
import { IoCalendarOutline } from "react-icons/io5";

// RangeValue component
const RangeValue = (props: IRangeValue) => {
  // destructuring props
  const { startDate, endDate, ...restProps } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <div {...restProps} className={styles.rangeValue}>
      <div className={styles.text}>{`${dateFormatter(startDate, DateFormats.DATE_WITH_DOTS_SHORT_YEAR)} - ${dateFormatter(
        endDate,
        DateFormats.DATE_WITH_DOTS_SHORT_YEAR
      )}`}</div>
      <Icon icon={<IoCalendarOutline />} />
    </div>
  );
};

// export
export default RangeValue;
