@use "/styles/variables" as *;
@use "/styles/mixins.scss" as *;

.rangeValue {
  margin-left: 2rem;
  position: relative;
  height: 63px;
  display: flex;
  flex-grow: 1;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 16px;
  width: 14rem;
  gap: $gap-xxxlarge;
  border: 1px solid var(--color-gray-300);
  border-radius: $border-radius-xxlarge;
  cursor: pointer;
  &:hover {
    opacity: $default-hover-opacity;
  }

  @media (max-width: $breakpoint-md) {
    margin-left: 0;
    width: 100%;
  }
}
.text {
  @include font-14-500;
  color: var(--color-gray-750);
}
