"use client";

import "react-datepicker/dist/react-datepicker.css";
import { default as ReactDatePicker } from "react-datepicker";
import { defaultFloatingSettings } from "@/utils/floating";
import classNames from "classnames";
import { DateFormats } from "@/utils/dateFormatter/enums";
import { IDateRangePicker } from "./DateRangePicker.types";
import datePickerStyles from "./../../form/DatePicker/DatePicker.module.scss";
import RangeValue from "./components/RangeValue";

// DateRangePicker component
const DateRangePicker = (props: IDateRangePicker) => {
  // destructuring props
  const { onChange, maxDate, minDate, startDate, endDate, showMonthDropdown, showYearDropdown } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <ReactDatePicker
      wrapperClassName={datePickerStyles.wrapper}
      calendarClassName={datePickerStyles.calendar}
      calendarIconClassName={classNames(datePickerStyles.calendarIcon)}
      clearButtonClassName={datePickerStyles.clearButton}
      yearClassName={() => datePickerStyles.year}
      dayClassName={() => datePickerStyles.day}
      monthClassName={() => datePickerStyles.month}
      weekDayClassName={() => datePickerStyles.weekDay}
      timeClassName={() => datePickerStyles.time}
      popperClassName={datePickerStyles.popper}
      popperPlacement={defaultFloatingSettings.placement}
      showPopperArrow={false}
      showMonthDropdown={showMonthDropdown}
      showYearDropdown={showYearDropdown}
      selected={startDate}
      dateFormat={DateFormats.DATE_WITH_SLASHES_FOR_DATE_PICKER}
      customInput={<RangeValue startDate={startDate} endDate={endDate} />}
      minDate={minDate}
      maxDate={maxDate}
      selectsRange
      startDate={startDate}
      endDate={endDate}
      onChange={(date) => {
        onChange(date as Date[]);
      }}
    />
  );
};

// export
export default DateRangePicker;
