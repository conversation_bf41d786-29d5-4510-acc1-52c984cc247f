"use client";

import Modal from "@/components/common/Modal";
import { ModalTypes } from "@/contexts/ModalContext/ModalContext.types";
import { SizeVariant } from "@/types/SizeVariant";

// ModalDefault component
const ModalDefault = () => {
  // destructuring props

  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <Modal sizeVariant={SizeVariant.default} id={ModalTypes.ModalDefault} title="Modal Default Sample">
      <p>
        Lorem, ipsum dolor sit amet consectetur adipisicing elit. Consequatur quidem minus amet tempore asperiores doloremque reprehenderit ipsum laborum fugit,
        saepe facilis placeat quas doloribus debitis nostrum cumque autem nesciunt earum.
      </p>
      <p>
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Illum debitis distinctio vitae eveniet voluptate quis, natus adipisci molestias nobis
        laudantium ipsa a ullam amet exercitationem facilis quo nemo porro voluptatem!
      </p>
      <p>
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptatem molestiae maxime sapiente illo nam dolorem fugit, ratione, eum itaque, atque ipsa.
        Nisi, molestias magnam earum ex nulla voluptates quas accusantium!
      </p>
      <p>
        Lorem ipsum dolor sit, amet consectetur adipisicing elit. Laboriosam labore minus recusandae animi exercitationem molestiae harum nihil commodi aperiam
        aliquid quae provident perspiciatis porro saepe dolorum magni, placeat reiciendis nam!
      </p>
    </Modal>
  );
};

// export
export default ModalDefault;
