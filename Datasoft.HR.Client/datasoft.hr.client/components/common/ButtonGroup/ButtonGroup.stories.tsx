import type { <PERSON>a, StoryObj } from "@storybook/nextjs";

import { AlignVariant } from "@/types/AlignVariant";
import ButtonGroup from "./ButtonGroup";
import Button from "../Button/Button";
import { StyleVariant } from "@/types/StyleVariant";
import DropdownButtonWithMenu from "../DropdownButtonWithMenu";

const meta = {
  tags: ["autodocs"],
  component: ButtonGroup,
  argTypes: {
    alignVariant: {
      options: Object.values(AlignVariant),
      control: { type: "inline-radio" },
    },
  },
} satisfies Meta<typeof ButtonGroup>;

export const Default: StoryObj<typeof meta> = {
  args: {
    alignVariant: AlignVariant.left,
    block: false,
    children: (
      <>
        <Button id="" onClick={() => null} text="Kaydet" />
        <Button id="" onClick={() => null} text="Yeni" />
        <Button id="" styleVariant={StyleVariant.outlined} onClick={() => null} text="İptal" />
      </>
    ),
  },
};

export const Combined: StoryObj<typeof meta> = {
  args: {
    alignVariant: AlignVariant.left,
    block: false,
    children: (
      <>
        <Button id="" onClick={() => null} text="Kaydet" />
        <Button id="" onClick={() => null} text="Yeni" styleVariant={StyleVariant.outlined} />
        <DropdownButtonWithMenu
          id=""
          dropdownButton={{
            styleVariant: StyleVariant.outlined,
          }}
          menuItems={[
            {
              title: "Fatura (Genel Tip)",
              onClick: () => null,
            },
            {
              title: "SGK",
              onClick: () => null,
            },
            {
              title: "Kamu",
              onClick: () => null,
            },
            {
              title: "İade",
              onClick: () => null,
            },
          ]}
        />
      </>
    ),
  },
};

export default meta;
