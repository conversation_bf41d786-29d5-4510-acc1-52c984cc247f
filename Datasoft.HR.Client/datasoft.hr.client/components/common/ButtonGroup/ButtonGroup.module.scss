@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.buttonGroup {
  display: flex;

  & > * {
    & > button {
    }

    &:first-child {
      @include buttonGroup-firstChildStyles;
      & > button {
        @include buttonGroup-firstChildStyles;
      }
    }
    &:last-child {
      @include buttonGroup-lastChildStyles;
      & > button {
        @include buttonGroup-lastChildStyles;
      }
    }
    &:not(:first-child):not(:last-child) {
      @include buttonGroup-betweenChildrenStyles;
      & > button {
        @include buttonGroup-betweenChildrenStyles;
      }
    }
  }
}

.block {
  width: 100%;
  button[class*="button"] {
    flex-grow: 1;
  }
}

// alignVariant
.left {
  justify-content: flex-start;
}
.center {
  justify-content: center;
}
.right {
  justify-content: flex-end;
}
