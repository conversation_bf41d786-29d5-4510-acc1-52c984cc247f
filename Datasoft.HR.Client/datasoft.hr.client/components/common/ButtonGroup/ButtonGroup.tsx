"use client";

import { IButtonGroup } from "./ButtonGroup.types";

import styles from "./ButtonGroup.module.scss";
import { AlignVariant } from "@/types/AlignVariant";
import classNames from "classnames";

// ButtonGroup component
const ButtonGroup = (props: IButtonGroup) => {
  // destructuring props
  const { children, alignVariant = AlignVariant.left, block } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return <div className={classNames(styles.buttonGroup, styles[alignVariant], block && styles.block)}>{children}</div>;
};

// export
export default ButtonGroup;
