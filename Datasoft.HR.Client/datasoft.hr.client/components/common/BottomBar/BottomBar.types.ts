import { IButton } from "../Button/Button.types";

export interface IBottomBar {
  hasPageControls?: boolean;
  hasNavigationControls?: boolean;
  blockPageControls?: boolean;
  hasButtonSave?: boolean;
  hasButtonNew?: boolean;
  hasButtonCancel?: boolean;
  hasButtonDelete?: boolean;
  hasButtonReset?: boolean;
  hasButtonGoToFirst?: boolean;
  hasButtonPrev?: boolean;
  hasButtonNext?: boolean;
  hasButtonGoToLast?: boolean;
  buttonSave?: TBottomBarButton;
  buttonNew?: TBottomBarButton;
  buttonCancel?: TBottomBarButton;
  buttonDelete?: TBottomBarButton;
  buttonReset?: TBottomBarButton;
  buttonGoToFirst?: TBottomBarButton;
  buttonPrev?: TBottomBarButton;
  buttonNext?: TBottomBarButton;
  buttonGoToLast?: TBottomBarButton;
}

export type TBottomBarButton = Omit<IButton, "id">;
