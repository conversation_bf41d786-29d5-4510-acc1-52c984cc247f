@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.bottomBar {
  position: relative;
  z-index: $bottombar-z-index;
  width: 100%;
  display: flex;
  padding: 1rem;
  gap: $gap-xlarge;
  align-items: center;
  justify-content: space-between;
  border-radius: $border-radius-large;
  border: 1px solid var(--color-blue-350);
  background-color: var(--color-white);
}
.controls {
  display: flex;
  gap: $gap-xlarge;
}
.pageControls {
  flex: 1;
  justify-content: flex-start;
}
.navigationControls {
  justify-content: flex-end;
}
.onlyNavigationControls {
  flex: 1;
  justify-content: flex-end;
}
