"use client";

import { IBottomBar, TBottomBarButton } from "./BottomBar.types";

import styles from "./BottomBar.module.scss";
import Button from "../Button";
import { StyleVariant } from "@/types/StyleVariant";
import { MdSettingsBackupRestore, MdKeyboardDoubleArrowLeft, MdKeyboardDoubleArrowRight, MdKeyboardArrowLeft, MdKeyboardArrowRight } from "react-icons/md";
import classNames from "classnames";

// BottomBar component
const BottomBar = (props: IBottomBar) => {
  // initial values
  const defaultValueButtonSave: TBottomBarButton = {
    text: "Kaydet",
    onClick: () => null,
  };
  const defaultValueButtonNew: TBottomBarButton = {
    text: "Yeni",
    styleVariant: StyleVariant.outlined,
    onClick: () => null,
  };
  const defaultValueButtonCancel: TBottomBarButton = {
    text: "Vazgeç",
    styleVariant: StyleVariant.outlined,
    onClick: () => null,
  };
  const defaultValueButtonDelete: TBottomBarButton = {
    text: "Sil",
    styleVariant: StyleVariant.outlined,
    onClick: () => null,
  };
  const defaultValueButtonReset: TBottomBarButton = {
    leftIcon: <MdSettingsBackupRestore />,
    // colorVariant: ColorVariant.light,
    disabled: true,
    styleVariant: StyleVariant.outlined,
    onClick: () => null,
  };
  const defaultValueButtonGoToFirst: TBottomBarButton = {
    leftIcon: <MdKeyboardDoubleArrowLeft />,
    onlyIcon: true,
    styleVariant: StyleVariant.outlined,
    disabled: true,
    onClick: () => null,
  };
  const defaultValueButtonPrev: TBottomBarButton = {
    leftIcon: <MdKeyboardArrowLeft />,
    onlyIcon: true,
    styleVariant: StyleVariant.outlined,
    onClick: () => null,
  };
  const defaultValueButtonNext: TBottomBarButton = {
    leftIcon: <MdKeyboardArrowRight />,
    onlyIcon: true,
    styleVariant: StyleVariant.outlined,
    onClick: () => null,
  };
  const defaultValueButtonGoToLast: TBottomBarButton = {
    leftIcon: <MdKeyboardDoubleArrowRight />,
    onlyIcon: true,
    styleVariant: StyleVariant.outlined,
    onClick: () => null,
  };
  // destructuring props
  const {
    hasPageControls = true,
    hasNavigationControls = true,
    blockPageControls = true,
    hasButtonSave = true,
    hasButtonNew = true,
    hasButtonCancel = true,
    hasButtonDelete = true,
    hasButtonReset = true,
    hasButtonGoToFirst = true,
    hasButtonPrev = true,
    hasButtonNext = true,
    hasButtonGoToLast = true,
    buttonSave,
    buttonNew,
    buttonCancel,
    buttonDelete,
    buttonReset,
    buttonGoToFirst,
    buttonPrev,
    buttonNext,
    buttonGoToLast,
  } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <div className={classNames(styles.bottomBar, !hasPageControls && hasNavigationControls && styles.onlyNavigationControls)}>
      {hasPageControls && (
        <div className={classNames(styles.controls, styles.pageControls)}>
          {hasButtonSave && <Button id="BottomBar-Save" block={blockPageControls} {...{ ...defaultValueButtonSave, ...buttonSave }} />}
          {hasButtonNew && <Button id="BottomBar-New" block={blockPageControls} {...{ ...defaultValueButtonNew, ...buttonNew }} />}
          {hasButtonCancel && <Button id="BottomBar-Cancel" block={blockPageControls} {...{ ...defaultValueButtonCancel, ...buttonCancel }} />}
          {hasButtonDelete && <Button id="BottomBar-Delete" block={blockPageControls} {...{ ...defaultValueButtonDelete, ...buttonDelete }} />}
          {hasButtonReset && <Button id="BottomBar-Reset" block={blockPageControls} {...{ ...defaultValueButtonReset, ...buttonReset }} />}
        </div>
      )}
      {hasNavigationControls && (
        <div className={classNames(styles.controls, styles.navigationControls)}>
          {hasButtonGoToFirst && <Button id="BottomBar-GoToFirst" {...{ ...defaultValueButtonGoToFirst, ...buttonGoToFirst }} />}
          {hasButtonPrev && <Button id="BottomBar-Prev" {...{ ...defaultValueButtonPrev, ...buttonPrev }} />}
          {hasButtonNext && <Button id="BottomBar-Next" {...{ ...defaultValueButtonNext, ...buttonNext }} />}
          {hasButtonGoToLast && <Button id="BottomBar-GoToLast" {...{ ...defaultValueButtonGoToLast, ...buttonGoToLast }} />}
        </div>
      )}
    </div>
  );
};

// export
export default BottomBar;
