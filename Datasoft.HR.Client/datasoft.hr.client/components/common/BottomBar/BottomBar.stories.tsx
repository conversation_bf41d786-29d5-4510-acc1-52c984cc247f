import type { Meta, StoryObj } from "@storybook/nextjs";

import BottomBar from "./BottomBar";

const meta = {
  tags: ["autodocs"],
  component: BottomBar,
  argTypes: {},
} satisfies Meta<typeof BottomBar>;

export const Default: StoryObj<typeof meta> = {
  args: {
    hasPageControls: true,
    blockPageControls: true,
    hasNavigationControls: true,
    hasButtonSave: true,
    hasButtonNew: true,
    hasButtonCancel: true,
    hasButtonDelete: true,
    hasButtonReset: true,
    hasButtonGoToFirst: true,
    hasButtonPrev: true,
    hasButtonNext: true,
    hasButtonGoToLast: true,
  },
};

export const OnlyPageControls: StoryObj<typeof meta> = {
  args: {
    hasPageControls: true,
    blockPageControls: true,
    hasNavigationControls: false,
    hasButtonSave: true,
    hasButtonNew: true,
    hasButtonCancel: true,
    hasButtonDelete: true,
    hasButtonReset: true,
    hasButtonGoToFirst: true,
    hasButtonPrev: true,
    hasButtonNext: true,
    hasButtonGoToLast: true,
  },
};

export const OnlyNavigationControls: StoryObj<typeof meta> = {
  args: {
    hasPageControls: false,
    blockPageControls: true,
    hasNavigationControls: true,
    hasButtonSave: true,
    hasButtonNew: true,
    hasButtonCancel: true,
    hasButtonDelete: true,
    hasButtonReset: true,
    hasButtonGoToFirst: true,
    hasButtonPrev: true,
    hasButtonNext: true,
    hasButtonGoToLast: true,
  },
};

export const OnlySpecificControls: StoryObj<typeof meta> = {
  args: {
    hasPageControls: true,
    hasNavigationControls: true,
    blockPageControls: true,
    hasButtonSave: true,
    hasButtonNew: true,
    hasButtonCancel: false,
    hasButtonDelete: false,
    hasButtonReset: true,
    hasButtonGoToFirst: true,
    hasButtonPrev: true,
    hasButtonNext: true,
    hasButtonGoToLast: true,
  },
};

export default meta;
