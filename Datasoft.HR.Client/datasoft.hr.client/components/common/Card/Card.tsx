"use client";

import { ICard } from "./Card.types";

import styles from "./Card.module.scss";

// Card component
const Card = (props: ICard) => {
  // destructuring props
  const { title, children } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <div className={styles.card}>
      {title && <div className={styles.header}>{title}</div>}
      <div className={styles.body}>{children}</div>
    </div>
  );
};

// export
export default Card;
