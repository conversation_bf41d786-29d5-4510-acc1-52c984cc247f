import type { <PERSON>a, StoryObj } from "@storybook/nextjs";

import Card from "./Card";
import Input from "@/components/form/Input";
import { ColumnSize } from "@/components/grid/Col/Col.types";
import { useState } from "react";
import InputMoney from "@/components/form/InputMoney";

const meta = {
  tags: ["autodocs"],
  component: Card,
} satisfies Meta<typeof Card>;

export const Default: StoryObj<typeof meta> = {
  args: {
    title: "Genel Ayarlar",
    children: null,
  },
};

export const WithFormElements: StoryObj<typeof meta> = {
  render: (args) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [inputValues, setInputValues] = useState({
      name: "",
      surname: "",
      email: "",
      amount: 0,
      tax: 0,
      stockName: "",
      gType: "",
    });
    return (
      <Card {...args}>
        <Input
          id="name"
          label="Ad"
          columnSize={ColumnSize.ColumnSize2}
          value={inputValues.name}
          onChange={(e: any) => {
            setInputValues({
              ...inputValues,
              name: e.target.value,
            });
          }}
          required
        />
        <Input
          id="surname"
          label="Soyad"
          columnSize={ColumnSize.ColumnSize2}
          value={inputValues.surname}
          onChange={(e: any) => {
            setInputValues({
              ...inputValues,
              surname: e.target.value,
            });
          }}
          required
        />
        <Input
          id="email"
          label="E-posta"
          columnSize={ColumnSize.ColumnSize4}
          value={inputValues.email}
          onChange={(e: any) => {
            setInputValues({
              ...inputValues,
              email: e.target.value,
            });
          }}
        />
        <InputMoney
          id="amount"
          label="Miktar"
          columnSize={ColumnSize.ColumnSize4}
          value={inputValues.amount}
          onChange={(e: any) => {
            setInputValues({
              ...inputValues,
              amount: e.target.value,
            });
          }}
        />
        <InputMoney
          id="tax"
          label="Vergi"
          columnSize={ColumnSize.ColumnSize3}
          value={inputValues.tax}
          onChange={(e: any) => {
            setInputValues({
              ...inputValues,
              tax: e.target.value,
            });
          }}
        />
        <Input
          id="stockName"
          label="Stok Adı"
          columnSize={ColumnSize.ColumnSize3}
          value={inputValues.stockName}
          onChange={(e: any) => {
            setInputValues({
              ...inputValues,
              stockName: e.target.value,
            });
          }}
        />
        <Input
          id="gType"
          label="G TİPİ"
          columnSize={ColumnSize.ColumnSize1}
          value={inputValues.gType}
          onChange={(e: any) => {
            setInputValues({
              ...inputValues,
              gType: e.target.value,
            });
          }}
        />
      </Card>
    );
  },
  args: {
    title: "Genel Ayarlar",
    children: null,
  },
};

export default meta;
