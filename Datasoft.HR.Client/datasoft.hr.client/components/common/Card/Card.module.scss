@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.card {
  position: relative;
  border: 1px solid var(--color-blue-350);
  border-radius: $border-radius-large;
  background-color: var(--color-white);
}
.header {
  position: absolute;
  left: 1rem;
  top: -9px;
  @include font-18-600;
  color: var(--color-gray-950);
  line-height: 1;
  background-color: var(--color-white);
  z-index: $card-z-index;
}
.body {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  padding: 1.5rem calc(1rem - $gutter-small * 0.5);

  & > * {
    @include calculate-column-gutter($gutter-small, $gutter-xlarge);
    &:last-child {
      // margin-bottom: 0;
    }
  }
}
