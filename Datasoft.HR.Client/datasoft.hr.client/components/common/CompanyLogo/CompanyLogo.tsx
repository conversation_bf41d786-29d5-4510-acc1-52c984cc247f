import Image from "next/image";
import logo from "@/public/logo-198x44.svg";
import { useRouter } from "next/navigation";
import { routes } from "@/routes";
import styles from "./CompanyLogo.module.scss";
import Col from "@/components/grid/Col";
import { ColumnSize } from "@/components/grid/Col/Col.types";
// CompanyLogo component
const CompanyLogo = () => {
  // destructuring props

  // state

  // context hooks
  const router = useRouter();
  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <Col columnSize={ColumnSize.ColumnSize2} className={styles.companyLogo}>
      <Image width={99} height={22} src={logo} alt="" className={styles.logo} priority onClick={() => router.push(routes.home.url)} />
      <div className={styles.info}>Cloud HR</div>
    </Col>
  );
};

// export
export default CompanyLogo;
