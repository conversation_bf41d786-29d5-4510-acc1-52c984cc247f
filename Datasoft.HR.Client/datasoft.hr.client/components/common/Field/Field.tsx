"use client";

import { IField } from "./Field.types";

import styles from "./Field.module.scss";
import { LayoutVariant } from "@/types/LayoutVariant";
import classNames from "classnames";
import { AlignVariant } from "@/types/AlignVariant";

// Field component
const Field = (props: IField) => {
  // destructuring props
  const { label, value, layoutVariant = LayoutVariant.horizontal, alignVariant = AlignVariant.left } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <div className={classNames(styles.field, styles[layoutVariant], styles[alignVariant])}>
      {label && <div className={styles.label}>{label}</div>}
      {value && <div className={styles.value}>{value}</div>}
    </div>
  );
};

// export
export default Field;
