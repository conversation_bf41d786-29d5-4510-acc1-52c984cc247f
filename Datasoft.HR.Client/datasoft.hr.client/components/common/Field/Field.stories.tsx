import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import Field from "./Field";
import { AlignVariant } from "@/types/AlignVariant";
import { LayoutVariant } from "@/types/LayoutVariant";

const meta = {
  tags: ["autodocs"],
  component: Field,
  argTypes: {
    alignVariant: {
      options: Object.values(AlignVariant),
      control: { type: "inline-radio" },
    },
    layoutVariant: {
      options: Object.values(LayoutVariant),
      control: { type: "inline-radio" },
    },
  },
} satisfies Meta<typeof Field>;

export const Default: StoryObj<typeof meta> = {
  args: {
    alignVariant: AlignVariant.left,
    layoutVariant: LayoutVariant.horizontal,
    label: "Alacak Bakiyesi",
    value: "500",
  },
};

export default meta;
