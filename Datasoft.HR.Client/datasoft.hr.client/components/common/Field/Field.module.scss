@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.field {
  display: flex;
}
.label {
  @include font-14-700;
  color: var(--color-gray-1000);
}
.value {
  @include font-14-400;
  color: var(--color-gray-750);
}

// layoutVariant
.horizontal {
  flex-direction: row;
  align-items: center;
  gap: $gap-xxlarge;

  // children
  .label {
    &::after {
      content: ":";
    }
  }
}
.vertical {
  flex-direction: column;
}

// alignVariant
.left {
  text-align: left;
  // layoutVariant
  &.horizontal {
    justify-content: flex-start;
  }
}
.right {
  text-align: right;
  // layoutVariant
  &.horizontal {
    justify-content: flex-end;
  }
}
.center {
  text-align: center;
  // layoutVariant
  &.horizontal {
    justify-content: center;
  }
}
