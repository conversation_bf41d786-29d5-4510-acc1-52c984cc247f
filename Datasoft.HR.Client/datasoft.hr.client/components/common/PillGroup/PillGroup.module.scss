@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.pillGroup {
  display: flex;
  align-items: center;
  width: max-content;
  padding: 0.25rem;
  gap: 1rem;
  border-radius: $border-radius-xxxlarge;
  background-color: var(--color-gray-100);

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    width: 100%;
  }
}

// alignVariant
.left {
  justify-content: flex-start;
}
.center {
  justify-content: center;
}
.right {
  justify-content: flex-end;
}
