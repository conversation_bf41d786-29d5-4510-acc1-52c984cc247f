"use client";

import { IPillGroupItem } from "./PillGroupItem.types";

import styles from "./PillGroupItem.module.scss";
import classNames from "classnames";
import { ColorVariant } from "@/types/ColorVariant";

// PillGroupItem component
const PillGroupItem = (props: IPillGroupItem) => {
  // destructuring props
  const { text, active, block, onClick, colorVariant = ColorVariant.primary } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <button className={classNames(styles.pillGroupItem, active && styles.active, active && styles[colorVariant], block && styles.block)} onClick={onClick}>
      {text}
    </button>
  );
};

// export
export default PillGroupItem;
