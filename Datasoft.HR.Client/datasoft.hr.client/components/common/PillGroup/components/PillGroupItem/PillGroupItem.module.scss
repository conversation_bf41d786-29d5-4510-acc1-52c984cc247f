@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.pillGroupItem {
  height: 55px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $gap-default;
  border-radius: $border-radius-xxxlarge-item-tolerance;
  padding: 1rem;
  font-family: var(--font-axiforma);
  @include font-14-500;
  color: var(--color-gray-750);
  background-color: transparent;
  transition: color linear $animation-duration-small, background-color linear $animation-duration-small, opacity linear $animation-duration-small;
  outline: none;
  border: none;
  cursor: pointer;
  white-space: nowrap;

  &:hover {
    opacity: $default-hover-opacity;
  }

  @media (max-width: $breakpoint-md) {
    width: 100%;
  }
}

.block {
  flex-grow: 1;
}
.active {
}

// colorVariant
.primary {
  color: var(--color-white);
  background-color: var(--color-blue-700);
}
.success {
  color: var(--color-white);
  background-color: var(--color-green-300);
}
.light {
  color: var(--color-white);
  background-color: var(--color-gray-700);
}
