import { Dispatch, SetStateAction } from "react";
import { IPillGroupItem } from "./components/PillGroupItem/PillGroupItem.types";
import { AlignVariant } from "@/types/AlignVariant";
import { ColorVariant } from "@/types/ColorVariant";

export interface IPillGroup {
  pillGroupItems: IPillGroupItem[];
  activePillGroupItemIndex: number;
  setActivePillGroupItemIndex: Dispatch<SetStateAction<number>>;
  block?: boolean;
  alignVariant?: AlignVariant;
  colorVariant?: ColorVariant;
}
