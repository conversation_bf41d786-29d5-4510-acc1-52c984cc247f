"use client";

import { IPillGroup } from "./PillGroup.types";

import styles from "./PillGroup.module.scss";
import { ColorVariant } from "@/types/ColorVariant";
import classNames from "classnames";
import { AlignVariant } from "@/types/AlignVariant";
import PillGroupItem from "./components/PillGroupItem";

// PillGroup component
const PillGroup = (props: IPillGroup) => {
  // destructuring props
  const {
    activePillGroupItemIndex,
    setActivePillGroupItemIndex,
    pillGroupItems,
    alignVariant = AlignVariant.left,
    block,
    colorVariant = ColorVariant.primary,
  } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <div className={classNames(styles.pillGroup, styles[alignVariant])}>
      {pillGroupItems.map((tabPillItem, index) => (
        <PillGroupItem
          key={`TabPillItem-${index}`}
          colorVariant={colorVariant}
          active={activePillGroupItemIndex === index}
          text={tabPillItem.text}
          block={block}
          disabled={tabPillItem.disabled}
          onClick={() => {
            if (tabPillItem.onClick) {
              tabPillItem.onClick();
            } else {
              setActivePillGroupItemIndex(index);
            }
          }}
        />
      ))}
    </div>
  );
};

// export
export default PillGroup;
