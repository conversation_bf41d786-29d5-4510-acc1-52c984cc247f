"use client";
import { cloneElement } from "react";

import { IIcon } from "./Icon.types";
import { SizeVariant } from "@/types/SizeVariant";

// Icon component
const Icon = (props: IIcon) => {
  // destructuring props
  const { icon, sizeVariant = SizeVariant.default, className } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers
  const getSize = (): number => {
    switch (sizeVariant) {
      case SizeVariant.small:
        return 22;
      case SizeVariant.large:
        return 34;
      default:
        return 28;
    }
  };
  const element = cloneElement(icon, { size: getSize(), className });
  // render
  return element;
};

// export
export default Icon;
