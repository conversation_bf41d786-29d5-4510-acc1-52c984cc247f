import { AlignVariant } from "@/types/AlignVariant";
import { ColorVariant } from "@/types/ColorVariant";
import { SizeVariant } from "@/types/SizeVariant";
import { StyleVariant } from "@/types/StyleVariant";
import { ReactElement } from "react";

export interface IButton {
  id: string;
  onClick: () => void;
  text?: string | ButtonText;
  leftIcon?: ReactElement;
  rightIcon?: ReactElement;
  alignVariant?: AlignVariant;
  sizeVariant?: SizeVariant;
  colorVariant?: ColorVariant;
  styleVariant?: StyleVariant;
  disabled?: boolean;
  onlyIcon?: boolean;
  block?: boolean;
  rounded?: boolean;
  className?: string;
  type?: "button" | "submit";
  hasMinWidth?: boolean;
  htmlRef?: any;
}

export enum ButtonText {
  Cancel = "Vazgeç",
  Save = "Kaydet",
  New = "Yeni",
  Delete = "Sil",
}
