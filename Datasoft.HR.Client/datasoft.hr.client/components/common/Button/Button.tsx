import classNames from "classnames";

import { AlignVariant } from "@/types/AlignVariant";
import { ColorVariant } from "@/types/ColorVariant";
import { SizeVariant } from "@/types/SizeVariant";
import { StyleVariant } from "@/types/StyleVariant";

import styles from "./Button.module.scss";
import { IButton } from "./Button.types";
import Icon from "../Icon";

// Button component
const Button = (props: IButton) => {
  // destructuring props
  const {
    id,
    onClick,
    text,
    leftIcon,
    rightIcon,
    alignVariant = AlignVariant.center,
    colorVariant = ColorVariant.primary,
    sizeVariant = SizeVariant.default,
    styleVariant = StyleVariant.solid,
    onlyIcon,
    block,
    className,
    disabled,
    rounded,
    hasMinWidth,
    type = "button",
    htmlRef,
  } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <button
      id={id}
      onClick={onClick}
      className={classNames(
        styles.button,
        styles[alignVariant],
        styles[colorVariant],
        styles[sizeVariant],
        styles[styleVariant],
        onlyIcon && styles.onlyIcon,
        block && styles.block,
        rounded && styles.rounded,
        disabled && styles.disabled,
        hasMinWidth && styles.hasMinWidth,
        className && className
      )}
      ref={htmlRef}
      type={type}
    >
      {leftIcon && <div className={styles.icon}>{<Icon sizeVariant={sizeVariant} icon={leftIcon} />}</div>}
      {!onlyIcon && text && <span className={styles.text}>{text}</span>}
      {!onlyIcon && rightIcon && <div className={styles.icon}>{<Icon sizeVariant={sizeVariant} icon={rightIcon} />}</div>}
    </button>
  );
};

// export
export default Button;
