import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs";

import <PERSON><PERSON> from "./Button";
import { ColorVariant } from "@/types/ColorVariant";
import { AlignVariant } from "@/types/AlignVariant";
import { StyleVariant } from "@/types/StyleVariant";
import { SizeVariant } from "@/types/SizeVariant";
import { BsChevronUp, BsPerson } from "react-icons/bs";

const meta = {
  tags: ["autodocs"],
  component: Button,
  argTypes: {
    alignVariant: {
      options: Object.values(AlignVariant),
      control: { type: "inline-radio" },
    },
    colorVariant: {
      options: Object.values(ColorVariant),
      control: { type: "inline-radio" },
    },
    styleVariant: {
      options: Object.values(StyleVariant),
      control: { type: "inline-radio" },
    },
    sizeVariant: {
      options: Object.values(SizeVariant),
      control: { type: "inline-radio" },
    },
  },
} satisfies Meta<typeof Button>;

export const Default: StoryObj<typeof meta> = {
  args: {
    styleVariant: StyleVariant.solid,
    sizeVariant: SizeVariant.default,
    colorVariant: ColorVariant.primary,
    alignVariant: AlignVariant.center,
    block: false,
    disabled: false,
    onlyIcon: false,
    rounded: false,
    hasMinWidth: false,
    id: "",
    onClick: () => null,
    text: "Lorem İpsum",
    leftIcon: <BsPerson />,
    rightIcon: <BsChevronUp />,
    type: "button",
  },
};

export default meta;
