@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.button {
  display: flex;
  padding-block: 0;
  align-items: center;
  outline-width: 1px;
  outline-style: solid;
  outline-color: transparent;
  border-width: 1px;
  border-style: solid;
  border-color: transparent;
  border-radius: $border-radius-default;
  font-family: var(--font-axiforma);
  cursor: pointer;
  transition: color linear $animation-duration-small, background-color linear $animation-duration-small, border-color linear $animation-duration-small,
    outline-color linear $animation-duration-small, opacity linear $animation-duration-small;

  &:hover {
    opacity: $default-hover-opacity;
  }
}
.block {
  width: 100%;
}
.rounded {
  // sizeVariant
  &.small {
    border-radius: $button-dimension-small;
  }
  &.default {
    border-radius: $button-dimension-default;
  }
  &.large {
    border-radius: $button-dimension-large;
  }
}
.onlyIcon {
  // sizeVariant
  &.small {
    width: $button-dimension-small;
  }
  &.default {
    width: $button-dimension-default;
  }
  &.large {
    width: $button-dimension-large;
  }
}
.hasMinWidth {
  &.small {
    min-width: 12.5rem;
  }
  &.default {
    min-width: 12.5rem;
  }
  &.large {
    min-width: 12.5rem;
  }
}
// alignVariant
.center {
  align-items: center;
  justify-content: center;
  text-align: center;
}
.left {
}
.right {
  align-items: center;
  justify-content: flex-end;
  text-align: right;
}
// colorVariant
.primary {
  // styleVariant
  &.solid {
    @include button-variant-generation($color-white, $color-blue-750, $color-blue-750, $color-blue-600, $color-blue-750);
  }
  &.outlined {
    @include button-variant-generation($color-blue-750, $color-white, $color-blue-750, $color-blue-600, $color-blue-750);
  }
  &.plain {
    @include button-variant-generation($color-blue-750, $color-white, $color-white, $color-white, $color-white);
  }
}
.success {
  // styleVariant
  &.solid {
    @include button-variant-generation($color-white, $color-green-300, $color-green-300, $color-green-500, $color-green-300);
  }
  &.outlined {
    @include button-variant-generation($color-green-300, $color-white, $color-green-300, $color-green-500, $color-green-300);
  }
  &.plain {
    @include button-variant-generation($color-green-300, $color-white, $color-white, $color-white, $color-white);
  }
}
.light {
  // styleVariant
  &.solid {
    @include button-variant-generation($color-white, $color-gray-700, $color-gray-700, $color-gray-600, $color-gray-700);
  }
  &.outlined {
    @include button-variant-generation($color-gray-700, $color-white, $color-gray-250, $color-gray-250, $color-gray-700);
  }
  &.plain {
    @include button-variant-generation($color-gray-700, $color-white, $color-white, $color-white, $color-white);
  }
}
.disabled {
  // styleVariant
  &.solid {
    @include button-variant-generation($color-white, $color-gray-450, $color-gray-450, $color-gray-600, $color-gray-450);
  }
  &.outlined {
    @include button-variant-generation($color-white, $color-gray-450, $color-gray-450, $color-gray-600, $color-gray-450);
  }
  &.plain {
    @include button-variant-generation($color-gray-450, $color-white, $color-white, $color-white, $color-white);
  }
  user-select: none;
}
// styleVariant
.solid {
}
.outlined {
}
.plain {
}
// sizeVariant
.large {
  padding-inline: 1.5rem;
  gap: $gap-small;
  height: $button-dimension-large;
  @include font-16-600;
}
.default {
  padding-inline: 1rem;
  gap: $gap-small;
  height: $button-dimension-default;
  @include font-14-600;
}
.small {
  padding-inline: 1rem;
  gap: $gap-small;
  height: $button-dimension-small;
  @include font-12-600;
}

// content
.text {
}
.icon {
  display: flex;
  align-items: center;
  justify-content: center;
}
