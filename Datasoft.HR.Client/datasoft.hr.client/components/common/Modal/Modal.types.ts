import { ModalTypes } from "@/contexts/ModalContext/ModalContext.types";
import { SizeVariant } from "@/types/SizeVariant";
import { ReactNode } from "react";
import { IButton } from "../Button/Button.types";

export interface IModal {
  id: ModalTypes;
  title?: string;
  sizeVariant?: SizeVariant;
  isFooterAvailable?: boolean;
  isHeaderAvailable?: boolean;
  isFooterLeftButtonAvailable?: boolean;
  isFooterRightButtonAvailable?: boolean;
  footerLeftButton?: IButton;
  footerRightButton?: IButton;
  isHidableOnClickOverlay?: boolean;
  hasCloseButton?: boolean;
  children: ReactNode;
}
