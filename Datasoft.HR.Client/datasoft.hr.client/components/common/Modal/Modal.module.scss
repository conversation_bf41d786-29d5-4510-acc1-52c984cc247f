@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.overlay {
  position: fixed;
  display: flex;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding-inline: $gap-default;
  justify-content: center;
  background-color: rgba($color: $color-gray-650, $alpha: 0.9);
  z-index: $modal-overlay-z-index;
}
.modal {
  position: relative;
  z-index: $modal-z-index;
  top: $modal-space;
  width: 100%;
  height: max-content;
  display: flex;
  flex-direction: column;
  border-radius: $border-radius-large;
  background-color: var(--color-white);
  @include box-shadow-modal;

  @media (max-width: $breakpoint-md) {
    max-width: 100% !important;
  }
}
.header {
  padding: $modal-space;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: $gap-default;
}
.title {
  @include font-20-600;
}
.body {
  padding-inline: $modal-space;
  max-height: calc(100vh - 244px - ($modal-space * 2));
  overflow-y: auto;
  @include scrollbar-default;
}
.footer {
  padding: $modal-space;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: $gap-default;
}

// sizeVariant
.small {
  max-width: $modal-small-max-width;
}
.default {
  max-width: $modal-default-max-width;
}
.large {
  max-width: $modal-large-max-width;
}
