"use client";

import classNames from "classnames";
import { CgClose } from "react-icons/cg";
import Button from "../Button";

import useModal from "@/hooks/useModal";

import { ColorVariant } from "@/types/ColorVariant";
import { SizeVariant } from "@/types/SizeVariant";

import styles from "./Modal.module.scss";
import { IModal } from "./Modal.types";
import { StyleVariant } from "@/types/StyleVariant";

// Modal component
const Modal = (props: IModal) => {
  // destructuring props
  const {
    id,
    title,
    isHeaderAvailable = true,
    isFooterAvailable = true,
    isFooterLeftButtonAvailable = true,
    isFooterRightButtonAvailable = true,
    footerLeftButton,
    footerRightButton,
    isHidableOnClickOverlay = true,
    sizeVariant = SizeVariant.default,
    hasCloseButton = true,
    children,
  } = props;
  // state

  // context hooks
  const { hideModal } = useModal();
  // queries

  // effect

  // other variables/functions/handlers
  const handleClickOverlay = () => {
    if (isHidableOnClickOverlay) {
      hideModal(id);
    } else {
      return false;
    }
  };
  const handleClickCloseButton = () => {
    hideModal(id);
  };
  // render
  return (
    <div className={styles.overlay} onClick={handleClickOverlay}>
      <div
        id={id}
        className={classNames(styles.modal, styles[sizeVariant])}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        {isHeaderAvailable && (
          <div className={styles.header}>
            <div className={styles.title}>{title}</div>
            {hasCloseButton && (
              <Button
                id="Modal-Header-Close-Button"
                onlyIcon
                rounded
                leftIcon={<CgClose />}
                onClick={handleClickCloseButton}
                colorVariant={ColorVariant.light}
                styleVariant={StyleVariant.plain}
                sizeVariant={SizeVariant.small}
              />
            )}
          </div>
        )}
        <div className={styles.body}>{children}</div>
        {isFooterAvailable && (
          <div className={styles.footer}>
            {isFooterLeftButtonAvailable && (
              <Button
                id={`Modal-Footer-Left-Button-${id}`}
                onClick={() => null}
                styleVariant={StyleVariant.solid}
                text="Kaydet"
                hasMinWidth
                block={sizeVariant === SizeVariant.small}
                {...(footerLeftButton ? footerLeftButton : null)}
              />
            )}
            {isFooterRightButtonAvailable && (
              <Button
                id={`Modal-Footer-Right-Button-${id}`}
                onClick={handleClickCloseButton}
                text="Vazgeç"
                hasMinWidth
                styleVariant={StyleVariant.outlined}
                block={sizeVariant === SizeVariant.small}
                {...(footerRightButton ? footerRightButton : null)}
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// export
export default Modal;
