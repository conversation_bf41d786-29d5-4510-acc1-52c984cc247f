"use client";

import { ILink } from "./Link.types";
import NextLink from "next/link";
import styles from "./Link.module.scss";

// Link component
const Link = (props: ILink) => {
  // destructuring props
  const { href, text } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <NextLink href={href} className={styles.link}>
      {text}
    </NextLink>
  );
};

// export
export default Link;
