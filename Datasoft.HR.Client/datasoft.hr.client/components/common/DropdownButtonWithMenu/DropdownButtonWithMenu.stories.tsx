import type { Meta, StoryObj } from "@storybook/nextjs";

import DropdownButtonWithMenu from "./DropdownButtonWithMenu";

const meta = {
  tags: ["autodocs"],
  component: DropdownButtonWithMenu,
} satisfies Meta<typeof DropdownButtonWithMenu>;

export const Default: StoryObj<typeof meta> = {
  args: {
    id: "",
    menuItems: [
      {
        title: "Fatura (Genel Tip)",
        onClick: () => null,
      },
      {
        title: "SGK",
        onClick: () => null,
      },
      {
        title: "<PERSON><PERSON>",
        onClick: () => null,
      },
      {
        title: "İade",
        onClick: () => null,
      },
    ],
  },
};

export default meta;
