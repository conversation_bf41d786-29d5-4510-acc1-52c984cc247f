"use client";

import styles from "./DropdownButtonWithMenu.module.scss";
import { useState } from "react";
import { useFloating } from "@floating-ui/react";
import { defaultFloatingSettings } from "@/utils/floating";
import { useOutsideClick } from "@/hooks/useOutsideClick";
import Button from "../Button";
import MenuItem from "./components/MenuItem";
import classNames from "classnames";
import { IoIosArrowDown, IoIosArrowUp } from "react-icons/io";
import { IDropdownButtonWithMenu } from "./DropdownButtonWithMenu.types";

// DropdownButtonWithMenu component
const DropdownButtonWithMenu = (props: IDropdownButtonWithMenu) => {
  // destructuring props
  const { id, dropdownButton, menuItems } = props;
  // state
  const [open, setOpen] = useState<boolean>(false);
  // context hooks
  const { refs, floatingStyles } = useFloating(defaultFloatingSettings);
  const dropdownMenuRef = useOutsideClick(() => {
    setOpen(false);
  });
  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers
  const handleClick = () => {
    setOpen(!open);
  };
  // render
  return (
    <div className={classNames(styles.dropdownButtonWithMenu, open && styles.open)} ref={dropdownMenuRef}>
      <Button
        id={id}
        {...dropdownButton}
        leftIcon={dropdownButton?.leftIcon ? dropdownButton.leftIcon : open ? <IoIosArrowUp className={styles.hey} /> : <IoIosArrowDown />}
        onlyIcon={dropdownButton && dropdownButton.onlyIcon !== undefined ? dropdownButton.onlyIcon : true}
        onClick={handleClick}
        htmlRef={refs.setReference}
      />
      {open && (
        <div className={styles.dropdown} style={floatingStyles} ref={refs.setFloating}>
          {menuItems.map((menuItem, index) => {
            return <MenuItem key={`MenuItem-${id}-${index}`} {...menuItem} />;
          })}
        </div>
      )}
    </div>
  );
};

// export
export default DropdownButtonWithMenu;
