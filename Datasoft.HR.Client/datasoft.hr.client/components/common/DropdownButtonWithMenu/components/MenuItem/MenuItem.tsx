"use client";

import styles from "./MenuItem.module.scss";
import { IMenuItem } from "./MenuItem.types";

// MenuItem component
const MenuItem = (props: IMenuItem) => {
  // destructuring props
  const { title, leftIcon, onClick } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <div className={styles.menuItem} onClick={onClick}>
      {leftIcon && leftIcon}
      {title}
    </div>
  );
};

// export
export default MenuItem;
