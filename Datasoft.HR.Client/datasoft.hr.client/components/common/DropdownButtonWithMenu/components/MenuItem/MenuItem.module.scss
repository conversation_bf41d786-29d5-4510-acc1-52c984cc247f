@use "/styles/mixins.scss" as *;
@use "/styles/variables.scss" as *;

.menuItem {
  display: flex;
  align-items: center;
  gap: $gap-small;
  height: 44px;
  padding-inline: 1.375rem;
  @include font-14-400;
  background-color: var(--color-white);
  border-radius: $border-radius-default-item-tolerance;
  transition: background-color linear $animation-duration-small;
  cursor: pointer;

  &:hover {
    background-color: var(--color-blue-250);
  }
}
