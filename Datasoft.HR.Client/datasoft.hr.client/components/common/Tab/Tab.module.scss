@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.tab {
  display: flex;
  flex-direction: column;
  gap: $gap-xxlarge;
}
.header {
  display: flex;
  align-items: center;
  background-color: var(--color-white);
  padding: 0.375rem;
  border-radius: $border-radius-medium;
  border: 1px solid var(--color-gray-350);
}
.body {
}

// alignVariant
.left {
  .header {
    justify-content: flex-start;
  }
}
.center {
  .header {
    justify-content: center;
  }
}
.right {
  .header {
    justify-content: flex-end;
  }
}
