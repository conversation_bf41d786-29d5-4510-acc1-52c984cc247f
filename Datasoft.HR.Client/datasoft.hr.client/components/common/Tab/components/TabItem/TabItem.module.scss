@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.tabItem {
  position: relative;
  height: $button-dimension-small;
  padding-inline: 30px;
  font-family: var(--font-axiforma);
  @include font-12-400;
  color: var(--color-gray-750);
  border: 1px solid transparent;
  background-color: var(--color-white);
  border-radius: $border-radius-small;
  outline: none;
  cursor: pointer;
  transition: opacity linear $animation-duration-small, color linear $animation-duration-small, border-color linear $animation-duration-small;

  &:hover {
    opacity: $default-hover-opacity;
  }
}
.caret {
  opacity: 0;
  position: absolute;
  bottom: -8px;
  left: calc(50% - 15px * 0.5);
  width: 15px;
  height: 15px;
  border: 1px solid var(--color-blue-750);
  border-radius: 0;
  border-bottom-right-radius: $border-radius-small;
  border-top: none;
  border-left: none;
  background-color: var(--color-blue-200);
  transform: rotate(45deg);
  transition: opacity linear $animation-duration-small, border-color linear $animation-duration-small;
}
.active {
  @include tab-variant-generation(var(--color-blue-750), var(--color-blue-200), var(--color-blue-750));
  .caret {
    opacity: 1;
  }
}

.block {
  flex-grow: 1;
}
