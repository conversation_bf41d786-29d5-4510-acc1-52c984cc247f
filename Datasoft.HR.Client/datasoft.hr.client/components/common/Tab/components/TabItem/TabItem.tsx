"use client";

import { ITabItem } from "./TabItem.types";

import styles from "./TabItem.module.scss";
import classNames from "classnames";

// TabItem component
const TabItem = (props: ITabItem) => {
  // destructuring props
  const { text, active, onClick, block, disabled } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <button
      className={classNames(styles.tabItem, active && styles.active, disabled && styles.disabled, block && styles.block)}
      onClick={onClick}
      disabled={disabled}
    >
      {text}
      {active && <div className={styles.caret}></div>}
    </button>
  );
};

// export
export default TabItem;
