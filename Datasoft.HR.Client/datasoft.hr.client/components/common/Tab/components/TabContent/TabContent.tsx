"use client";

import { ITabContent } from "./TabContent.types";

import styles from "./TabContent.module.scss";

// TabContent component
const TabContent = (props: ITabContent) => {
  // destructuring props
  const { children } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return <div className={styles.tabContent}>{children}</div>;
};

// export
export default TabContent;
