import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";

import { AlignVariant } from "@/types/AlignVariant";
import Tab from "./Tab";
import { useState } from "react";
import TabContent from "./components/TabContent";

const meta = {
  tags: ["autodocs"],
  component: Tab,
  argTypes: {
    alignVariant: {
      options: Object.values(AlignVariant),
      control: { type: "inline-radio" },
    },
  },
} satisfies Meta<typeof Tab>;

export const Default: StoryObj<typeof meta> = {
  render: (args) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [activeTabIndex, setActiveTabIndex] = useState<number>(0);
    return (
      <Tab
        {...args}
        activeTabIndex={activeTabIndex}
        setActiveTabIndex={setActiveTabIndex}
        tabItems={[
          {
            text: "Kullanılacak Birimler",
          },
          {
            text: "<PERSON>yat Bilgileri",
          },
          {
            text: "<PERSON><PERSON>gi Bilgileri",
          },
          {
            text: "İhracat Bilgileri",
          },
        ]}
      >
        <TabContent>
          <p>
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Quaerat, dignissimos dolorum! Excepturi doloremque itaque repudiandae blanditiis quod modi
            possimus error autem, ducimus nemo perspiciatis, quae incidunt voluptates, nobis suscipit repellat.
          </p>
        </TabContent>
        <TabContent>
          <p>
            Quaerat, dignissimos dolorum! Excepturi doloremque itaque repudiandae blanditiis quod modi possimus error autem, ducimus nemo perspiciatis, quae
            incidunt voluptates, nobis suscipit repellat.
          </p>
        </TabContent>
        <TabContent>
          <p>
            Excepturi doloremque itaque repudiandae blanditiis quod modi possimus error autem, ducimus nemo perspiciatis, quae incidunt voluptates, nobis
            suscipit repellat.
          </p>
        </TabContent>
        <TabContent>
          <p>
            Aperiam minus excepturi voluptatibus natus molestias? Consequuntur eveniet molestias ullam molestiae corporis necessitatibus, dolorem optio sequi
            laboriosam impedit minus aspernatur placeat asperiores.
          </p>
        </TabContent>
      </Tab>
    );
  },
  args: {
    alignVariant: AlignVariant.left,
    block: false,
    activeTabIndex: 0,
    setActiveTabIndex: () => null,
    tabItems: [
      {
        text: "Kullanılacak Birimler",
      },
      {
        text: "Fiyat Bilgileri",
      },
      {
        text: "Vergi Bilgileri",
      },
      {
        text: "İhracat Bilgileri",
      },
    ],
    children: [],
  },
};

export default meta;
