"use client";

import { ITab } from "./Tab.types";

import styles from "./Tab.module.scss";
import TabItem from "./components/TabItem";
import classNames from "classnames";
import { AlignVariant } from "@/types/AlignVariant";

// Tab component
const Tab = (props: ITab) => {
  // destructuring props
  const { activeTabIndex, setActiveTabIndex, tabItems, children, block, alignVariant = AlignVariant.left } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <div className={classNames(styles.tab, styles[alignVariant])}>
      <div className={styles.header}>
        {tabItems.map((tabItem, index) => {
          return (
            <TabItem
              key={`TabItem-${index}`}
              active={activeTabIndex === index}
              text={tabItem.text}
              block={block}
              disabled={tabItem.disabled}
              onClick={() => {
                if (tabItem.onClick) {
                  tabItem.onClick();
                } else {
                  setActiveTabIndex(index);
                }
              }}
            />
          );
        })}
      </div>
      <div className={styles.body}>{children[activeTabIndex]}</div>
    </div>
  );
};

// export
export default Tab;
