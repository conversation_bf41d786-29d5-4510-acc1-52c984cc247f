@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.toast {
  padding: 0.25rem 0.75rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: $border-radius-medium;
  min-height: 2.5rem;
  gap: $gap-default;
  @include font-12-400;
  color: var(--color-white);
  border-width: 1px;
  border-style: solid;
  backdrop-filter: blur(4px);
}
.titleContainer {
  display: flex;
  align-items: center;
  gap: $gap-default;
}
.controls {
}

// status
.success {
  border-color: var(--color-white);
  background-color: rgba($color-green-500, 0.75);
}
.error {
  border-color: var(--color-red-300);
  background-color: rgba($color-red-300, 0.75);
}
.info {
  border-color: var(--color-yellow-250);
  background-color: rgba($color-yellow-250, 0.75);
}
.neutral {
  color: var(--color-black);
  border-color: var(--color-black);
  background-color: rgba($color-white, 0.75);
}
