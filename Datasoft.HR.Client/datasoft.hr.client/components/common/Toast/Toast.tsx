"use client";

import { IToast, ToastStatus } from "./Toast.types";

import styles from "./Toast.module.scss";
import classNames from "classnames";

// Toast component
const Toast = (props: IToast) => {
  // destructuring props
  const { text, icon, id, status = ToastStatus.error } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <div id={id} className={classNames(styles.toast, styles[status])}>
      <div className={styles.titleContainer}>
        {icon}
        <div className={styles.text}>{text}</div>
      </div>
      <div className={styles.controls}></div>
    </div>
  );
};

// export
export default Toast;
