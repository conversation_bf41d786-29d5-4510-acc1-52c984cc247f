"use client";

import dynamic from "next/dynamic";
import { IFormItem } from "./FormItem.types";
import styles from "./FormItem.module.scss";
import classNames from "classnames";
import { SizeVariant } from "@/types/SizeVariant";
import { ColumnSize } from "@/components/grid/Col/Col.types";
import Col from "@/components/grid/Col";
import Button from "@/components/common/Button";
const ButtonEdit = dynamic(() => import("./components/ButtonEdit"));

// FormItem component
const FormItem = (props: IFormItem) => {
  // destructuring props
  const {
    columnSize = ColumnSize.ColumnSize12,
    id,
    label,
    children,
    sizeVariant = SizeVariant.small,
    error,
    disabled,
    required,
    hasButtonEdit,
    buttonEditOnClick,
    className,
    prefix,
    suffix,
    prefixButton,
    suffixxButton,
  } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <Col
      columnSize={columnSize}
      className={classNames(
        styles.formItemColumn,
        (prefix || prefixButton) && styles.hasPrefix,
        prefix && prefixButton && styles.hasBothPrefix,
        (suffix || suffixxButton) && styles.hasSuffix,
        suffix && suffixxButton && styles.hasBothSuffix
      )}
    >
      {prefixButton && (
        <Button id={`${id}-AddonButton-Prefix`} sizeVariant={sizeVariant} className={classNames(styles.addonButton, styles.prefixButton)} {...prefixButton} />
      )}
      {prefix && <div className={classNames(styles.addon, styles.prefix)}>{prefix}</div>}
      <fieldset
        id={`FormItem-${id}`}
        className={classNames(
          styles.formItem,
          styles[sizeVariant],
          error && styles.error,
          disabled && styles.disabled,
          required && styles.required,
          className && className
        )}
      >
        {(label || hasButtonEdit) && (
          <legend className={styles.labelContainer}>
            {label && <div className={styles.label}>{label}</div>}
            {hasButtonEdit && <ButtonEdit onClick={buttonEditOnClick} />}
          </legend>
        )}
        {error && <div className={styles.errorContainer}>{error}</div>}
        <div className={styles.inputContainer}>{children}</div>
      </fieldset>
      {suffix && <div className={classNames(styles.addon, styles.suffix)}>{suffix}</div>}
      {suffixxButton && (
        <Button id={`${id}-AddonButton-Suffix`} sizeVariant={sizeVariant} className={classNames(styles.addonButton, styles.suffixButton)} {...suffixxButton} />
      )}
    </Col>
  );
};

// export
export default FormItem;
