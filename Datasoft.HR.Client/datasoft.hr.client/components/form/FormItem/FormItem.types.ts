import { IButton } from "@/components/common/Button/Button.types";
import { ColumnSize } from "@/components/grid/Col/Col.types";
import { AlignVariant } from "@/types/AlignVariant";
import { SizeVariant } from "@/types/SizeVariant";
import { ReactNode } from "react";

export interface IFormItem {
  columnSize: ColumnSize;
  id: string;
  name?: string;
  label?: string;
  hasButtonEdit?: boolean;
  buttonEditOnClick?: () => void;
  error?: string;
  children: ReactNode;
  className?: string;
  sizeVariant?: SizeVariant;
  alignVariant?: AlignVariant;
  inputRef?: any;
  required?: boolean;
  disabled?: boolean;
  prefix?: string;
  suffix?: string;
  prefixButton?: Omit<IButton, "id" | "sizeVariant">;
  suffixxButton?: Omit<IButton, "id" | "sizeVariant">;
}
