import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs";

import { SizeVariant } from "@/types/SizeVariant";
import FormItem from "./FormItem";
import { ColumnSize } from "@/components/grid/Col/Col.types";
import { useState } from "react";
import { AlignVariant } from "@/types/AlignVariant";
import { ColorVariant } from "@/types/ColorVariant";
import Input from "../Input";

const meta = {
  tags: ["autodocs"],
  component: FormItem,
  argTypes: {
    alignVariant: {
      options: Object.values(AlignVariant),
      control: { type: "inline-radio" },
    },
    columnSize: {
      options: Object.values(ColumnSize),
      control: { type: "inline-radio" },
    },
    sizeVariant: {
      options: Object.values(SizeVariant),
      control: { type: "inline-radio" },
    },
  },
} satisfies Meta<typeof FormItem>;

export const Default: StoryObj<typeof meta> = {
  render: (args) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [value, setValue] = useState<string>("");
    return (
      <div style={{ display: "flex" }}>
        <Input {...args} value={value} onChange={(e: any) => setValue(e.target.value)} />
      </div>
    );
  },
  args: {
    columnSize: ColumnSize.ColumnSize6,
    sizeVariant: SizeVariant.default,
    alignVariant: AlignVariant.left,
    disabled: false,
    required: false,
    hasButtonEdit: false,
    error: "",
    label: "Firma Adı",
    id: "",
    children: "",
  },
};

export const Prefix: StoryObj<typeof meta> = {
  render: (args) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [value, setValue] = useState<string>("");
    return (
      <div style={{ display: "flex" }}>
        <Input {...args} value={value} onChange={(e: any) => setValue(e.target.value)} />
      </div>
    );
  },
  args: {
    prefix: "datasoft/@username",
    columnSize: ColumnSize.ColumnSize6,
    sizeVariant: SizeVariant.default,
    alignVariant: AlignVariant.left,
    disabled: false,
    required: true,
    hasButtonEdit: true,
    placeholder: "Kullanıcı adı giriniz",
    error: "",
    label: "Kullanıcı Adı",
    id: "",
    onChange: () => null,
    value: "",
  },
};

export const Suffix: StoryObj<typeof meta> = {
  render: (args) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [value, setValue] = useState<string>("");
    return (
      <div style={{ display: "flex" }}>
        <Input {...args} value={value} onChange={(e: any) => setValue(e.target.value)} />
      </div>
    );
  },
  args: {
    suffix: "TL",
    columnSize: ColumnSize.ColumnSize6,
    sizeVariant: SizeVariant.default,
    alignVariant: AlignVariant.left,
    disabled: false,
    required: true,
    hasButtonEdit: true,
    error: "",
    label: "Miktar",
    id: "",
    children: "",
  },
};

export const PrefixAndSuffix: StoryObj<typeof meta> = {
  render: (args) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [value, setValue] = useState<string>("");
    return (
      <div style={{ display: "flex" }}>
        <Input {...args} value={value} onChange={(e: any) => setValue(e.target.value)} />
      </div>
    );
  },
  args: {
    prefix: "Havale/EFT",
    suffix: "TL",
    columnSize: ColumnSize.ColumnSize6,
    sizeVariant: SizeVariant.default,
    alignVariant: AlignVariant.left,
    disabled: false,
    required: true,
    hasButtonEdit: true,
    error: "",
    label: "Firma Adı",
    id: "",
    children: "",
  },
};

export const PrefixButton: StoryObj<typeof meta> = {
  render: (args) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [value, setValue] = useState<string>("");
    return (
      <div style={{ display: "flex" }}>
        <Input {...args} value={value} onChange={(e: any) => setValue(e.target.value)} />
      </div>
    );
  },
  args: {
    prefixButton: {
      text: "Verileri Getir",
      onClick: () => null,
    },
    columnSize: ColumnSize.ColumnSize6,
    sizeVariant: SizeVariant.default,
    alignVariant: AlignVariant.left,
    disabled: false,
    required: true,
    hasButtonEdit: true,
    error: "",
    label: "Fatura Kalemi",
    id: "",
    children: "",
  },
};

export const SuffixButton: StoryObj<typeof meta> = {
  render: (args) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [value, setValue] = useState<string>("");
    return (
      <div style={{ display: "flex" }}>
        <Input {...args} value={value} onChange={(e: any) => setValue(e.target.value)} />
      </div>
    );
  },
  args: {
    suffixxButton: {
      text: "Ara",
      colorVariant: ColorVariant.success,
      onClick: () => null,
    },
    columnSize: ColumnSize.ColumnSize6,
    sizeVariant: SizeVariant.default,
    alignVariant: AlignVariant.left,
    disabled: false,
    required: true,
    hasButtonEdit: true,
    error: "",
    label: "Fatura Kalemi",
    id: "",
    children: "",
  },
};

export const CombinedUsage: StoryObj<typeof meta> = {
  render: (args) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [value, setValue] = useState<string>("");
    return (
      <div style={{ display: "flex" }}>
        <Input {...args} value={value} onChange={(e: any) => setValue(e.target.value)} />
      </div>
    );
  },
  args: {
    prefix: "@username",
    prefixButton: {
      text: "Verileri Getir",
      colorVariant: ColorVariant.primary,
      onClick: () => null,
    },
    suffixxButton: {
      text: "Ara",
      colorVariant: ColorVariant.success,
      onClick: () => null,
    },
    suffix: "TL",
    columnSize: ColumnSize.ColumnSize6,
    sizeVariant: SizeVariant.default,
    alignVariant: AlignVariant.left,
    disabled: false,
    required: true,
    hasButtonEdit: true,
    error: "",
    label: "Fatura Kalemi",
    id: "",
    children: "",
  },
};

export default meta;
