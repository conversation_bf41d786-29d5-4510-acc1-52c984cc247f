@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.buttonEdit {
  width: 1.125rem;
  height: 1.125rem;
  color: var(--color-blue-750);
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  outline: none;
  background-color: transparent;
  cursor: pointer;
  transition: opacity linear $animation-duration-small;

  &:hover {
    opacity: $default-hover-opacity;
  }
}
