"use client";

import { IButtonEdit } from "./ButtonEdit.types";

import styles from "./ButtonEdit.module.scss";
import { FiEdit } from "react-icons/fi";
import { MouseEvent } from "react";

// ButtonEdit component
const ButtonEdit = (props: IButtonEdit) => {
  // destructuring props
  const { onClick } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <button
      className={styles.buttonEdit}
      type="button"
      onClick={(e: MouseEvent<HTMLButtonElement>) => {
        e.stopPropagation();
        onClick?.();
      }}
    >
      <FiEdit size={16} />
    </button>
  );
};

// export
export default ButtonEdit;
