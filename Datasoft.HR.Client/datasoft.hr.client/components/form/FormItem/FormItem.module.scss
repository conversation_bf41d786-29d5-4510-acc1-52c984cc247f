@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.formItemColumn {
  display: flex;
}
.formItem {
  display: flex;
  position: relative;
  flex-grow: 1;
  padding: 0;
  margin: 0;
  border: 2px solid var(--color-blue-350);
  border-radius: $border-radius-medium;
  background-color: var(--color-white);
  transition: border-color linear $animation-duration-small;

  &:focus-within {
    border-color: var(--color-green-450);
  }
}
.labelContainer {
  margin-left: 6px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: $gap-medium;
  @include font-12-500;
  line-height: 0;
  height: 0;
  z-index: $input-label-z-index;
  color: var(--color-gray-950);
}
.label {
  white-space: nowrap;
}
.required {
  .label {
    &:after {
      content: "*";
    }
  }
}
.errorContainer {
  position: absolute;
  right: 0;
  top: -$input-dimension-small;
  padding: 6px 10px;
  border-top-left-radius: $border-radius-default;
  border-top-right-radius: $border-radius-default;
  background-color: var(--color-red-300);
  color: var(--color-white);
  border: 1px solid var(--color-white);
  z-index: $error-z-index;
  white-space: nowrap;
  @include font-10-700;
}
.inputContainer {
  display: flex;
  flex-grow: 1;
}
.error {
  border-color: var(--color-red-300);

  &:focus-within {
    border-color: var(--color-red-300);
  }
}
.disabled {
  background-color: var(--color-gray-200);
}
// addon
.hasSuffix {
  .formItem {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}
.hasPrefix {
  .formItem {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
.addon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-inline: 0.75rem;
  @include font-12-400;
  color: var(--color-gray-700);
  border-radius: $border-radius-medium;
  border: 2px solid var(--color-blue-350);
}
.addonButton {
}
.prefix {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
.suffix {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}
.prefixButton {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.suffixButton {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.hasBothPrefix {
  .prefix {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: none;
  }
}
.hasBothSuffix {
  .suffix {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
  }
}
