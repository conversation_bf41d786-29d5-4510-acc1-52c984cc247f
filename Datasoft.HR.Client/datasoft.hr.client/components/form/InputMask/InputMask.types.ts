import { IInput } from "../Input/Input.types";

export interface IInputMask extends IInput {
  mask?: string;
  maskType?: MaskTypes;
  showMask?: boolean;
  maskChar?: string;
}

export const defaultMaskChar = "-";
export const datePickerMaskChar = "_";

export enum MaskTypes {
  Phone = "Phone",
  IBAN = "IBAN",
  PhoneOnlyCountryCode = "PhoneOnlyCountryCode",
  PhoneOnlyLocalField = "PhoneOnlyLocalField",
  Date = "Date",
}

export type TMaskFormats = {
  [key in `${keyof typeof MaskTypes}`]: string;
};

export const MaskFormats: TMaskFormats = {
  Phone: "+## ### ### ####",
  IBAN: "TR## #### #### #### #### #### ##",
  PhoneOnlyCountryCode: "+##",
  PhoneOnlyLocalField: "### ### ####",
  Date: "##/##/####",
};
