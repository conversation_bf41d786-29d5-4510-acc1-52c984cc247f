"use client";

import { PatternFormat } from "react-number-format";

import { defaultMask<PERSON>har, IInputMask, MaskFormats, MaskTypes } from "./InputMask.types";
import Input from "../Input/Input";

// InputMask component
const InputMask = (props: IInputMask) => {
  // destructuring props
  const { maskType = MaskTypes.Phone, mask, maskChar = defaultMaskChar, showMask, ...restProps } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return <PatternFormat customInput={Input} format={mask || MaskFormats[maskType]} mask={showMask ? maskChar : ""} {...restProps} />;
};

// export
export default InputMask;
