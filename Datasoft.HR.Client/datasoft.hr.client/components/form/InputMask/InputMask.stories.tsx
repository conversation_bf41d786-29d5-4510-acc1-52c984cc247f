import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs";

import { SizeVariant } from "@/types/SizeVariant";
import { ColumnSize } from "@/components/grid/Col/Col.types";
import { useState } from "react";
import InputMask from "./InputMask";
import { MaskTypes } from "./InputMask.types";

const meta = {
  tags: ["autodocs"],
  component: InputMask,
  argTypes: {
    maskType: {
      options: Object.values(MaskTypes),
      control: { type: "radio" },
    },
    columnSize: {
      options: Object.values(ColumnSize),
      control: { type: "inline-radio" },
    },
    sizeVariant: {
      options: Object.values(SizeVariant),
      control: { type: "inline-radio" },
    },
  },
} satisfies Meta<typeof InputMask>;

export const Default: StoryObj<typeof meta> = {
  render: (args) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [value, setValue] = useState<string>("");
    return (
      <div style={{ display: "flex" }}>
        <InputMask {...args} value={value} onChange={(e: any) => setValue(e.target.value)} />
      </div>
    );
  },
  args: {
    maskType: MaskTypes.Phone,
    showMask: true,
    maskChar: "",
    mask: "",
    columnSize: ColumnSize.ColumnSize6,
    sizeVariant: SizeVariant.default,
    disabled: false,
    required: false,
    hasButtonEdit: false,
    placeholder: "Miktar giriniz",
    error: "",
    label: "Miktar",
    id: "",
    onChange: () => null,
    value: "",
  },
};

export default meta;
