"use client";

import CreatableSelect from "react-select/creatable";
import { AlignVariant } from "@/types/AlignVariant";
import { SizeVariant } from "@/types/SizeVariant";
import classNames from "classnames";
import styles from "./../Select/Select.module.scss";
import { mapEntityArrayToOptionArray } from "@/utils/mapEntityToOption";
import { ISelectCreatable } from "./SelectCreatable.types";
import { Option } from "@/types/Option";
import FormItemHOC from "@/components/hoc/FormItemHOC";
import InputComponent from "../Select/components/InputComponent";
// SelectCreatable component
const SelectCreatable = (props: ISelectCreatable) => {
  // destructuring props
  const {
    id,
    name,
    error,
    onChange,
    onBlur,
    onFocus,
    alignVariant = AlignVariant.left,
    sizeVariant = SizeVariant.small,
    disabled,
    inputRef,
    // select creatable related props
    value,
    options = [],
    labelKeyForOptionMapping,
    valueKeyForOptionMapping,
    isMulti,
    isClearable = true,
    isSearchable = true,
    menuIsOpen,
    hideSelectedOptions = true,
    closeMenuOnSelect,
    noOptionsMessage = "Seçenek Yok",
    loadingMessage = "Yükleniyor",
    placeholder = "Seçiniz",
    formatCreateLabel = (input) => `'${input}'`,
    onCreateOption,
  } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <CreatableSelect
      id={id}
      instanceId={id}
      name={name || id}
      className={classNames(styles.select, error && styles.error, styles[sizeVariant], styles[alignVariant])}
      classNamePrefix={"reactSelect"}
      onChange={onChange as any}
      onBlur={onBlur}
      onFocus={onFocus}
      placeholder={placeholder}
      isDisabled={disabled}
      // select based features
      value={isMulti ? (value as Option[]) : (value as Option)}
      options={mapEntityArrayToOptionArray(options, labelKeyForOptionMapping, valueKeyForOptionMapping)}
      isClearable={isClearable}
      isSearchable={isSearchable}
      isMulti={isMulti}
      menuIsOpen={menuIsOpen}
      closeMenuOnSelect={closeMenuOnSelect}
      hideSelectedOptions={hideSelectedOptions}
      noOptionsMessage={() => noOptionsMessage}
      loadingMessage={() => loadingMessage}
      ref={inputRef}
      formatCreateLabel={formatCreateLabel}
      onCreateOption={onCreateOption}
      components={{
        Input: InputComponent,
      }}
    />
  );
};

// export
export default FormItemHOC(SelectCreatable);
