import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";

import { SizeVariant } from "@/types/SizeVariant";
import { ColumnSize } from "@/components/grid/Col/Col.types";
import { useState } from "react";
import InputMoney from "./InputMoney";

const meta = {
  tags: ["autodocs"],
  component: InputMoney,
  argTypes: {
    columnSize: {
      options: Object.values(ColumnSize),
      control: { type: "inline-radio" },
    },
    sizeVariant: {
      options: Object.values(SizeVariant),
      control: { type: "inline-radio" },
    },
  },
} satisfies Meta<typeof InputMoney>;

export const Default: StoryObj<typeof meta> = {
  render: (args) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [value, setValue] = useState<string>("");
    return (
      <div style={{ display: "flex" }}>
        <InputMoney {...args} value={value} onChange={(e: any) => setValue(e.target.value)} />
      </div>
    );
  },
  args: {
    hasThousandSeperator: true,
    hasDecimalSeperator: true,
    decimalScale: 2,
    fixedDecimalScale: true,
    columnSize: ColumnSize.ColumnSize6,
    sizeVariant: SizeVariant.default,
    disabled: false,
    required: false,
    hasButtonEdit: false,
    placeholder: "Miktar giriniz",
    error: "",
    label: "Miktar",
    id: "",
    onChange: () => null,
    value: "",
  },
};

export default meta;
