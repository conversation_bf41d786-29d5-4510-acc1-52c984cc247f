"use client";

import { NumericFormat } from "react-number-format";
import Input from "../Input/Input";
import { IInputMoney } from "./InputMoney.types";
import { AlignVariant } from "@/types/AlignVariant";

// InputMoney component
const InputMoney = (props: IInputMoney) => {
  // destructuring props
  const { hasThousandSeperator = true, hasDecimalSeperator = true, decimalScale = 2, fixedDecimalScale = true, ...restProps } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <NumericFormat
      customInput={Input}
      allowLeadingZeros
      thousandSeparator={hasThousandSeperator ? "." : undefined}
      decimalSeparator={hasDecimalSeperator ? "," : undefined}
      decimalScale={decimalScale}
      fixedDecimalScale={fixedDecimalScale}
      alignVariant={AlignVariant.right}
      {...restProps}
    />
  );
};

// export
export default InputMoney;
