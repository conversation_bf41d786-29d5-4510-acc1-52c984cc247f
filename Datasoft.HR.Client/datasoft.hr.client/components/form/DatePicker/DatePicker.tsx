"use client";

import { IDatePicker } from "./DatePicker.types";
import { default as ReactDatePicker } from "react-datepicker";
import { IoCalendarOutline } from "react-icons/io5";
import FormItemHOC from "@/components/hoc/FormItemHOC";
import "react-datepicker/dist/react-datepicker.css";
import styles from "./DatePicker.module.scss";
import inputStyles from "./../Input/Input.module.scss";
import classNames from "classnames";
import { SizeVariant } from "@/types/SizeVariant";
import { defaultFloatingSettings } from "@/utils/floating";
import { PatternFormat } from "react-number-format";
import { datePickerMaskChar, MaskFormats } from "../InputMask/InputMask.types";
import { DateFormats } from "@/utils/dateFormatter/enums";
import Icon from "@/components/common/Icon";
// DatePicker component
const DatePicker = (props: IDatePicker) => {
  // destructuring props
  const {
    id,
    sizeVariant = SizeVariant.small,
    onChange,
    value,
    minDate,
    maxDate,
    rangeStartDate,
    rangeEndDate,
    showMonthDropdown = true,
    showYearDropdown = true,
  } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <ReactDatePicker
      id={id}
      className={classNames(inputStyles.input, inputStyles[sizeVariant])}
      wrapperClassName={styles.wrapper}
      calendarClassName={styles.calendar}
      calendarIconClassName={classNames(styles.calendarIcon, styles[sizeVariant])}
      clearButtonClassName={styles.clearButton}
      yearClassName={() => styles.year}
      dayClassName={() => styles.day}
      monthClassName={() => styles.month}
      weekDayClassName={() => styles.weekDay}
      timeClassName={() => styles.time}
      popperClassName={styles.popper}
      popperPlacement={defaultFloatingSettings.placement}
      showPopperArrow={false}
      showMonthDropdown={showMonthDropdown}
      showYearDropdown={showYearDropdown}
      selected={value}
      icon={<Icon icon={<IoCalendarOutline />} sizeVariant={sizeVariant} />}
      showIcon
      dateFormat={DateFormats.DATE_WITH_SLASHES_FOR_DATE_PICKER}
      customInput={<PatternFormat format={MaskFormats.Date} mask={datePickerMaskChar} />}
      minDate={minDate}
      maxDate={maxDate}
      startDate={rangeStartDate}
      endDate={rangeEndDate}
      onChange={(date) => {
        onChange(date);
      }}
    />
  );
};

// export
export default FormItemHOC(DatePicker);
