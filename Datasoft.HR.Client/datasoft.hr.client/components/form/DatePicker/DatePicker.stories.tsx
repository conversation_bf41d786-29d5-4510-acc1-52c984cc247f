import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";

import { SizeVariant } from "@/types/SizeVariant";
import DatePicker from "./DatePicker";
import { ColumnSize } from "@/components/grid/Col/Col.types";
import { useState } from "react";
import { AlignVariant } from "@/types/AlignVariant";

const meta = {
  tags: ["autodocs"],
  component: DatePicker,
  argTypes: {
    columnSize: {
      options: Object.values(ColumnSize),
      control: { type: "inline-radio" },
    },
    sizeVariant: {
      options: Object.values(SizeVariant),
      control: { type: "inline-radio" },
    },
  },
} satisfies Meta<typeof DatePicker>;

export const Default: StoryObj<typeof meta> = {
  render: (args) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [value, setValue] = useState<Date | null>();
    return (
      <div style={{ display: "flex" }}>
        <DatePicker {...args} value={value} onChange={(date) => setValue(date)} />
      </div>
    );
  },
  args: {
    columnSize: ColumnSize.ColumnSize6,
    sizeVariant: SizeVariant.default,
    alignVariant: AlignVariant.left,
    disabled: false,
    required: false,
    hasButtonEdit: false,
    showMonthDropdown: true,
    showYearDropdown: true,
    error: "",
    label: "Tarih seçiniz",
    id: "",
    onChange: () => null,
    value: new Date(),
  },
};

export default meta;
