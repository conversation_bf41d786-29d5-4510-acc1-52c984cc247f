@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.popper {
  z-index: $dropdown-z-index;
}
.wrapper {
  width: 100%;
}
.calendar {
  font-family: var(--font-axiforma);
  color: var(--color-gray-950);
  @include font-12-400;
  @include dropdown-react-select;

  :global(.react-datepicker__header) {
    background-color: var(--color-blue-300);
    border-bottom: 1px solid var(--color-blue-350);
    border-top-left-radius: $border-radius-default-item-tolerance !important;
    border-top-right-radius: $border-radius-default-item-tolerance !important;
  }
  :global(.react-datepicker__current-month),
  :global(.react-datepicker-time__header),
  :global(.react-datepicker-year-header) {
    @include font-12-700;
    color: var(--color-gray-950);
  }
  :global(.react-datepicker__day--selected),
  :global(.react-datepicker__day--in-selecting-range),
  :global(.react-datepicker__day--in-range),
  :global(.react-datepicker__month-text--selected),
  :global(.react-datepicker__month-text--in-selecting-range),
  :global(.react-datepicker__month-text--in-range),
  :global(.react-datepicker__quarter-text--selected),
  :global(.react-datepicker__quarter-text--in-selecting-range),
  :global(.react-datepicker__quarter-text--in-range),
  :global(.react-datepicker__year-text--selected),
  :global(.react-datepicker__year-text--in-selecting-range),
  :global(.react-datepicker__year-text--in-range) {
    border-radius: $border-radius-small;
    background-color: var(--color-blue-750);
    color: var(--color-white);
    &:hover {
      background-color: var(--color-blue-700) !important;
    }
  }
  :global(.react-datepicker__day:not([aria-disabled="true"]):hover),
  :global(.react-datepicker__month-text:not([aria-disabled="true"]):hover),
  :global(.react-datepicker__quarter-text:not([aria-disabled="true"]):hover),
  :global(.react-datepicker__year-text:not([aria-disabled="true"]):hover) {
    border-radius: $border-radius-small;
    background-color: var(--color-gray-200);
  }
  :global(.react-datepicker__day--disabled),
  :global(.react-datepicker__month-text--disabled),
  :global(.react-datepicker__quarter-text--disabled),
  :global(.react-datepicker__year-text--disabled) {
    color: var(--color-gray-450);
  }

  :global(.react-datepicker__day--keyboard-selected) {
    background-color: var(--color-blue-300);
    color: var(--color-gray-950);
  }
  :global(.react-datepicker__day--outside-month) {
    opacity: 0.5;
  }
  :global(.react-datepicker__view-calendar-icon input) {
  }
  :global(.react-datepicker__year-option),
  :global(.react-datepicker__month-option),
  :global(.react-datepicker__month-year-option) {
    @include font-12-400;
  }
}
.calendarIcon {
  padding: 0 !important;
  width: auto;
  height: auto;
  right: 0;
  & + input {
    padding-top: 0;
    padding-left: 0.75rem;
    padding-bottom: 0;
  }
}
.year {
  cursor: initial;
}
.day {
  color: var(--color-gray-950);
  transition: color linear $animation-duration-small, background-color linear $animation-duration-small;
}

.month {
  cursor: initial;
}
.clearButton {
  cursor: initial;
}
.weekDay {
  cursor: initial;
}
.time {
  cursor: initial;
}

// sizeVariant
.small {
  top: 7px;
  right: 0.75rem;
  & + input {
    padding-right: calc(22px + 0.75rem) !important;
  }
}
.default {
  top: 9px;
  right: 0.75rem;
  & + input {
    padding-right: calc(28px + 0.75rem) !important;
  }
}
.large {
  top: 11px;
  right: 0.75rem;
  & + input {
    padding-right: calc(34px + 0.75rem) !important;
  }
}
