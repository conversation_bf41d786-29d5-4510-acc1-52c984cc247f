"use client";

import ReactSelect from "react-select";
import { ISelect, reactSelectClassNamePrefix } from "./Select.types";
import { AlignVariant } from "@/types/AlignVariant";
import { SizeVariant } from "@/types/SizeVariant";
import classNames from "classnames";
import styles from "./Select.module.scss";
import { mapEntityArrayToOptionArray } from "@/utils/mapEntityToOption";
import FormItemHOC from "@/components/hoc/FormItemHOC";
import InputComponent from "./components/InputComponent";
// Select component
const Select = (props: ISelect) => {
  // destructuring props
  const {
    id,
    name,
    error,
    value,
    onChange,
    onBlur,
    onFocus,
    alignVariant = AlignVariant.left,
    sizeVariant = SizeVariant.small,
    disabled,
    inputRef,
    // select related props
    options = [],
    labelKeyForOptionMapping,
    valueKeyForOptionMapping,
    isMulti,
    isClearable = true,
    isSearchable = true,
    menuIsOpen,
    hideSelectedOptions = false,
    closeMenuOnSelect,
    noOptionsMessage = "Seçenek Yok",
    loadingMessage = "Yükleniyor",
    placeholder = "Seçiniz",
  } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <ReactSelect
      id={id}
      instanceId={id}
      name={name || id}
      className={classNames(styles.select, error && styles.error, styles[sizeVariant], styles[alignVariant])}
      classNamePrefix={reactSelectClassNamePrefix}
      onChange={onChange as any}
      onBlur={onBlur}
      onFocus={onFocus}
      placeholder={placeholder}
      isDisabled={disabled}
      value={value}
      options={mapEntityArrayToOptionArray(options, labelKeyForOptionMapping, valueKeyForOptionMapping)}
      isClearable={isClearable}
      isSearchable={isSearchable}
      isMulti={isMulti}
      menuIsOpen={menuIsOpen}
      closeMenuOnSelect={closeMenuOnSelect}
      hideSelectedOptions={hideSelectedOptions}
      noOptionsMessage={() => noOptionsMessage}
      loadingMessage={() => loadingMessage}
      ref={inputRef}
      components={{
        Input: InputComponent,
      }}
    />
  );
};

// export
export default FormItemHOC(Select);
