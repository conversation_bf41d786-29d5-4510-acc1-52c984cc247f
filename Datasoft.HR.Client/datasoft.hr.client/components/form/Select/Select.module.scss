@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.select {
  width: 100%;
  border: none;
  outline: none;
  border-radius: 0;
  font-family: var(--font-axiforma);
  @include font-12-400;
  color: var(--color-gray-700);

  // react-select features
  :global(.#{$selectClassNamePrefix}) {
  }
  :global(.#{$selectClassNamePrefix}__container) {
    cursor: inherit;
  }
  :global(.#{$selectClassNamePrefix}__control) {
    border: none !important;
    box-shadow: none !important;
  }
  :global(.#{$selectClassNamePrefix}__control--is-focused) {
    background-color: var(--color-green-50);
  }
  :global(.#{$selectClassNamePrefix}__group) {
    cursor: inherit;
  }
  :global(.#{$selectClassNamePrefix}__group-heading) {
    cursor: inherit;
  }
  :global(.#{$selectClassNamePrefix}__indicators-container) {
    cursor: inherit;
  }
  :global(.#{$selectClassNamePrefix}__clear-indicator) {
    padding: 0 !important;
    color: var(--color-blue-1000) !important;
  }
  :global(.#{$selectClassNamePrefix}__dropdown-indicator) {
    padding: 0 !important;
    color: var(--color-blue-1000) !important;
  }
  :global(.#{$selectClassNamePrefix}__indicator-separator) {
    display: none;
  }
  :global(.#{$selectClassNamePrefix}__input-container) {
    margin: 0 !important;
    padding: 0 !important;
    color: var(--color-gray-700) !important;
  }
  :global(.#{$selectClassNamePrefix}__input) {
    margin: 0 !important;
    padding: 0 !important;
    color: var(--color-gray-700) !important;
  }
  :global(.#{$selectClassNamePrefix}__loading-indicator) {
    cursor: inherit;
  }
  :global(.#{$selectClassNamePrefix}__loading-message) {
    cursor: inherit;
  }
  :global(.#{$selectClassNamePrefix}__menu) {
    margin-bottom: 0 !important;
    @include dropdown-react-select;
  }
  :global(.#{$selectClassNamePrefix}__menu-list) {
    padding-block: 0;
    overflow-y: auto;
    @include scrollbar-default;
  }
  :global(.#{$selectClassNamePrefix}__menu-portal) {
    cursor: inherit;
  }
  :global(.#{$selectClassNamePrefix}__multi-value) {
    background-color: var(--color-blue-300) !important;
  }
  :global(.#{$selectClassNamePrefix}__multi-value__label) {
    color: var(--color-blue-750) !important;
    font-weight: $font-weight-700;
    padding: 0px 4px !important;
  }
  :global(.#{$selectClassNamePrefix}__multi-value__remove) {
    color: var(--color-blue-750) !important;
    padding: 0px !important;
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    transition: color linear $animation-duration-small, background-color linear $animation-duration-small;
    cursor: pointer;

    &:hover {
      color: var(--color-white) !important;
      background-color: var(--color-blue-750) !important;
    }
  }
  :global(.#{$selectClassNamePrefix}__no-options-message) {
    cursor: inherit;
  }
  :global(.#{$selectClassNamePrefix}__option) {
    color: var(--color-gray-700) !important;
    transition: background-color linear $animation-duration-small, color linear $animation-duration-small;

    &:first-child {
      border-top-left-radius: $border-radius-default-item-tolerance;
      border-top-right-radius: $border-radius-default-item-tolerance;
    }
    &:last-child {
      border-bottom-left-radius: $border-radius-default-item-tolerance;
      border-bottom-right-radius: $border-radius-default-item-tolerance;
    }
  }
  :global(.#{$selectClassNamePrefix}__option--is-selected) {
    color: var(--color-white) !important;
    background-color: var(--color-blue-750) !important;
  }
  :global(.#{$selectClassNamePrefix}__option--is-focused) {
    color: var(--color-gray-700) !important;
    background-color: var(--color-blue-50) !important;
  }
  :global(.#{$selectClassNamePrefix}__option--is-focused):global(.#{$selectClassNamePrefix}__option--is-selected) {
    color: var(--color-white) !important;
    background-color: var(--color-blue-700) !important;
  }
  :global(.#{$selectClassNamePrefix}__placeholder) {
    cursor: inherit;
  }
  :global(.#{$selectClassNamePrefix}__single-value) {
    color: var(--color-gray-700) !important;
  }
  :global(.#{$selectClassNamePrefix}__value-container) {
    padding: 0 0.75rem !important;
  }
}
// states
.error {
  :global(.#{$selectClassNamePrefix}__control--is-focused) {
    background-color: var(--color-red-50);
  }
}

// sizeVariant
.small {
  :global(.#{$selectClassNamePrefix}__control) {
    min-height: $input-dimension-small !important;
  }
}
.default {
  :global(.#{$selectClassNamePrefix}__control) {
    min-height: $input-dimension-default !important;
  }
}
.large {
  :global(.#{$selectClassNamePrefix}__control) {
    min-height: $input-dimension-large !important;
  }
}

// alignVariant
.left {
  :global(.#{$selectClassNamePrefix}__control) {
    text-align: left !important;
  }
}
.center {
  :global(.#{$selectClassNamePrefix}__control) {
    text-align: center !important;
  }
}
.right {
  :global(.#{$selectClassNamePrefix}__control) {
    text-align: right !important;
  }
}
