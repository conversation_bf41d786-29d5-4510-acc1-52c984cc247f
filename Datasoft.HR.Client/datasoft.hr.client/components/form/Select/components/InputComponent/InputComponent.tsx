// THIS COMPONENT IS FOR FIXING THE HYDRATION ISSUE OF REACT-SELECT
// PLEASE CHECK THE SOLUTION BELOW
// fix for ssr & csr hydration error and contributed to react-select
// https://github.com/JedWatson/react-select/issues/5459#issuecomment-3010993879
"use client";
import { components } from "react-select";

// InputComponent component
const InputComponent = (props: any) => {
  // destructuring props

  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  const newProps = {
    ...props,
    ["aria-activedescendant"]: undefined,
  };
  return <components.Input {...newProps} />;
};

// export
export default InputComponent;
