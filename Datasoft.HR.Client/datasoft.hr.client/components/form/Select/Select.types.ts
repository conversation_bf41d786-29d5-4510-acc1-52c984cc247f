import { Option } from "@/types/Option";
import { IFormItem } from "../FormItem/FormItem.types";

export interface SelectBase extends Omit<IFormItem, "children"> {
  labelKeyForOptionMapping: string | string[];
  valueKeyForOptionMapping: string;
  onBlur?: any;
  onFocus?: any;
  options?: any[];
  isClearable?: boolean;
  isSearchable?: boolean;
  hideSelectedOptions?: boolean;
  menuIsOpen?: boolean;
  closeMenuOnSelect?: boolean;
  noOptionsMessage?: string;
  loadingMessage?: string;
  placeholder?: string;
}

export interface ISelectMulti extends SelectBase {
  isMulti: true;
  value?: Option[] | null;
  onChange: (options: Option[]) => void;
}
export interface ISelectSingle extends SelectBase {
  isMulti?: false | undefined;
  value?: Option | null;
  onChange: (option: Option) => void;
}

export type ISelect = ISelectSingle | ISelectMulti;

export const reactSelectClassNamePrefix = "reactSelect";
