"use client";

import { AsyncPaginate } from "react-select-async-paginate";
import { GroupBase, OptionsOrGroups } from "react-select";
import { AlignVariant } from "@/types/AlignVariant";
import { SizeVariant } from "@/types/SizeVariant";
import classNames from "classnames";
import styles from "./../Select/Select.module.scss";
import { mapEntityArrayToOptionArray } from "@/utils/mapEntityToOption";
import { ISelectAsync } from "./SelectAsync.types";
import { Option } from "@/types/Option";
import { defaultDebounceTimeout } from "@/utils/constants";
import FormItemHOC from "@/components/hoc/FormItemHOC";
import InputComponent from "../Select/components/InputComponent";
import { reactSelectClassNamePrefix } from "../Select/Select.types";

// SelectAsync component
const SelectAsync = (props: ISelectAsync) => {
  // destructuring props
  const {
    id,
    name,
    error,
    onChange,
    onBlur,
    onFocus,
    alignVariant = AlignVariant.left,
    sizeVariant = SizeVariant.small,
    disabled,
    inputRef,
    // select async related props
    value,
    labelKeyForOptionMapping,
    valueKeyForOptionMapping,
    isMulti,
    isClearable = true,
    isSearchable = true,
    menuIsOpen,
    hideSelectedOptions = true,
    closeMenuOnSelect,
    noOptionsMessage = "Seçenek Yok",
    loadingMessage = "Yükleniyor",
    searchFn,
    defaultOptions,
    placeholder = "Seçiniz",
  } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers
  const loadOptions = async (
    searchTerm: string,
    prevOptions: OptionsOrGroups<Option, GroupBase<Option>>,
    additional: {
      pageNumber: number;
    } = { pageNumber: 1 }
  ) => {
    const response = await searchFn(searchTerm, additional.pageNumber);
    return {
      options: mapEntityArrayToOptionArray(response.data, labelKeyForOptionMapping, valueKeyForOptionMapping),
      hasMore: response.data.length > 0,
      additional: {
        pageNumber: additional.pageNumber + 1,
      },
    };
  };
  // render
  return (
    <AsyncPaginate
      id={id}
      instanceId={id}
      name={name || id}
      className={classNames(styles.select, error && styles.error, styles[sizeVariant], styles[alignVariant])}
      classNamePrefix={reactSelectClassNamePrefix}
      onChange={onChange as any}
      onBlur={onBlur}
      onFocus={onFocus}
      placeholder={placeholder}
      isDisabled={disabled}
      value={value}
      isClearable={isClearable}
      isSearchable={isSearchable}
      isMulti={isMulti}
      menuIsOpen={menuIsOpen}
      closeMenuOnSelect={closeMenuOnSelect}
      hideSelectedOptions={hideSelectedOptions}
      noOptionsMessage={() => noOptionsMessage}
      selectRef={inputRef}
      loadingMessage={() => loadingMessage}
      debounceTimeout={defaultDebounceTimeout}
      defaultOptions={defaultOptions}
      loadOptions={loadOptions}
      components={{
        Input: InputComponent,
      }}
    />
  );
};

// export
export default FormItemHOC(SelectAsync);
