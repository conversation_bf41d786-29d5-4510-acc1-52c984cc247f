import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs";

import { <PERSON><PERSON><PERSON>ariant } from "@/types/SizeVariant";
import SelectAsync from "./SelectAsync";
import { ColumnSize } from "@/components/grid/Col/Col.types";
import { AlignVariant } from "@/types/AlignVariant";

const meta = {
  tags: ["autodocs"],
  component: SelectAsync,
  argTypes: {
    columnSize: {
      options: Object.values(ColumnSize),
      control: { type: "inline-radio" },
    },
    sizeVariant: {
      options: Object.values(SizeVariant),
      control: { type: "inline-radio" },
    },
  },
} satisfies Meta<typeof SelectAsync>;

export const Default: StoryObj<typeof meta> = {
  args: {
    columnSize: ColumnSize.ColumnSize6,
    sizeVariant: SizeVariant.default,
    alignVariant: AlignVariant.left,
    disabled: false,
    required: false,
    hasButtonEdit: false,
    placeholder: "<PERSON>rma ad<PERSON> giri<PERSON>",
    error: "",
    label: "<PERSON><PERSON>",
    id: "",
    onChange: () => null,
    labelKeyForOptionMapping: "name",
    valueKeyForOptionMapping: "id",
    options: [
      {
        id: "123",
        name: "asdf",
        surname: "",
        age: "",
      },
      {
        id: "435",
        name: "sdfgf",
        surname: "",
        age: "",
      },
      {
        id: "4355asdfasdf",
        name: "İÖÇŞĞÜ - türkçe karakter deneme",
        surname: "",
        age: "",
      },
      {
        id: "13",
        name: "İSMET",
        surname: "",
        age: "",
      },
      {
        id: "4355",
        name: "erov",
        surname: "",
        age: "",
      },
      {
        id: "54",
        name: "iaş",
        surname: "",
        age: "",
      },
      {
        id: "26",
        name: "nd",
        surname: "",
        age: "",
      },
      {
        id: "77",
        name: "sdf",
        surname: "",
        age: "",
      },
      {
        id: "55",
        name: "bgr",
        surname: "",
        age: "",
      },
      {
        id: "44",
        name: "xcv",
        surname: "",
        age: "",
      },
      {
        id: "33",
        name: "wg",
        surname: "",
        age: "",
      },
      {
        id: "22",
        name: "gsd",
        surname: "",
        age: "",
      },
      {
        id: "435512",
        name: "asdşfkkj",
        surname: "",
        age: "",
      },
      {
        id: "43553254",
        name: "asdşfkkj",
        surname: "",
        age: "",
      },
      {
        id: "43552111",
        name: "asdşfkkj",
        surname: "",
        age: "",
      },
      {
        id: "43553331",
        name: "asdşfkkj",
        surname: "",
        age: "",
      },
      {
        id: "43555124",
        name: "asdşfkkj",
        surname: "",
        age: "",
      },
      {
        id: "43532455",
        name: "asdşfkkj",
        surname: "",
        age: "",
      },
    ],
  },
};

export default meta;
