import { ISelect } from "../Select/Select.types";

export type ISelectAsync = ISelect & {
  searchFn: (searchTerm: string, pageNumber: number) => Promise<any>;
  defaultOptions?: boolean;
};

export interface SearchInput {
  data: any;
  pagingOptions?: PagingOptionsInput;
}
export interface DataInput {
  code: string | null;
  name: string | null;
}
export interface PagingOptionsInput {
  enablePaging: true;
  pageNumber: number;
  pageSize: number;
}

export const defaultPagingOptionValues: PagingOptionsInput = {
  enablePaging: true,
  pageNumber: 1,
  pageSize: 10,
};

export const defaultDataValues: DataInput = {
  code: null,
  name: null,
};

export const defaultSearchInputValues: SearchInput = {
  data: defaultDataValues,
  pagingOptions: defaultPagingOptionValues,
};
