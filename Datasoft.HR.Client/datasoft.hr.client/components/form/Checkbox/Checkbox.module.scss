@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.checkbox {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.input {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;

  &:checked + .checkmark {
    border-color: var(--color-green-500);
    background-color: var(--color-green-500);
    .checkIcon {
      display: initial;
    }
  }
}
.checkmark {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--color-gray-550);
  border-radius: $border-radius-small-item-tolerance;
  background-color: var(--color-white);
  z-index: $checkbox-checkmark-z-index;
  transition: background-color linear $animation-duration-small, border-color linear $animation-duration-small;
}
.checkIcon {
  display: none;
  color: var(--color-white);
}
.checked {
  border-color: var(--color-green-500);
  background-color: var(--color-green-500);
}
.label {
  @include font-12-500;
  color: var(--color-gray-1000);
}
.rounded {
  &.small {
    .checkmark {
      border-radius: $input-checkbox-checkmark-dimension-small;
    }
  }
  &.default {
    .checkmark {
      border-radius: $input-checkbox-checkmark-dimension-default;
    }
  }
  &.large {
    .checkmark {
      border-radius: $input-checkbox-checkmark-dimension-large;
    }
  }
}

// sizeVariant
.large {
  gap: $gap-default;
  height: $input-checkbox-dimension-large;

  .checkmark {
    width: $input-checkbox-checkmark-dimension-large;
    height: $input-checkbox-checkmark-dimension-large;
  }
}
.default {
  gap: $gap-default;
  height: $input-checkbox-dimension-default;

  .checkmark {
    width: $input-checkbox-checkmark-dimension-default;
    height: $input-checkbox-checkmark-dimension-default;
  }
}
.small {
  gap: $gap-small;
  height: $input-checkbox-dimension-small;

  .checkmark {
    width: $input-checkbox-checkmark-dimension-small;
    height: $input-checkbox-checkmark-dimension-small;
  }
}
