"use client";

import { CheckboxType, ICheckbox } from "./Checkbox.types";

import { FaCheck } from "react-icons/fa";

import styles from "./Checkbox.module.scss";
import classNames from "classnames";
import { SizeVariant } from "@/types/SizeVariant";

// Checkbox component
const Checkbox = (props: ICheckbox) => {
  // destructuring props
  const {
    id,
    name,
    value,
    label,
    onChange,
    checked,
    className,
    disabled,
    inputRef,
    type = CheckboxType.Checkbox,
    rounded,
    sizeVariant = SizeVariant.small,
  } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <label className={classNames(styles.checkbox, rounded && styles.rounded, styles[sizeVariant], className && className)} htmlFor={id}>
      <input
        id={id}
        name={name || id}
        className={styles.input}
        type={type}
        value={value}
        disabled={disabled}
        checked={checked}
        onChange={onChange}
        ref={inputRef}
      />
      <div className={classNames(styles.checkmark, checked && styles.checked)}>
        <FaCheck className={styles.checkIcon} size={12} />
      </div>
      <div className={styles.label}>{label}</div>
    </label>
  );
};

// export
export default Checkbox;
