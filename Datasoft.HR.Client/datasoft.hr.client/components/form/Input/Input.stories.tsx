import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs";

import { SizeVariant } from "@/types/SizeVariant";
import Input from "./Input";
import { ColumnSize } from "@/components/grid/Col/Col.types";
import { useState } from "react";
import { AlignVariant } from "@/types/AlignVariant";

const meta = {
  tags: ["autodocs"],
  component: Input,
  argTypes: {
    columnSize: {
      options: Object.values(ColumnSize),
      control: { type: "inline-radio" },
    },
    sizeVariant: {
      options: Object.values(SizeVariant),
      control: { type: "inline-radio" },
    },
  },
} satisfies Meta<typeof Input>;

export const Default: StoryObj<typeof meta> = {
  render: (args) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [value, setValue] = useState<string>("");
    return (
      <div style={{ display: "flex" }}>
        <Input {...args} value={value} onChange={(e: any) => setValue(e.target.value)} />
      </div>
    );
  },
  args: {
    columnSize: ColumnSize.ColumnSize6,
    sizeVariant: SizeVariant.default,
    alignVariant: AlignVariant.left,
    disabled: false,
    required: false,
    hasButtonEdit: false,
    placeholder: "Firma adı giriniz",
    error: "",
    label: "Firma Adı",
    id: "",
    onChange: () => null,
    value: "",
  },
};

export default meta;
