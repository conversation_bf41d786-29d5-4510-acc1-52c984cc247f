@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.input {
  width: 100%;
  border: none;
  outline: none;
  padding-inline: 0.75rem;
  font-family: var(--font-axiforma);
  @include font-12-400;
  color: var(--color-gray-700);
  border-radius: $border-radius-medium-item-tolerance;
  transition: background-color linear $animation-duration-small;

  &:focus {
    background-color: var(--color-green-50);
  }
  &:disabled,
  &.disabled {
    user-select: none;
  }
}
// states
.error {
  &:focus {
    background-color: var(--color-red-50);
  }
}
// sizeVariant
.small {
  height: $input-dimension-small;
}
.default {
  height: $input-dimension-default;
}
.large {
  height: $input-dimension-large;
}

// alignVariant
.left {
  text-align: left;
}
.center {
  text-align: center;
}
.right {
  text-align: right;
}
