"use client";

import { IInput } from "./Input.types";

import styles from "./Input.module.scss";
import classNames from "classnames";
import { SizeVariant } from "@/types/SizeVariant";
import { AlignVariant } from "@/types/AlignVariant";
import FormItemHOC from "@/components/hoc/FormItemHOC";

// Input component
const Input = (props: IInput) => {
  // destructuring props
  const {
    id,
    name,
    error,
    value,
    onChange,
    onBlur,
    onFocus,
    placeholder,
    sizeVariant = SizeVariant.small,
    alignVariant = AlignVariant.left,
    disabled,
    inputRef,
  } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <input
      id={id}
      name={name || id}
      className={classNames(styles.input, error && styles.error, styles[sizeVariant], styles[alignVariant])}
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      onFocus={onFocus}
      placeholder={placeholder}
      disabled={disabled}
      ref={inputRef}
      autoComplete="off"
    />
  );
};

// export
export default FormItemHOC(Input);
