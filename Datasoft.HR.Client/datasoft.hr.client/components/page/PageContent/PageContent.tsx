"use client";

import { IPageContent } from "./PageContent.types";
import { BsArrowLeft } from "react-icons/bs";
import styles from "./PageContent.module.scss";
import Field from "@/components/common/Field";
import { ColorVariant } from "@/types/ColorVariant";
import classNames from "classnames";
import ContainerFullwidth from "@/components/grid/ContainerFullwidth";
import Button from "@/components/common/Button";
import { StyleVariant } from "@/types/StyleVariant";
import Row from "@/components/grid/Row";

// PageContent component
const PageContent = (props: IPageContent) => {
  // destructuring props
  const { colorVariant = ColorVariant.light, title, subtitle, controls, fields, children } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <ContainerFullwidth className={classNames(styles.pageContent, styles[colorVariant])}>
      <ContainerFullwidth className={styles.header}>
        <Button id="PageContent-Button-GoBack" leftIcon={<BsArrowLeft />} onlyIcon onClick={() => {}} styleVariant={StyleVariant.plain} />
        <div className={styles.titleContainer}>
          {subtitle && <div className={styles.subtitle}>{subtitle}</div>}
          <div className={styles.title}>{title}</div>
        </div>
        {fields && (
          <div className={styles.fieldContainer}>
            {fields.map((field, index) => (
              <Field key={`PageContent-Field-${index}`} {...field} />
            ))}
          </div>
        )}
        {controls && <div className={styles.controls}>{controls}</div>}
      </ContainerFullwidth>
      <ContainerFullwidth className={styles.body}>
        <Row>{children}</Row>
      </ContainerFullwidth>
    </ContainerFullwidth>
  );
};

// export
export default PageContent;
