@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.pageContent {
  position: relative;
  display: flex;
  flex-direction: column;
}
.header {
  padding: $pagecontent-padding-block $pagecontent-padding-inline;
  display: flex;
  align-items: flex-start;
  gap: $gap-default;
}
.icon {
}
.titleContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}
.title {
  @include font-18-600;
}
.subtitle {
  @include font-14-500;
}
.fieldContainer {
}
.controls {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: $gap-xlarge;
}
.body {
  padding: $pagecontent-padding-block $pagecontent-padding-inline;
}
.footer {
}

// colorVariant
.light {
  @include pageContent-variant-generation("&", $color-blue-750, $color-black, $color-white, $color-white, $color-white);
}
.success {
  @include pageContent-variant-generation("&", $color-white, $color-white, $color-green-300, $color-white, $color-white);
}
.primary {
  @include pageContent-variant-generation("&", $color-white, $color-white, $color-blue-750, $color-white, $color-white);
}
