"use client";

import { IToastContainer } from "./ToastContainer.types";

import styles from "./ToastContainer.module.scss";
import Toast from "@/components/common/Toast";

// ToastContainer component
const ToastContainer = (props: IToastContainer) => {
  // destructuring props
  const { toasts } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <div className={styles.toastContainer}>
      {toasts.map((toast, index) => (
        <Toast key={`Toast-${index}`} {...toast} />
      ))}
    </div>
  );
};

// export
export default ToastContainer;
