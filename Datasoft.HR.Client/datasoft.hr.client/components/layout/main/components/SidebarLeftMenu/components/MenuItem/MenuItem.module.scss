@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.menuItem {
  width: 100%;
}
.button {
  padding: 0.75rem 1.5rem;
  border: none;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: var(--font-axiforma);
  color: var(--color-gray-700);
  text-decoration: none;
  @include font-14-400;
  border-radius: $border-radius-default;
  background-color: var(--color-white);
  transition: background-color linear $animation-duration-small, color linear $animation-duration-small, box-shadow linear $animation-duration-small;
  cursor: pointer;
  user-select: none;

  &.open {
    color: var(--color-white);
    background-color: var(--color-primary);
    @include box-shadow-medium;

    &:hover {
      color: var(--color-white);
    }
    .icon {
      transform: rotate(90deg);
    }
  }
}
.titleContainer {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}
.open {
}
.submenu {
  padding-block: 1rem;
  display: flex;
  flex-direction: column;
}
