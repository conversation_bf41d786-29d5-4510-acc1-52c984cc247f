"use client";

import { IMenuItem } from "./MenuItem.types";

import styles from "./MenuItem.module.scss";
import { MouseEvent } from "react";
import SubmenuItem from "../SubmenuItem";
import { IoIosArrowForward } from "react-icons/io";
import classNames from "classnames";
import { useSidebarLeft } from "@/hooks/useSidebarLeft";

// MenuItem component
const MenuItem = (props: IMenuItem) => {
  // destructuring props
  const { id, title, leftIcon, href, hasSubmenu, submenu } = props;
  // state

  // context hooks
  const { activeMenuItemId, setActiveMenuItemId } = useSidebarLeft();
  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers
  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {
    if (!href && submenu) {
      e.preventDefault();
      setActiveMenuItemId(activeMenuItemId === id ? null : id);
    }
  };

  // render
  return (
    <div className={styles.menuItem}>
      <a href={href} onClick={handleClick} className={classNames(styles.button, activeMenuItemId === id && styles.open)}>
        <div className={styles.titleContainer}>
          {leftIcon && leftIcon}
          {title}
        </div>
        {hasSubmenu && <IoIosArrowForward size={18} className={styles.icon} />}
      </a>
      {!href && hasSubmenu && activeMenuItemId === id && (
        <div className={styles.submenu}>
          {submenu?.map((submenuItem) => {
            return <SubmenuItem key={`MenuItem-${id}-SubmenuItem-${submenuItem.id}`} {...submenuItem} />;
          })}
        </div>
      )}
    </div>
  );
};

// export
export default MenuItem;
