@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.submenuItem {
  width: 262px;
  padding-left: 58px;
  @include font-12-500;
  line-height: 30px;
  color: var(--color-gray-700);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-decoration: none;
  user-select: none;
  cursor: pointer;
  border-radius: $border-radius-xlarge;
  transition: background-color linear $animation-duration-small, color linear $animation-duration-small;

  &:hover {
    color: var(--color-primary);
  }
  &.active {
    color: var(--color-primary);
    background-color: var(--color-blue-50);
  }
}
