"use client";

import { ISidebarLeftMenu } from "./SidebarLeftMenu.types";
import { BsHouse, BsLayoutTextWindowReverse } from "react-icons/bs";
import { BiAdjust } from "react-icons/bi";

import styles from "./SidebarLeftMenu.module.scss";
import MenuItem from "./components/MenuItem";
import { routes } from "@/routes";
import { IMenuItem } from "./components/MenuItem/MenuItem.types";

// SidebarLeftMenu component
const SidebarLeftMenu = (props: ISidebarLeftMenu) => {
  // destructuring props
  const {} = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers
  const menuItems: IMenuItem[] = [
    {
      id: "hello",
      title: "Merhaba",
      leftIcon: <BsHouse size={34} />,
      href: routes.home.url,
    },
    {
      id: "currents",
      title: "<PERSON><PERSON><PERSON>",
      leftIcon: <BsHouse size={34} />,
      hasSubmenu: true,
      submenu: [
        {
          id: "currents-currentAccount",
          title: "Cari",
          href: "",
        },
        {
          id: "currents-case",
          title: "Kasa",
          href: "",
        },
        {
          id: "currents-bankAccount",
          title: "Banka Hesabı",
          href: "",
        },
        {
          id: "currents-costCenter",
          title: "Masraf Merkezi",
          href: "",
        },
        {
          id: "currents-creditCards",
          title: "Kredi Kartları",
          href: "",
        },
        {
          id: "currents-posDevices",
          title: "POSlar",
          href: "",
        },
        {
          id: "currents-employees",
          title: "Personeller",
          href: "",
        },
      ],
    },
    {
      id: "stockTransferVirmans",
      title: "Stok Devir ve Virmanlar",
      leftIcon: <BiAdjust size={34} />,
      hasSubmenu: true,
      submenu: [
        {
          id: "reports-currentAccountBalance",
          title: "Cari Bakiye Raporu",
          href: "#",
        },
        {
          id: "reports-currentAccountActivity",
          title: "Cari Hareketleri Raporu",
          href: "#",
        },
        {
          id: "reports-caseBalance",
          title: "Kasa Bakiye Raporu",
          href: "#",
        },
        {
          id: "reports-caseActivity",
          title: "Kasa Hareketleri Raporu",
          href: "#",
        },
        {
          id: "reports-bankAccountBalance",
          title: "Banka Hesabı Bakiye Raporu",
          href: "#",
        },
        {
          id: "reports-bankAccountActivity",
          title: "Banka Hesabı Hareketleri Raporu",
          href: "#",
        },
        {
          id: "reports-buySell",
          title: "Vergi Bazlı Alış Satış Raporu",
          href: "",
        },
        {
          id: "reports-stockInventory",
          title: "Stok Envanter Raporu",
          href: "",
        },
        {
          id: "reports-stockActivity",
          title: "Stok Hareketleri Raporu",
          href: "",
        },
      ],
    },
    {
      id: "reports",
      title: "Raporlar",
      leftIcon: <BsLayoutTextWindowReverse size={34} />,
      hasSubmenu: true,
      submenu: [
        {
          id: "reports-currentAccountBalance",
          title: "Cari Bakiye Raporu",
          href: "#",
        },
        {
          id: "reports-currentAccountActivity",
          title: "Cari Hareketleri Raporu",
          href: "#",
        },
        {
          id: "reports-caseBalance",
          title: "Kasa Bakiye Raporu",
          href: "#",
        },
        {
          id: "reports-caseActivity",
          title: "Kasa Hareketleri Raporu",
          href: "#",
        },
        {
          id: "reports-bankAccountBalance",
          title: "Banka Hesabı Bakiye Raporu",
          href: "#",
        },
        {
          id: "reports-bankAccountActivity",
          title: "Banka Hesabı Hareketleri Raporu",
          href: "#",
        },
        {
          id: "reports-buySell",
          title: "Vergi Bazlı Alış Satış Raporu",
          href: "",
        },
        {
          id: "reports-stockInventory",
          title: "Stok Envanter Raporu",
          href: "",
        },
        {
          id: "reports-stockActivity",
          title: "Stok Hareketleri Raporu",
          href: "",
        },
      ],
    },
  ];
  // render
  return (
    <div className={styles.sidebarLeftMenu}>
      {menuItems.map((menuItem) => (
        <MenuItem key={`SidebarLeftMenu-MenuItem-${menuItem.id}`} {...menuItem} />
      ))}
    </div>
  );
};

// export
export default SidebarLeftMenu;
