@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.sidebarLeft {
  position: absolute;
  top: 0;
  width: $sidebar-left-width;
  height: calc(100vh - $header-height);
  padding: 1.5rem;
  display: flex;
  gap: $gap-default;
  background-color: var(--color-white);
  z-index: $layout-sidebar-left-z-index;
  // overflow-y: auto;
  @include scrollbar-default;
  animation-duration: $animation-duration-small;
}
.open {
  left: 0;
}
.closed {
  left: -$sidebar-left-width;
}
