"use client";

import { IToggleButton } from "./ToggleButton.types";

import styles from "./ToggleButton.module.scss";
import { IoIosArrowBack } from "react-icons/io";
import classNames from "classnames";

// ToggleButton component
const ToggleButton = (props: IToggleButton) => {
  // destructuring props
  const { onClick, open } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <button className={classNames(styles.toggleButton, !open && styles.closed)} type="button" onClick={onClick}>
      <IoIosArrowBack size={22} className={styles.icon} />
    </button>
  );
};

// export
export default ToggleButton;
