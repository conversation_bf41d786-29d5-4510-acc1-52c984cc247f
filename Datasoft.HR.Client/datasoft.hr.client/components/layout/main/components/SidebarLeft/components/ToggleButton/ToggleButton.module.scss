@use "/styles/variables.scss" as *;

.toggleButton {
  position: absolute;
  right: -30px;
  top: calc(50% - 15px);
  width: 30px;
  height: 60px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  outline: none;
  color: var(--color-white);
  background-color: var(--color-primary);
  border-top-right-radius: $border-radius-default;
  border-bottom-right-radius: $border-radius-default;
  z-index: $layout-sidebar-left-z-index;
  cursor: pointer;
}
.closed {
  .icon {
    transform: rotate(180deg);
  }
}
