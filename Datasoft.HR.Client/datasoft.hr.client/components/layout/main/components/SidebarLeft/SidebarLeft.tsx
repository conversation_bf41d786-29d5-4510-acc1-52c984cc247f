"use client";
import { useSidebarLeft } from "@/hooks/useSidebarLeft";
import SidebarLeftMenu from "../SidebarLeftMenu";
import styles from "./SidebarLeft.module.scss";
import ToggleButton from "./components/ToggleButton";
import classNames from "classnames";
import { useOutsideClick } from "@/hooks/useOutsideClick";

// SidebarLeft component
const SidebarLeft = () => {
  // destructuring props

  // state

  // context hooks
  const { sidebarLeftOpen, setSidebarLeftOpen } = useSidebarLeft();
  const sidebarLeftRef = useOutsideClick(() => {
    setSidebarLeftOpen(false);
  });
  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <div className={classNames(styles.sidebarLeft, sidebarLeftOpen ? styles.open : styles.closed)} ref={sidebarLeftRef}>
      <ToggleButton open={sidebarLeftOpen} onClick={() => setSidebarLeftOpen(!sidebarLeftOpen)} />
      <SidebarLeftMenu />
      {/* search field */}
    </div>
  );
};

// export
export default SidebarLeft;
