import ContainerFullwidth from "@/components/grid/ContainerFullwidth";
import styles from "./Wrapper.module.scss";
import { IWrapper } from "./Wrapper.types";

// Wrapper component
const Wrapper = (props: IWrapper) => {
  // destructuring props
  const { children } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return <ContainerFullwidth className={styles.wrapper}>{children}</ContainerFullwidth>;
};

// export
export default Wrapper;
