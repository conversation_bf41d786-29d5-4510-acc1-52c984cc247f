import { BiUser } from "react-icons/bi";
import { IHeaderMenu } from "./HeaderMenu.types";

import styles from "./HeaderMenu.module.scss";

import { externalProjectRoutes } from "@/routes";
import MenuItem from "./components/MenuItem";
import Col from "@/components/grid/Col";

// HeaderMenu component
const HeaderMenu = (props: IHeaderMenu) => {
  // destructuring props
  const {} = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <Col columnSize="10" className={styles.headerMenu}>
      <MenuItem id="HeaderMenuItem-BuyPackage" title="Paket Satın Al" href={externalProjectRoutes.packages} />
      <MenuItem
        id="HeaderMenuItem-User"
        leftIcon={<BiUser size={24} />}
        title="Erel Çolak"
        hasSubmenu
        submenu={[
          {
            id: "HeaderMenuItem-User-UserInfo",
            title: "Kullanıcı Bilgilerim",
            href: externalProjectRoutes.userinfo,
          },
          {
            id: "HeaderMenuItem-User-ChangePassword",
            title: "Şifre Değiştir",
            href: externalProjectRoutes.changepassword,
          },
          {
            id: "HeaderMenuItem-User-Licenses",
            title: "Lisanslarım",
            href: externalProjectRoutes.license,
          },
          {
            id: "HeaderMenuItem-User-BuyCredit",
            title: "TCKN/VKN Sorgulama Kontör Satın Al",
            href: externalProjectRoutes.buyCredit,
          },
          {
            id: "HeaderMenuItem-User-SalesHistory",
            title: "Satış Geçmişi",
            href: externalProjectRoutes.saleshistory,
          },
          {
            id: "HeaderMenuItem-User-Agreements",
            title: "Sözleşmelerim",
            href: externalProjectRoutes.agreements,
          },
          {
            id: "HeaderMenuItem-User-Logout",
            title: "Çıkış",
            href: externalProjectRoutes.logout,
          },
        ]}
      />
      <MenuItem id="HeaderMenuItem-GoToSettings" title="Ayarlar Menüsüne Git" href={externalProjectRoutes.pre} />
    </Col>
  );
};

// export
export default HeaderMenu;
