import styles from "./MenuItem.module.scss";
import { useFloating } from "@floating-ui/react";
import { MouseEvent, useState } from "react";
import { useOutsideClick } from "@/hooks/useOutsideClick";
import { defaultFloatingSettings } from "@/utils/floating";
import classNames from "classnames";
import { IoIosArrowDown } from "react-icons/io";
import { IMenuItem } from "./MenuItem.types";
import SubmenuItem from "../SubmenuItem";

// MenuItem component
const MenuItem = (props: IMenuItem) => {
  // destructuring props
  const { id, title, leftIcon, rightIcon, href, hasSubmenu, submenu } = props;
  // state
  const [open, setOpen] = useState<boolean>(false);
  // context hooks
  const { refs, floatingStyles } = useFloating(defaultFloatingSettings);
  const menuItemRef = useOutsideClick(() => {
    setOpen(false);
  });
  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers
  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {
    if (!href && submenu) {
      e.preventDefault();
      setOpen(!open);
    }
  };
  // render
  return (
    <div className={styles.menuItem} ref={menuItemRef}>
      <a href={href} onClick={handleClick} className={classNames(styles.button, open && styles.open)} ref={refs.setReference}>
        {leftIcon && leftIcon}
        {title}
        {rightIcon && rightIcon}
        {hasSubmenu && <IoIosArrowDown size={18} className={classNames(styles.icon)} />}
      </a>
      {!href && hasSubmenu && open && (
        <div className={styles.dropdown} style={floatingStyles} ref={refs.setFloating}>
          {submenu?.map((submenuItem) => (
            <SubmenuItem key={`MenuItem-${id}-SubmenuItem-${submenuItem.id}`} {...submenuItem} />
          ))}
        </div>
      )}
    </div>
  );
};

// export
export default MenuItem;
