@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.submenuItem {
  height: 44px;
  padding-inline: 1.25rem;
  border-bottom: 1px solid var(--color-gray-300);
  display: flex;
  align-items: center;
  color: var(--color-gray-700);
  @include font-12-400;
  text-decoration: none;
  white-space: nowrap;
  transition: background-color linear $animation-duration-default;
  cursor: pointer;
  user-select: none;

  &:hover {
    background-color: var(--color-blue-50);
  }
  &:first-child {
    border-top-left-radius: $border-radius-default-item-tolerance;
    border-top-right-radius: $border-radius-default-item-tolerance;
  }
  &:last-child {
    border-bottom: none;
    border-bottom-left-radius: $border-radius-default-item-tolerance;
    border-bottom-right-radius: $border-radius-default-item-tolerance;
  }
}
