@use "/styles/variables.scss" as *;
@use "/styles/mixins.scss" as *;

.menuItem {
  position: relative;
}
.button {
  height: 3rem;
  padding-inline: 1rem;
  border: none;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $gap-xsmall;
  font-family: var(--font-axiforma);
  color: var(--color-gray-700);
  text-decoration: none;
  @include font-12-500;
  background-color: transparent;
  transition: background-color linear $animation-duration-default;
  cursor: pointer;
  user-select: none;
  border-left: 1px solid var(--color-gray-250);

  &:hover {
    background-color: var(--color-blue-50);
  }
  &.open {
    color: var(--color-primary);

    .icon {
      transform: rotate(180deg);
    }
  }
}
.dropdown {
  @include dropdown-header-menu-item;
}
