import { ISubmenuItem } from "./SubmenuItem.types";

import styles from "./SubmenuItem.module.scss";

// SubmenuItem component
const SubmenuItem = (props: ISubmenuItem) => {
  // destructuring props
  const { id, title, href } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <a className={styles.submenuItem} href={href}>
      {title}
    </a>
  );
};

// export
export default SubmenuItem;
