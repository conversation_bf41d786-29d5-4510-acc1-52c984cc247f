"use client";

import { IMainLayout } from "./MainLayout.types";

import styles from "./MainLayout.module.scss";
import Header from "./components/Header";
import Body from "./components/Body";
import Wrapper from "./components/Wrapper";
import SidebarLeft from "./components/SidebarLeft";
import ToastContainer from "./components/ToastContainer";
import useToast from "@/hooks/useToast";

// MainLayout component
const MainLayout = (props: IMainLayout) => {
  // destructuring props
  const { children } = props;
  // state

  // context hooks
  const { toasts } = useToast();
  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <div className={styles.mainLayout}>
      <Header />
      <Wrapper>
        <SidebarLeft />
        <Body>
          {toasts.length > 0 && <ToastContainer toasts={toasts} />}
          {children}
        </Body>
      </Wrapper>
    </div>
  );
};

// export
export default MainLayout;
