import { IContainer } from "./Container.types";

import styles from "./Container.module.scss";
import classNames from "classnames";

// Container component
const Container = (props: IContainer) => {
  // destructuring props
  const { children, className } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return <div className={classNames(styles.container, className && className)}>{children}</div>;
};

// export
export default Container;
