import { IContainerFullwidth } from "./ContainerFullwidth.types";

import styles from "./ContainerFullwidth.module.scss";
import classNames from "classnames";

// ContainerFullwidth component
const ContainerFullwidth = (props: IContainerFullwidth) => {
  // destructuring props
  const { children, className } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return <div className={classNames(styles.containerFullwidth, className && className)}>{children}</div>;
};

// export
export default ContainerFullwidth;
