import classNames from "classnames";
import styles from "./Row.module.scss";
import { IRow } from "./Row.types";

// Row component
const Row = (props: IRow) => {
  // destructuring props
  const { children, className } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return <div className={classNames(styles.row, className && className)}>{children}</div>;
};

// export
export default Row;
