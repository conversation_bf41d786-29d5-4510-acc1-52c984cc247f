import { ColumnSize, ICol } from "./Col.types";

import styles from "./Col.module.scss";
import classNames from "classnames";

// Col component
const Col = (props: ICol) => {
  // destructuring props
  const { columnSize = ColumnSize.ColumnSize12, className, children } = props;
  // state

  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return <div className={classNames(styles.col, styles[`col-${columnSize}`], className && className)}>{children}</div>;
};

// export
export default Col;
