import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";

import Col from "./Col";
import { ColumnSize } from "./Col.types";
import Row from "../Row";

const meta = {
  tags: ["autodocs"],
  component: Col,
  argTypes: {
    columnSize: {
      options: Object.values(ColumnSize),
      control: { type: "inline-radio" },
    },
  },
} satisfies Meta<typeof Col>;

export const Default: StoryObj<typeof meta> = {
  render: (args) => {
    return (
      <Row>
        <Col {...args}>
          <p>
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Quos inventore quo voluptas nihil doloremque ut, ipsam veritatis soluta rem, necessitatibus
            ratione animi itaque error culpa quaerat ab placeat quibusdam sit?
          </p>
        </Col>
      </Row>
    );
  },
  args: {
    columnSize: ColumnSize.ColumnSize12,
    children: null,
  },
};

export default meta;
