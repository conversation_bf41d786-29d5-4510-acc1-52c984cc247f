import { ReactNode } from "react";

export interface ICol {
  columnSize?: ColumnSize;
  className?: string;
  children: ReactNode;
}

export enum ColumnSize {
  ColumnSize1 = "1",
  ColumnSize2 = "2",
  ColumnSize3 = "3",
  ColumnSize4 = "4",
  ColumnSize5 = "5",
  ColumnSize6 = "6",
  ColumnSize7 = "7",
  ColumnSize8 = "8",
  ColumnSize9 = "9",
  ColumnSize10 = "10",
  ColumnSize11 = "11",
  ColumnSize12 = "12",
}
