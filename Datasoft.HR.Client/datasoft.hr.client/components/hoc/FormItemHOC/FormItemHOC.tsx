import FormItem from "@/components/form/FormItem";
import { IFormItem } from "@/components/form/FormItem/FormItem.types";
import { ComponentType } from "react";

const FormItemHOC = <P extends Omit<IFormItem, "children">>(WrappedInputComponent: ComponentType<P>) => {
  const ComponentWithFormItem = (props: P) => {
    const {
      id,
      columnSize,
      name,
      label,
      error,
      sizeVariant,
      alignVariant,
      required,
      className,
      inputRef,
      disabled,
      hasButtonEdit,
      buttonEditOnClick,
      prefix,
      suffix,
      prefixButton,
      suffixxButton,
      ...rest
    } = props;

    return (
      <FormItem
        id={id}
        columnSize={columnSize}
        name={name}
        label={label}
        error={error}
        sizeVariant={sizeVariant}
        alignVariant={alignVariant}
        required={required}
        className={className}
        inputRef={inputRef}
        disabled={disabled}
        hasButtonEdit={hasButtonEdit}
        buttonEditOnClick={buttonEditOnClick}
        prefix={prefix}
        suffix={suffix}
        prefixButton={prefixButton}
        suffixxButton={suffixxButton}
      >
        <WrappedInputComponent {...(rest as P)} id={id} name={name} disabled={disabled} inputRef={inputRef} />
      </FormItem>
    );
  };

  ComponentWithFormItem.displayName = `FormItemHOC(${WrappedInputComponent.displayName})`;
  return ComponentWithFormItem;
};

export default FormItemHOC;
