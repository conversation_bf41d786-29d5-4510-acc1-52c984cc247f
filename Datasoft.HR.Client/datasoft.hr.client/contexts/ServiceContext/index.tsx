"use client";

import { serviceOptions } from "@/swagger/api";
import { axiosInstance } from "@/utils/axios";
import { createSyncStoragePersister } from "@tanstack/query-sync-storage-persister";
import { QueryClient } from "@tanstack/react-query";
import { PersistQueryClientProvider } from "@tanstack/react-query-persist-client";
import { createContext } from "react";
import { IServiceContext, IServiceContextProvider } from "./ServiceContext.types";

const ServiceContext = createContext<IServiceContext | null>(null);
// define an axios instance for api calls
serviceOptions.axios = axiosInstance;
// define default queryClient
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 0,
    },
  },
});
// define localStorage persister for query caching
const localStoragePersister = createSyncStoragePersister({
  storage: typeof window !== "undefined" ? window.localStorage : undefined,
  key: "offlineCache",
});

// ServiceContextProvider component
const ServiceContextProvider = (props: IServiceContextProvider) => {
  // destructuring props
  const { children } = props;
  // render
  return (
    <PersistQueryClientProvider
      client={queryClient}
      persistOptions={{
        persister: localStoragePersister,
        dehydrateOptions: {
          shouldDehydrateQuery(query) {
            if (query.queryKey.includes("GetProfile")) {
              return true;
            }
            return false;
          },
        },
      }}
    >
      <ServiceContext.Provider value={{ queryClient }}>{children}</ServiceContext.Provider>
    </PersistQueryClientProvider>
  );
};

// export
export { ServiceContext, ServiceContextProvider };
