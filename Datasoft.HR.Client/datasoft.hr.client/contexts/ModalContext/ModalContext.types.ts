import { Dispatch, ReactNode, SetStateAction } from "react";

export interface IModalContextProvider {
  children: ReactNode;
}
export interface IModalContext {
  modals: TModals;
  setModals: Dispatch<SetStateAction<TModals>>;
  modalData: TModalData;
  setModalData: Dispatch<SetStateAction<TModalData>>;
  modalStack: any[];
  setModalStack: Dispatch<SetStateAction<any[]>>;
}

/**
 * <AUTHOR>
 * @description define a unique modal id for every modal
 * @enum {ModalTypes}
 */
export enum ModalTypes {
  ModalDefault = "ModalDefault",
  ModalDynamicSample = "ModalDynamicSample",
  ModalSmall = "ModalSmall",
  ModalLarge = "ModalLarge",
}

/**
 * <AUTHOR>
 * @description  define type of data for each ModalType that will be used for showing modals
 * @type {ModalDataMapping}
 */
export type ModalDataMapping = {
  [ModalTypes.ModalDefault]: { id: string; name: "" };
  [ModalTypes.ModalDynamicSample]: { id: string };
  [ModalTypes.ModalSmall]: { id: string };
  [ModalTypes.ModalLarge]: { id: string };
};

export type BaseModalDataObjectType<T> = {
  data: T;
  callback?: () => void;
};

export type TModalData = {
  [Key in ModalTypes]: BaseModalDataObjectType<ModalDataMapping[Key]>;
};

export type TModals = Record<ModalTypes, boolean>;

export const initialValuesModals: TModals = Object.fromEntries(Object.values(ModalTypes).map((modalType) => [modalType, false])) as Record<ModalTypes, boolean>;

export const initialValuesModalData: TModalData = {
  ModalDefault: {
    data: {
      id: "",
      name: "",
    },
  },
  ModalDynamicSample: {
    data: {
      id: "",
    },
  },
  ModalLarge: {
    data: {
      id: "",
    },
  },
  ModalSmall: {
    data: {
      id: "",
    },
  },
};

export const initialValuesModalContext: IModalContext = {
  modals: initialValuesModals,
  setModals: () => {},
  modalData: initialValuesModalData,
  setModalData: () => {},
  modalStack: [],
  setModalStack: () => {},
};
