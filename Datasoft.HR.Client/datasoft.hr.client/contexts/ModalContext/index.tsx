"use client";

import { createContext, useState } from "react";
import { IModalContext, IModalContextProvider, initialValuesModalContext, TModalData, TModals } from "./ModalContext.types";
import dynamic from "next/dynamic";
const ModalContext = createContext<IModalContext>(initialValuesModalContext);

const ModalDefault = dynamic(() => import("@/components/modals/ModalDefault"));

// ModalContextProvider component
const ModalContextProvider = (props: IModalContextProvider) => {
  // destructuring props
  const { children } = props;
  // state
  const [modals, setModals] = useState<TModals>(initialValuesModalContext.modals);
  const [modalData, setModalData] = useState<TModalData>(initialValuesModalContext.modalData);
  const [modalStack, setModalStack] = useState<any[]>([]);
  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <ModalContext.Provider
      value={{
        modals,
        setModals,
        modalData,
        setModalData,
        modalStack,
        setModalStack,
      }}
    >
      {children}
      {modals.ModalDefault && <ModalDefault />}
    </ModalContext.Provider>
  );
};

// export
export { ModalContext, ModalContextProvider };
