"use client";
import { createContext, useState } from "react";

import { IToastContext, IToastContextProvider } from "./ToastContext.types";
import { IToast } from "@/components/common/Toast/Toast.types";

const ToastContext = createContext<IToastContext>({
  toasts: [],
  setToasts: () => null,
});

// ToastContextProvider component
const ToastContextProvider = (props: IToastContextProvider) => {
  // destructuring props
  const { children } = props;
  // state
  const [toasts, setToasts] = useState<IToast[]>([]);
  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers

  // render
  return (
    <ToastContext.Provider
      value={{
        toasts,
        setToasts,
      }}
    >
      {children}
    </ToastContext.Provider>
  );
};

// export
export { ToastContext, ToastContextProvider };
