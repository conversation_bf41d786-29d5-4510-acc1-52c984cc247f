"use client";
import { createContext, useEffect, useState } from "react";

import { ISidebarLeftContext, ISidebarLeftContextProvider } from "./SidebarLeftContext.types";
import { LocalStorageItemSidebarLeftOpen } from "@/utils/constants";
import { setSidebarLeftOpenToLocalStorage } from "@/utils/localStorage";

const SidebarLeftContext = createContext<ISidebarLeftContext>({
  sidebarLeftOpen: true,
  setSidebarLeftOpen: () => null,
  activeMenuItemId: null,
  setActiveMenuItemId: () => null,
});

// SidebarLeftContextProvider component
const SidebarLeftContextProvider = (props: ISidebarLeftContextProvider) => {
  // destructuring props
  const { children } = props;
  // state
  const [sidebarLeftOpen, setSidebarLeftOpen] = useState<boolean>(false);
  const [activeMenuItemId, setActiveMenuItemId] = useState<string | null>(null);
  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers
  useEffect(() => {
    if (localStorage.getItem(LocalStorageItemSidebarLeftOpen)) {
      const isOpen = JSON.parse(localStorage.getItem(LocalStorageItemSidebarLeftOpen) as string) || false;
      setSidebarLeftOpen(isOpen);
    }
  }, []);
  useEffect(() => {
    setSidebarLeftOpenToLocalStorage(sidebarLeftOpen);
  }, [sidebarLeftOpen]);
  // render
  return (
    <SidebarLeftContext.Provider
      value={{
        sidebarLeftOpen,
        setSidebarLeftOpen,
        activeMenuItemId,
        setActiveMenuItemId,
      }}
    >
      {children}
    </SidebarLeftContext.Provider>
  );
};

// export
export { SidebarLeftContext, SidebarLeftContextProvider };
