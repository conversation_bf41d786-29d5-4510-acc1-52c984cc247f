@use "/styles/variables.scss" as *;
@use "sass:math";

// # typography
@mixin font-10-100 {
  font-size: 0.625rem;
  font-weight: 100;
}
@mixin font-10-200 {
  font-size: 0.625rem;
  font-weight: 200;
}
@mixin font-10-300 {
  font-size: 0.625rem;
  font-weight: 300;
}
@mixin font-10-400 {
  font-size: 0.625rem;
  font-weight: 400;
}
@mixin font-10-500 {
  font-size: 0.625rem;
  font-weight: 500;
}
@mixin font-10-600 {
  font-size: 0.625rem;
  font-weight: 600;
}
@mixin font-10-700 {
  font-size: 0.625rem;
  font-weight: 700;
}
@mixin font-10-800 {
  font-size: 0.625rem;
  font-weight: 800;
}
@mixin font-10-900 {
  font-size: 0.625rem;
  font-weight: 900;
}
@mixin font-10-1000 {
  font-size: 0.625rem;
  font-weight: 1000;
}
@mixin font-12-100 {
  font-size: 0.75rem;
  font-weight: 100;
}
@mixin font-12-200 {
  font-size: 0.75rem;
  font-weight: 200;
}
@mixin font-12-300 {
  font-size: 0.75rem;
  font-weight: 300;
}
@mixin font-12-400 {
  font-size: 0.75rem;
  font-weight: 400;
}
@mixin font-12-500 {
  font-size: 0.75rem;
  font-weight: 500;
}
@mixin font-12-600 {
  font-size: 0.75rem;
  font-weight: 600;
}
@mixin font-12-700 {
  font-size: 0.75rem;
  font-weight: 700;
}
@mixin font-12-800 {
  font-size: 0.75rem;
  font-weight: 800;
}
@mixin font-12-900 {
  font-size: 0.75rem;
  font-weight: 900;
}
@mixin font-12-1000 {
  font-size: 0.75rem;
  font-weight: 1000;
}
@mixin font-14-100 {
  font-size: 0.875rem;
  font-weight: 100;
}
@mixin font-14-200 {
  font-size: 0.875rem;
  font-weight: 200;
}
@mixin font-14-300 {
  font-size: 0.875rem;
  font-weight: 300;
}
@mixin font-14-400 {
  font-size: 0.875rem;
  font-weight: 400;
}
@mixin font-14-500 {
  font-size: 0.875rem;
  font-weight: 500;
}
@mixin font-14-600 {
  font-size: 0.875rem;
  font-weight: 600;
}
@mixin font-14-700 {
  font-size: 0.875rem;
  font-weight: 700;
}
@mixin font-14-800 {
  font-size: 0.875rem;
  font-weight: 800;
}
@mixin font-14-900 {
  font-size: 0.875rem;
  font-weight: 900;
}
@mixin font-14-1000 {
  font-size: 0.875rem;
  font-weight: 1000;
}
@mixin font-16-100 {
  font-size: 1rem;
  font-weight: 100;
}
@mixin font-16-200 {
  font-size: 1rem;
  font-weight: 200;
}
@mixin font-16-300 {
  font-size: 1rem;
  font-weight: 300;
}
@mixin font-16-400 {
  font-size: 1rem;
  font-weight: 400;
}
@mixin font-16-500 {
  font-size: 1rem;
  font-weight: 500;
}
@mixin font-16-600 {
  font-size: 1rem;
  font-weight: 600;
}
@mixin font-16-700 {
  font-size: 1rem;
  font-weight: 700;
}
@mixin font-16-800 {
  font-size: 1rem;
  font-weight: 800;
}
@mixin font-16-900 {
  font-size: 1rem;
  font-weight: 900;
}
@mixin font-16-1000 {
  font-size: 1rem;
  font-weight: 1000;
}
@mixin font-18-100 {
  font-size: 1.125rem;
  font-weight: 100;
}
@mixin font-18-200 {
  font-size: 1.125rem;
  font-weight: 200;
}
@mixin font-18-300 {
  font-size: 1.125rem;
  font-weight: 300;
}
@mixin font-18-400 {
  font-size: 1.125rem;
  font-weight: 400;
}
@mixin font-18-500 {
  font-size: 1.125rem;
  font-weight: 500;
}
@mixin font-18-600 {
  font-size: 1.125rem;
  font-weight: 600;
}
@mixin font-18-700 {
  font-size: 1.125rem;
  font-weight: 700;
}
@mixin font-18-800 {
  font-size: 1.125rem;
  font-weight: 800;
}
@mixin font-18-900 {
  font-size: 1.125rem;
  font-weight: 900;
}
@mixin font-18-1000 {
  font-size: 1.125rem;
  font-weight: 1000;
}
@mixin font-20-100 {
  font-size: 1.25rem;
  font-weight: 100;
}
@mixin font-20-200 {
  font-size: 1.25rem;
  font-weight: 200;
}
@mixin font-20-300 {
  font-size: 1.25rem;
  font-weight: 300;
}
@mixin font-20-400 {
  font-size: 1.25rem;
  font-weight: 400;
}
@mixin font-20-500 {
  font-size: 1.25rem;
  font-weight: 500;
}
@mixin font-20-600 {
  font-size: 1.25rem;
  font-weight: 600;
}
@mixin font-20-700 {
  font-size: 1.25rem;
  font-weight: 700;
}
@mixin font-20-800 {
  font-size: 1.25rem;
  font-weight: 800;
}
@mixin font-20-900 {
  font-size: 1.25rem;
  font-weight: 900;
}
@mixin font-20-1000 {
  font-size: 1.25rem;
  font-weight: 1000;
}
@mixin font-22-100 {
  font-size: 1.375rem;
  font-weight: 100;
}
@mixin font-22-200 {
  font-size: 1.375rem;
  font-weight: 200;
}
@mixin font-22-300 {
  font-size: 1.375rem;
  font-weight: 300;
}
@mixin font-22-400 {
  font-size: 1.375rem;
  font-weight: 400;
}
@mixin font-22-500 {
  font-size: 1.375rem;
  font-weight: 500;
}
@mixin font-22-600 {
  font-size: 1.375rem;
  font-weight: 600;
}
@mixin font-22-700 {
  font-size: 1.375rem;
  font-weight: 700;
}
@mixin font-22-800 {
  font-size: 1.375rem;
  font-weight: 800;
}
@mixin font-22-900 {
  font-size: 1.375rem;
  font-weight: 900;
}
@mixin font-22-1000 {
  font-size: 1.375rem;
  font-weight: 1000;
}
@mixin font-24-100 {
  font-size: 1.5rem;
  font-weight: 100;
}
@mixin font-24-200 {
  font-size: 1.5rem;
  font-weight: 200;
}
@mixin font-24-300 {
  font-size: 1.5rem;
  font-weight: 300;
}
@mixin font-24-400 {
  font-size: 1.5rem;
  font-weight: 400;
}
@mixin font-24-500 {
  font-size: 1.5rem;
  font-weight: 500;
}
@mixin font-24-600 {
  font-size: 1.5rem;
  font-weight: 600;
}
@mixin font-24-700 {
  font-size: 1.5rem;
  font-weight: 700;
}
@mixin font-24-800 {
  font-size: 1.5rem;
  font-weight: 800;
}
@mixin font-24-900 {
  font-size: 1.5rem;
  font-weight: 900;
}
@mixin font-24-1000 {
  font-size: 1.5rem;
  font-weight: 1000;
}
@mixin font-28-100 {
  font-size: 1.75rem;
  font-weight: 100;
}
@mixin font-28-200 {
  font-size: 1.75rem;
  font-weight: 200;
}
@mixin font-28-300 {
  font-size: 1.75rem;
  font-weight: 300;
}
@mixin font-28-400 {
  font-size: 1.75rem;
  font-weight: 400;
}
@mixin font-28-500 {
  font-size: 1.75rem;
  font-weight: 500;
}
@mixin font-28-600 {
  font-size: 1.75rem;
  font-weight: 600;
}
@mixin font-28-700 {
  font-size: 1.75rem;
  font-weight: 700;
}
@mixin font-28-800 {
  font-size: 1.75rem;
  font-weight: 800;
}
@mixin font-28-900 {
  font-size: 1.75rem;
  font-weight: 900;
}
@mixin font-28-1000 {
  font-size: 1.75rem;
  font-weight: 1000;
}
@mixin font-32-100 {
  font-size: 2rem;
  font-weight: 100;
}
@mixin font-32-200 {
  font-size: 2rem;
  font-weight: 200;
}
@mixin font-32-300 {
  font-size: 2rem;
  font-weight: 300;
}
@mixin font-32-400 {
  font-size: 2rem;
  font-weight: 400;
}
@mixin font-32-500 {
  font-size: 2rem;
  font-weight: 500;
}
@mixin font-32-600 {
  font-size: 2rem;
  font-weight: 600;
}
@mixin font-32-700 {
  font-size: 2rem;
  font-weight: 700;
}
@mixin font-32-800 {
  font-size: 2rem;
  font-weight: 800;
}
@mixin font-32-900 {
  font-size: 2rem;
  font-weight: 900;
}
@mixin font-32-1000 {
  font-size: 2rem;
  font-weight: 1000;
}
@mixin font-36-100 {
  font-size: 2.25rem;
  font-weight: 100;
}
@mixin font-36-200 {
  font-size: 2.25rem;
  font-weight: 200;
}
@mixin font-36-300 {
  font-size: 2.25rem;
  font-weight: 300;
}
@mixin font-36-400 {
  font-size: 2.25rem;
  font-weight: 400;
}
@mixin font-36-500 {
  font-size: 2.25rem;
  font-weight: 500;
}
@mixin font-36-600 {
  font-size: 2.25rem;
  font-weight: 600;
}
@mixin font-36-700 {
  font-size: 2.25rem;
  font-weight: 700;
}
@mixin font-36-800 {
  font-size: 2.25rem;
  font-weight: 800;
}
@mixin font-36-900 {
  font-size: 2.25rem;
  font-weight: 900;
}
@mixin font-36-1000 {
  font-size: 2.25rem;
  font-weight: 1000;
}

// # grid
// col
@mixin calculate-col-size($size) {
  flex: 0 0 (math.div($size, 12) * 100%);
  max-width: math.div($size, 12) * 100%;

  @media (max-width: $breakpoint-md) {
    flex-basis: 100%;
    max-width: 100%;
  }
}
@mixin calculate-column-gutter($gutter-x, $gutter-y) {
  padding-inline: calc($gutter-x * 0.5);
  margin-bottom: $gutter-y;
}

// # shadows
// large
@mixin box-shadow-large {
  box-shadow: rgba(0, 0, 0, 0.05) 0px 40px 40px 0px;
}
// default
@mixin box-shadow-default {
  box-shadow: rgba(0, 0, 0, 0.05) 0px 4px 4px 0px;
}
// medium
@mixin box-shadow-medium {
  box-shadow: rgba(0, 0, 0, 0.25) 0px 20px 30px 0px;
}
@mixin box-shadow-form-item-default {
  box-shadow: rgba(0, 0, 0, 0.25) 0px 20px 30px 0px;
}
@mixin box-shadow-tab-item-active {
  box-shadow: rgba(0, 0, 0, 0.25) 0 10px 15px;
}
@mixin box-shadow-modal {
  box-shadow: rgba(0, 0, 0, 0.25) 0 10px 20px;
}

// # scrollbar
// scrollbar-default
@mixin scrollbar-default {
  // shadow dom
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  &::-webkit-scrollbar-track {
    background-color: $color-gray-100;
    border-radius: 8px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: $color-gray-200;
    border-radius: 8px;

    // pseudo
    &:hover {
      background-color: $color-gray-300;
    }
  }
}

// # dropdown
// dropdown
@mixin dropdown-header-menu-item {
  border-radius: $border-radius-default;
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  z-index: $dropdown-z-index;
  @include box-shadow-default();
}
@mixin dropdown-button-with-menu {
  width: max-content;
  border-radius: $border-radius-default;
  background-color: var(--color-white);
  border: 1px solid var(--color-primary);
  outline: 1px solid var(--color-primary);
  z-index: $dropdown-z-index;
  @include box-shadow-default();
}
@mixin dropdown-react-select {
  border-radius: $border-radius-default;
  background-color: var(--color-white);
  border: 1px solid var(--color-blue-350);
  outline: none;
  z-index: $dropdown-z-index;
  @include box-shadow-default();
}

// # pageContent
@mixin pageContent-variant-generation($selector, $title-color, $subtitle-color, $background-color, $field-label-color, $field-value-color) {
  #{$selector} {
    .header {
      background-color: $background-color;
      .title {
        color: $title-color;
      }
      .subtitle {
        color: $subtitle-color;
      }
    }
  }
}

// # button
@mixin button-variant-generation($color, $background-color, $border-color, $hover-border-color, $hover-outline-color) {
  color: $color;
  background-color: $background-color;
  border-color: $border-color;

  &:hover {
    border-color: $hover-border-color;
    outline-color: $hover-outline-color;
  }
}

// # buttonGroup
// first child
@mixin buttonGroup-firstChildStyles {
  border-right: none;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
// last child
@mixin buttonGroup-lastChildStyles {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
// middle children
@mixin buttonGroup-betweenChildrenStyles {
  border-radius: 0;
  border-right: none;
}

// # tab
@mixin tab-variant-generation($active-color, $active-background, $active-border-color) {
  color: $active-color;
  background-color: $active-background;
  border-color: $active-border-color;
  z-index: $tab-item-z-index;
  @include box-shadow-tab-item-active;
}
// financial status
@mixin financial-status-card {
  display: flex;
  flex-direction: column;
  padding: 2rem;
  gap: 2rem;
  background-color: var(--color-white);
  border-left: 1px solid var(--color-gray-250);

  @media (max-width: $breakpoint-md) {
    border: none;
  }
}
