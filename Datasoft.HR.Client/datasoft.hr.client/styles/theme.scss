@use "/styles/variables.scss" as *;

:root {
  // base
  --color-black: #{$color-black};
  --color-white: #{$color-white};
  --color-primary: #{$color-blue-750};
  --color-success: #{$color-green-300};
  --color-light: #{$color-gray-700};
  // gray
  --color-gray-1250: #{$color-gray-1250};
  --color-gray-1200: #{$color-gray-1200};
  --color-gray-1150: #{$color-gray-1150};
  --color-gray-1100: #{$color-gray-1100};
  --color-gray-1050: #{$color-gray-1050};
  --color-gray-1000: #{$color-gray-1000};
  --color-gray-950: #{$color-gray-950};
  --color-gray-900: #{$color-gray-900};
  --color-gray-850: #{$color-gray-850};
  --color-gray-800: #{$color-gray-800};
  --color-gray-750: #{$color-gray-750};
  --color-gray-700: #{$color-gray-700};
  --color-gray-650: #{$color-gray-650};
  --color-gray-600: #{$color-gray-600};
  --color-gray-550: #{$color-gray-550};
  --color-gray-500: #{$color-gray-500};
  --color-gray-450: #{$color-gray-450};
  --color-gray-400: #{$color-gray-400};
  --color-gray-350: #{$color-gray-350};
  --color-gray-300: #{$color-gray-300};
  --color-gray-250: #{$color-gray-250};
  --color-gray-200: #{$color-gray-200};
  --color-gray-150: #{$color-gray-150};
  --color-gray-100: #{$color-gray-100};
  --color-gray-50: #{$color-gray-50};
  // blue
  --color-blue-1200: #{$color-blue-1200};
  --color-blue-1150: #{$color-blue-1150};
  --color-blue-1100: #{$color-blue-1100};
  --color-blue-1050: #{$color-blue-1050};
  --color-blue-1000: #{$color-blue-1000};
  --color-blue-950: #{$color-blue-950};
  --color-blue-900: #{$color-blue-900};
  --color-blue-850: #{$color-blue-850};
  --color-blue-800: #{$color-blue-800};
  --color-blue-750: #{$color-blue-750};
  --color-blue-700: #{$color-blue-700};
  --color-blue-650: #{$color-blue-650};
  --color-blue-600: #{$color-blue-600};
  --color-blue-550: #{$color-blue-550};
  --color-blue-500: #{$color-blue-500};
  --color-blue-450: #{$color-blue-450};
  --color-blue-400: #{$color-blue-400};
  --color-blue-350: #{$color-blue-350};
  --color-blue-300: #{$color-blue-300};
  --color-blue-250: #{$color-blue-250};
  --color-blue-200: #{$color-blue-200};
  --color-blue-150: #{$color-blue-150};
  --color-blue-100: #{$color-blue-100};
  --color-blue-50: #{$color-blue-50};
  // green
  --color-green-600: #{$color-green-600};
  --color-green-550: #{$color-green-550};
  --color-green-500: #{$color-green-500};
  --color-green-450: #{$color-green-450};
  --color-green-400: #{$color-green-400};
  --color-green-350: #{$color-green-350};
  --color-green-300: #{$color-green-300};
  --color-green-250: #{$color-green-250};
  --color-green-200: #{$color-green-200};
  --color-green-150: #{$color-green-150};
  --color-green-100: #{$color-green-100};
  --color-green-50: #{$color-green-50};
  // red
  --color-red-500: #{$color-red-500};
  --color-red-450: #{$color-red-450};
  --color-red-400: #{$color-red-400};
  --color-red-350: #{$color-red-350};
  --color-red-300: #{$color-red-300};
  --color-red-250: #{$color-red-250};
  --color-red-200: #{$color-red-200};
  --color-red-150: #{$color-red-150};
  --color-red-100: #{$color-red-100};
  --color-red-50: #{$color-red-50};
  // yellow
  --color-yellow-250: #{$color-yellow-250};
  --color-yellow-200: #{$color-yellow-200};
  --color-yellow-150: #{$color-yellow-150};
  --color-yellow-100: #{$color-yellow-100};
  --color-yellow-50: #{$color-yellow-50};
  // teal
  --color-teal-150: #{$color-teal-150};
  --color-teal-100: #{$color-teal-100};
  --color-teal-50: #{$color-teal-50};
  // indigo
  --color-indigo-300: #{$color-indigo-300};
}
