// # color palette

// base
$color-black: #000000;
$color-white: #ffffff;
// gray
$color-gray-1250: #101828;
$color-gray-1200: #2d2d2d;
$color-gray-1150: #333333;
$color-gray-1100: #3c3c40;
$color-gray-1050: #424353;
$color-gray-1000: #484e5d; // input label | card header | field label color
$color-gray-950: #4d4e53;
$color-gray-900: #595a69;
$color-gray-850: #555555;
$color-gray-800: #686565;
$color-gray-750: #78798a; // subtext | headermenu text | field value color
$color-gray-700: #848181;
$color-gray-650: #b0b5bd;
$color-gray-600: #c5c2c2;
$color-gray-550: #d8d8d8; // checkbox border
$color-gray-500: #dacfcf;
$color-gray-450: #dddddd; // disabled button colors
$color-gray-400: #e0e0e0;
$color-gray-350: #e2e2e3; // tab header border
$color-gray-300: #e4e4e4; // header menu border
$color-gray-250: #e8e8e8; // dropdown border
$color-gray-200: #f0f0f0; // disabled
$color-gray-150: #f5f7f9;
$color-gray-100: #f9f9f9;
$color-gray-50: #fdfdfd;
// blue
$color-blue-1200: #2e90fa;
$color-blue-1150: #00102b;
$color-blue-1100: #202634;
$color-blue-1050: #2f343e;
$color-blue-1000: #314469; // select indicator | select clear
$color-blue-950: #374469;
$color-blue-900: #31708f;
$color-blue-850: #6d809f;
$color-blue-800: #0d66d0;
$color-blue-750: #0f56f8; // primary
$color-blue-700: #1e6af9;
$color-blue-650: #266bf5;
$color-blue-600: #646cff;
$color-blue-550: #76a4f8;
$color-blue-500: #84a8f9;
$color-blue-450: #9fb9ea;
$color-blue-400: #bce8f1;
$color-blue-350: #dce2ed; // input border | card border | bottombar border
$color-blue-300: #deebff; // select multi value background
$color-blue-250: #dfe4ef;
$color-blue-200: #e3edf8;
$color-blue-150: #e4e7f2;
$color-blue-100: #e7f3fe;
$color-blue-50: #edf4fa; // header menu bg hover
// green
$color-green-600: #10c12b;
$color-green-550: #238747; // toast bg success
$color-green-500: #00b140; // checkbox checked | toast bg success
$color-green-450: #00b735; // input border focus
$color-green-400: #00c36d;
$color-green-350: #00e685;
$color-green-300: #0daa00; // success
$color-green-250: #14b53d;
$color-green-200: #2ab561;
$color-green-150: #43af58;
$color-green-100: #4caf50;
$color-green-50: #eafff4;
// red
$color-red-500: #ffa3a3;
$color-red-450: #fc5a5a;
$color-red-400: #a94442;
$color-red-350: #d30000;
$color-red-300: #ff0025; // input - error border color | toast bg error
$color-red-250: #f66e84;
$color-red-200: #ebccd1;
$color-red-150: #f2dede;
$color-red-100: #f8d7da;
$color-red-50: #fff3f3;
// yellow
$color-yellow-250: #f4b22e; // toast bg info
$color-yellow-200: #ffc107;
$color-yellow-150: #8a6d3b;
$color-yellow-100: #faebcc;
$color-yellow-50: #fcf8e3;
// teal
$color-teal-150: #17a2b8;
$color-teal-100: #4dd8e2;
$color-teal-50: #45ccb1;
// indigo
$color-indigo-300: #7a3ff3;

// # typography
// font-weight
$font-weight-100: 100;
$font-weight-200: 200;
$font-weight-300: 300;
$font-weight-400: 400;
$font-weight-500: 500;
$font-weight-600: 600;
$font-weight-700: 700;
$font-weight-800: 800;
$font-weight-900: 900;
$font-weight-1000: 1000;
// font-size
$font-size-12: 12px;
$font-size-14: 14px;
$font-size-16: 16px;
$font-size-18: 18px;
$font-size-20: 20px;
$font-size-24: 24px;

// # breakpoints
$breakpoint-sm: 768px;
$breakpoint-md: 1024px;
$breakpoint-lg: 1280px;

// # animations & effects
$animation-duration-default: 0.3s;
$animation-duration-small: 0.1s;
$default-hover-opacity: 0.8;

// # layout & gap & dimensions
// width & height
$header-height: 48px;
$sidebar-left-width: 360px;
// gutters
$gutter-xlarge: 24px;
$gutter-large: 16px;
$gutter-default: 12px;
$gutter-small: 10px;
// gaps
$gap-giant: 32px;
$gap-xxxlarge: 24px;
$gap-xxlarge: 22px;
$gap-xlarge: 20px;
$gap-large: 16px;
$gap-default: 12px;
$gap-medium: 8px;
$gap-small: 6px;
$gap-xsmall: 5px;
$gap-xxsmall: 4px;
// border
$border-radius-xxxlarge: 16px;
$border-radius-xxxlarge-item-tolerance: 14px;
$border-radius-xxlarge: 12px;
$border-radius-xxlarge-item-tolerance: 11px;
$border-radius-xlarge: 10px;
$border-radius-xlarge-item-tolerance: 8px;
$border-radius-large: 8px;
$border-radius-large-item-tolerance: 7px;
$border-radius-default: 6px;
$border-radius-default-item-tolerance: 5px;
$border-radius-medium: 4px;
$border-radius-medium-item-tolerance: 2px;
$border-radius-small: 3px;
$border-radius-small-item-tolerance: 2px;
// button
$button-dimension-xsmall: 20px;
$button-dimension-small: 40px;
$button-dimension-default: 50px;
$button-dimension-large: 60px;
// dropdown
$layout-toastContainer-z-index: 5;
$layout-header-z-index: 4;
$layout-sidebar-left-z-index: 3;
$card-z-index: 2;
$bottombar-z-index: 2;
$dropdown-z-index: 3;
$input-label-z-index: 1;
$error-z-index: 2;
$tab-item-z-index: 2;
$modal-overlay-z-index: 5;
$modal-z-index: 5;
$loader-z-index: 6;
$checkbox-checkmark-z-index: 1;
// input
$input-dimension-small: 36px;
$input-dimension-default: 46px;
$input-dimension-large: 56px;
// input-checkbox
$input-checkbox-dimension-small: 40px;
$input-checkbox-dimension-default: 50px;
$input-checkbox-dimension-large: 60px;
$input-checkbox-checkmark-dimension-small: 20px;
$input-checkbox-checkmark-dimension-default: 25px;
$input-checkbox-checkmark-dimension-large: 30px;

// pagecontent
$pagecontent-padding-block: 20px;
$pagecontent-padding-inline: 56px;
$toastContainer-padding-inline: 56px;
// select
$selectClassNamePrefix: "reactSelect";
// modal
$modal-space: 1.875rem;
$modal-small-max-width: 600px;
$modal-default-max-width: calc(100% - 400px);
$modal-large-max-width: 100%;
// financialStatus
$financialStatusBubble-dimension-default: 240px;
