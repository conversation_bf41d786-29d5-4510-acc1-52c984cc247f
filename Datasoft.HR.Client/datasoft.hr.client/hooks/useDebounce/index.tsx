import { defaultDebounceTimeout } from "@/utils/constants";
import { useEffect, useState } from "react";

/**
 * <AUTHOR>
 * @function useDebounce
 * @description handling input values in given delay duration to prevent unnecessary requests
 * @param {string} value input value for debounce
 * @param {number} delay duration of trigger action
 * @returns {string}
 * @example
 * consst [searchTerm, setSearchTerm] = useState<string>("");
 * const debouncedSearchTerm = useDebounce(searchTerm);
 */
export const useDebounce = (value: string, delay: number = defaultDebounceTimeout): string => {
  const [debouncedValue, setDebouncedValue] = useState<string>(value);
  useEffect(() => {
    const debounceTimeout = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    return () => clearTimeout(debounceTimeout);
  }, [value, delay]);
  return debouncedValue;
};
