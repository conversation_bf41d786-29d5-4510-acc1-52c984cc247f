"use client";

import { useContext } from "react";

import { ModalContext } from "@/contexts/ModalContext";
import { TModalData } from "@/contexts/ModalContext/ModalContext.types";

const useModal = () => {
  const { modals, setModals, modalData, setModalData, modalStack, setModalStack } = useContext(ModalContext);

  /**
   * <AUTHOR>
   * @function showModal
   * @description shows given modal with their parameters
   * @param {TModal} modalType
   * @param {TModalData} data
   * @returns
   * @example
   * const { showModal } = useModal();
   * showModal(ModalTypes.ModalSample, {
   *    data: {id: "123456"},
   *    callback: () => {alert("!")}
   * })
   */
  const showModal = <T extends keyof TModalData>(modalType: T, data?: TModalData[T]) => {
    document.body.classList.add("modal-open");
    setModals({ ...modals, [modalType]: true });
    setModalData({
      ...modalData,
      [modalType]: data,
    });
  };

  /**
   * <AUTHOR>
   * @function hideModal
   * @description hide the modal shown
   * @param {TModal} modalType
   * @returns
   * @example
   * const { hideModal } = useModal();
   * hideModal(ModalTypes.ModalSample);
   */
  const hideModal = (modalType: keyof TModalData) => {
    // TODO: modal stack handling
    document.body.classList.remove("modal-open");
    const _modals = { ...modals };
    delete _modals[modalType];
    const _tempModalData = { ...modalData };
    delete _tempModalData[modalType];

    setModals(_modals);
    setModalData(_tempModalData);
  };
  return {
    modals,
    modalData,
    hideModal,
    showModal,
    modalStack,
    setModalStack,
  };
};

export default useModal;
