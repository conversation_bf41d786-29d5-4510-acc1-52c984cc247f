import { useEffect, useRef } from "react";

/**
 * <AUTHOR>
 * @function useOutsideClick
 * @description detect clicking outside area of an element via given ref and invokes a callback function
 * @param callback function that will be invoked after clicking outside
 * @example
 * const divRef = useOutsideClick(() => { setOpen(false) });
 * <div ref={divRef}></div>
 */
export const useOutsideClick = (callback: () => void) => {
  const ref = useRef<any | null>(null);
  useEffect(() => {
    // callback invoke
    const handleClick = (e: any) => {
      if (ref.current && !ref.current.contains(e.target)) {
        callback();
      }
    };
    document.addEventListener("click", handleClick, true);
    return () => {
      document.removeEventListener("click", handleClick, true);
    };
  }, [ref]);
  return ref;
};
