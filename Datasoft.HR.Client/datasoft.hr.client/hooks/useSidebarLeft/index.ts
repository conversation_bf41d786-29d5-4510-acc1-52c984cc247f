import { SidebarLeftContext } from "@/contexts/SidebarLeftContext";
import { useContext } from "react";

/**
 * <AUTHOR>
 * @function useSidebarLeft
 * @description custom hook for using SidebarLeftContext
 * @example
 * const {activeMenuItemId, setActiveMenuItemId, sidebarLeftOpen, setSidebarLeftOpen} = useSidebarLeft();
 */
export const useSidebarLeft = () => {
  const { activeMenuItemId, setActiveMenuItemId, sidebarLeftOpen, setSidebarLeftOpen } = useContext(SidebarLeftContext);
  return {
    activeMenuItemId,
    setActiveMenuItemId,
    sidebarLeftOpen,
    setSidebarLeftOpen,
  };
};
