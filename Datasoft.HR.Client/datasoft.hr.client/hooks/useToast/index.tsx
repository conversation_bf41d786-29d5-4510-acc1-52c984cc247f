"use client";

import { ReactElement, useContext } from "react";
import { BsCheck2Circle } from "react-icons/bs";
import { RxInfoCircled } from "react-icons/rx";
import { ToastContext } from "@/contexts/ToastContext";
import { IToast, ToastStatus } from "@/components/common/Toast/Toast.types";
import { defaultToastTimeoutDuration } from "@/utils/constants";
import { AiOutlineCloseCircle } from "react-icons/ai";
import Icon from "@/components/common/Icon";
import { SizeVariant } from "@/types/SizeVariant";

/**
 * <AUTHOR>
 * @function useToast
 * @description custom hook for using ToastContext
 * @example
 * const { showToast } = useToast();
 * showToast({ text: "lorem ipsum" })
 */
const useToast = () => {
  const { toasts, setToasts } = useContext(ToastContext);

  const getToastIcon = (status: ToastStatus): ReactElement => {
    switch (status) {
      case ToastStatus.success:
        return <BsCheck2Circle />;
      case ToastStatus.info:
        return <RxInfoCircled />;
      case ToastStatus.neutral:
        return <RxInfoCircled />;
      case ToastStatus.error:
        return <AiOutlineCloseCircle />;
      default:
        return <AiOutlineCloseCircle />;
    }
  };

  const showToast = ({ text = "", status = ToastStatus.error }) => {
    const _toast: IToast = {
      id: new Date().getTime().toString(),
      icon: <Icon icon={getToastIcon(status)} sizeVariant={SizeVariant.small} />,
      status,
      text,
    };
    setToasts((prevToastList: IToast[]) => [...prevToastList, _toast]);
    setTimeout(() => {
      hideToast(_toast.id as string);
    }, defaultToastTimeoutDuration);
  };

  const hideToast = (id: string) => {
    setToasts((prevToastList: IToast[]) => prevToastList.filter((toast) => toast.id !== id));
  };

  return {
    toasts,
    setToasts,
    showToast,
    hideToast,
  };
};

export default useToast;
