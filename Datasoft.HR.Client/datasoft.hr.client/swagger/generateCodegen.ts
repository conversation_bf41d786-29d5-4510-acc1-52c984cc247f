import * as fs from "fs";
import { codegen } from "swagger-axios-codegen";
import { codegenOutputDir, schemaOutputPath } from "./constants";

/**
 * <AUTHOR>
 * @function generateCodegen
 * @description generates axios instances with fetched swagger schema
 */
const generateCodegen = async () => {
  try {
    const schemaFile = fs.readFileSync(schemaOutputPath, "utf-8");
    const schemaJson = JSON.parse(schemaFile);
    await codegen({
      source: schemaJson,
      outputDir: codegenOutputDir,
      methodNameMode: "path",
      modelMode: "interface",
      useCustomerRequestInstance: true,
      serviceNameSuffix: "",
      classNameMode: "normal",
      enumNamePrefix: "",
    });
  } catch (err) {
    // eslint-disable-next-line no-console
    console.log("Failed:", err);
  }
};

generateCodegen();
