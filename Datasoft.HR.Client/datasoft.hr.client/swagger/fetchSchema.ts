import * as fs from 'fs';
import { schemaOutputPath, schemaURL } from './constants';

/**
 * <AUTHOR>
 * @function fetchSchema
 * @description fetches remote swagger schema
 */
const fetchSchema = async () => {
  try {
    const response = await fetch(schemaURL);
    if (response.ok) {
      const json = await response.json();
      const formatted = JSON.stringify(json, null, 2);
      fs.writeFileSync(schemaOutputPath, formatted);
    }
    else {
      throw new Error(`Failed: ${response.status}`);
    }
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error("Failed:", err);
  }
}

fetchSchema();
