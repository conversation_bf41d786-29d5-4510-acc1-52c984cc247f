import * as yup from "yup";

import { formValidationMessages } from "@/utils/formValidationMessages";

export const validationSchemaBankAccountCreate = () => {
  return yup.object().shape({
    bankAccountName: yup.string().required(formValidationMessages.required),
    trackDocument: yup.string().required(formValidationMessages.required),
    createDate: yup.date().required(formValidationMessages.required),
    startDate: yup.date().required(formValidationMessages.required),
    endDate: yup.date().required(formValidationMessages.required),
  });
};
