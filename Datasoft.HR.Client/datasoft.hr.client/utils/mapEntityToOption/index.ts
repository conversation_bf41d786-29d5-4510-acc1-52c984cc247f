import { Option } from "@/types/Option";

/**
 * <AUTHOR>
 * @function mapEntityToOption
 * @description converts entity to option object for using in Select, Checkbox, Radio components
 * @param {any} entity
 * @param {string} labelKey key inside of entity for label of option
 * @param {string} valueKey key inside of entity for value of option
 * @returns {Option}
 * @example
 * mapEntityToOption({id: "1", name: "<PERSON>", surname: "<PERSON><PERSON>"}, "name", "id")
 */
export const mapEntityToOption = (entity: any, labelKey: string | string[], valueKey: string): Option => {
  return {
    label: typeof labelKey === "string" ? entity[labelKey] : labelKey.map((_labelKey) => entity[_labelKey]).join(" "),
    value: entity[valueKey],
    data: entity,
  };
};

/**
 * <AUTHOR>
 * @function mapEntityArrayToOptionArray
 * @description converts array of objects of entity to array that occured by Op<PERSON> for using in Select, Checkbox, Radio component's "option" props
 * @param {any[]} entityArray
 * @param {string} labelKey key inside of entity for label of option
 * @param {string} valueKey key inside of entity for value of option
 * @returns {Option[]}
 * @example
 * mapEntityArrayToOptionArray(
 * [{id: 1, name: "John", surname: "Doe"},{id: 2, name: "Alice", surname: "Jacob"}], "name", "id")
 */
export const mapEntityArrayToOptionArray = (entityArray: any[], labelKey: string | string[], valueKey: string): Option[] => {
  return entityArray.map((entity) => {
    return mapEntityToOption(entity, labelKey, valueKey);
  });
};
