export enum DateFormats {
  DATE = "DD-MM-YYYY",
  DATE_WITH_SLASHES = "DD/MM/YYYY",
  DATE_WITH_SLASHES_FOR_DATE_PICKER = "dd/MM/yyyy", // https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table
  DATE_WITH_DOTS = "DD.MM.YYYY",
  DATE_WITH_DOTS_SHORT_YEAR = "DD.MM.YY",
  DATE_WITH_MONTH_NAME = "DD-MMMM-YYYY",
  DATE_TIME = "DD-MM-YYYY HH:mm",
  DATE_TIME_WITH_DOTS = "DD.MM.YYYY HH:mm",
}
