import dayjs from "dayjs";
import * as tr from "dayjs/locale/tr";
import { DateFormats } from "./enums";

dayjs.locale(tr);

/**
 * <AUTHOR>
 * @function dateFormatter
 * @description formats the given date as parameter and returns it as string value
 * @param {Date | string} date date parameter
 * @param {DateFormats} format format parameter
 * @returns {string}
 * @example
 * dateFormatter(new Date());
 * dateFormatter(new Date(), DateFormats.DATE_TIME);
 */
export const dateFormatter = (date: Date | string | null, format: DateFormats = DateFormats.DATE): string => {
  return date ? dayjs(new Date(date)).format(format) : "";
};
