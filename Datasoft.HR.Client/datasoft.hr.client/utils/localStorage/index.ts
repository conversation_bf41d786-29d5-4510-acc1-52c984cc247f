import { LocalStorageItemSidebarLeftOpen } from "../constants";

/**
 * <AUTHOR>
 * @function setSidebarLeftOpenToLocalStorage
 * @description sets sidebar open status to localStorage
 * @param {boolean} isOpen
 * @example
 * setSidebarLeftOpenToLocalStorage(true)
 * setSidebarLeftOpenToLocalStorage(false)
 */
export const setSidebarLeftOpenToLocalStorage = (isOpen: boolean = true) => {
  localStorage.setItem(LocalStorageItemSidebarLeftOpen, `${isOpen}`);
};
