/**
 * <AUTHOR>
 * @var formValidationMessages
 * @description default form validation messages that can be used for yup schema
 * @returns {string}
 * @example
 * yup.string().required(formValidationMessages.required).min(10, formValidationMessages.minLength(10)),
 */
export const formValidationMessages = {
  required: "Zorunlu alan",
  typeError: "Geçerli bir değer giriniz",
  onlyChars: "Bu alan yalnızca harf içermeli",
  minLength: (number: number) => `Bu alan en az ${number} karakter içermeli`,
  maxLength: (number: number) => `Bu alan en çok ${number} karakter içermeli`,
  min: (number: number) => `Bu alan en az ${number} olmalıdır`,
  max: (number: number) => `Bu alan en çok ${number} olmalıdır`,
};
