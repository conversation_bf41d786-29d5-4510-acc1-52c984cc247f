import { ExceptionEnums, ExceptionLabels } from "@/types/Exceptions";

/**
 * <AUTHOR>
 * @function exceptionHandler
 * @description controls the given error parameter in ExceptionEnums and returns related message as string value
 * @param error error message
 * @param defaultError pre-defined default error message
 * @example
 * exceptionHandler(ExceptionEnums.NOT_FOUND);
 * exceptionHandler(response.error, "Something went wrong");
 * exceptionHandler("Lorem ipsum dolor sit amet");
 * @returns {string}
 */
export const exceptionHandler = (error: any | ExceptionEnums | string, defaultError: string = ""): string => {
  if (Object.values(ExceptionEnums).includes(error as ExceptionEnums)) {
    return ExceptionLabels[error as keyof typeof ExceptionEnums];
  }
  return defaultError;
};
