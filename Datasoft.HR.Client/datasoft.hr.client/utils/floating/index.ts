import { FloatingPlacement } from "@/types/Floating";
import { flip, offset, shift, UseFloatingOptions } from "@floating-ui/react";

/**
 * <AUTHOR>
 * @var defaultFloatingSettings
 * @description default settings for useFloating() hook of @floating-ui/react module
 * @returns {UseFloatingOptions}
 * @example
 * const { refs, floatingStyles, update } = useFloating(defaultFloatingSettings);
 * const { refs, floatingStyles, update } = useFloating({...defaultFloatingSettings, otherSettings });
 */
export const defaultFloatingSettings: UseFloatingOptions = {
  placement: FloatingPlacement.BottomStart,
  middleware: [
    offset(4),
    flip({
      fallbackPlacements: [FloatingPlacement.BottomEnd],
    }),
    shift(),
  ],
};
