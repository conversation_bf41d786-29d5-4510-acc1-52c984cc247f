import axios from "axios";
import { baseURL, CookieAuthorization, CookieSession } from "../constants";
import Cookies from "js-cookie";

export const axiosInstance = axios.create();

axiosInstance.interceptors.request.use((config) => {
  config.baseURL = baseURL;
  config.headers["Content-Type"] = "application/json";
  config.headers.Authorization = Cookies.get(CookieAuthorization) ? `Bearer ${Cookies.get(CookieAuthorization)}` : "";
  config.headers.Cookie = Cookies.get(CookieSession) ? `__session=${Cookies.get(CookieSession)}` : "";
  return config;
});
