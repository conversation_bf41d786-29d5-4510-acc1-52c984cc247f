{"name": "datasoft.hr.client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "storybook": "storybook dev -p 6006", "codegen": "codegen:fetchSchema && codegen:generateCodegen", "codegen:fetchSchema": "npx tsc -p swagger/tsconfig.json && node swagger/_dist/fetchSchema.js", "codegen:generateCodegen": "npx tsc -p swagger/tsconfig.json && node swagger/_dist/generateCodegen.js"}, "dependencies": {"@floating-ui/react": "^0.27.12", "@tanstack/query-sync-storage-persister": "^5.79.0", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-persist-client": "^5.79.0", "axios": "^1.9.0", "classnames": "^2.5.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "formik": "^2.4.6", "js-cookie": "^3.0.5", "next": "15.3.2", "react": "^19.0.0", "react-datepicker": "^8.4.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-number-format": "^5.4.4", "react-select": "^5.10.1", "react-select-async-paginate": "^0.7.10", "recharts": "^3.1.0", "sass": "^1.89.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@storybook/addon-docs": "^9.0.8", "@storybook/nextjs": "^9.0.0", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.33.0", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "swagger-axios-codegen": "^0.17.4", "typescript": "^5"}}