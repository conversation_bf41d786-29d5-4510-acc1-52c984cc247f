This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

Start Project

```bash
npm install

npm run dev
```

Open site in your browser: [http://localhost:3000](http://localhost:3000)

## CSS Definition Order Rules

```
.selector {
    positionning: (position | top | left | bottom...)
    box model: (width | height | min-width | padding | margin | border | outline...)
    display features: (display | inline | block | flex | flex-direction | gap...)
    font features: (color | line-height | font-size...)
    background features: (background-color | background-image...)
    interactions: (cursor...)
    animations: (transition...)
    pseudo classes: (:hover | :active | :webkit...)
    nested selectors: (&.active | .subselector...)
}
```

## Component Class Naming

```
<div className="component">
    <div className="header">
        <div className="titleContainer">
            <div className="title"></div>
            <div className="subtitle"></div>
        </div>
        <div className="controls"></div>
    </div>
    <div className="body"></div>
    <div className="footer"></div>
</div>
```

## Sample File Tree For Component Structure

```
.
├── __tests__
├── app/
│   ├── (auth)/
│   │   └── login/
│   │       ├── page.tsx
│   │       └── layout.tsx
│   └── (main)/
│       ├── bank/
│       │   ├── create/
│       │   │   └── [id]/
│       │   │       └── page.tsx
│       │   └── list/
│       │       └── page.tsx
│       ├── ...
│       ├── layout.tsx // home page layout
│       └── page.tsx // home page
├── components/
│   ├── common/
│   │   └── Table/
│   │       ├── components/
│   │       │   ├── Pagination/
│   │       │   │   └── ...
│   │       │   ├── Column/
│   │       │   │   └── ...
│   │       │   ├── Row/
│   │       │   │   └── ...
│   │       │   ├── Thead/
│   │       │   │   └── ...
│   │       │   ├── TheadColumn/
│   │       │   │   └── ...
│   │       │   ├── Tbody/
│   │       │   │   └── ...
│   │       │   └── TFooter/
│   │       │       └── ...
│   │       ├── Table.stories.ts
│   │       ├── Table.test.ts
│   │       ├── Table.tsx
│   │       ├── Table.types.ts
│   │       ├── Table.module.scss
│   │       └── index.ts
│   ├── layout/
│   │   └── Root/
│   │       ├── _Body/
│   │       │   └── ...
│   │       ├── _Header/
│   │       │   └── ...
│   │       └── _Footer/
│   │           └── ...
│   ├── form/
│   ├── modals/
│   └── ...
├── contexts/
│   ├── ServiceContext/
│   │   └── index.tsx
│   │   └── ServiceContext.types.tsx
│   ├── ModalContext/
│   │   └── index.tsx
│   │   └── ModalContext.types.tsx
├── hooks/
│   └── useKeyboard/
│       └── index.tsx
├── styles/
│   ├── common.scss
│   ├── variables.scss
│   └── mixins.scss
├── types/
│   ├── Exceptions/
│   │   ├── enums.ts
│   │   ├── labels.ts
│   │   ├── mapping.ts
│   │   ├── types.ts
│   │   └── index.ts
│   └── Currency/
│       ├── types.ts
│       ├── enums.ts
│       └── index.ts
└── utils/
    └── formatDate/
        └── index.ts
```
