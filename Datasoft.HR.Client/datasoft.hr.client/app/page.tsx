"use client";

import { useFormik } from "formik";
import Button from "@/components/common/Button";
import ButtonGroup from "@/components/common/ButtonGroup";
import Card from "@/components/common/Card";
import DatePicker from "@/components/form/DatePicker";
import Input from "@/components/form/Input";
import Select from "@/components/form/Select";
import SelectAsync from "@/components/form/SelectAsync";
import { defaultPagingOptionValues } from "@/components/form/SelectAsync/SelectAsync.types";
import SelectCreatable from "@/components/form/SelectCreatable";
import Col from "@/components/grid/Col";
import { ColumnSize } from "@/components/grid/Col/Col.types";
import ModalLarge from "@/components/modals/ModalLarge";
import ModalSmall from "@/components/modals/ModalSmall";
import PageContent from "@/components/page/PageContent";
import { ModalTypes } from "@/contexts/ModalContext/ModalContext.types";
import useModal from "@/hooks/useModal";
import { TaxOffice } from "@/swagger/api";
import { Option } from "@/types/Option";
import { StyleVariant } from "@/types/StyleVariant";
import { mapEntityToOption } from "@/utils/mapEntityToOption";
import dynamic from "next/dynamic";

import { useState } from "react";
import { FiFolderPlus, FiSettings } from "react-icons/fi";
import { RiFileExcel2Line } from "react-icons/ri";
import { initialValuesBankAccountCreate, validationSchemaBankAccountCreate } from "@/schema/bankAccount/create";
import Checkbox from "@/components/form/Checkbox";
import useToast from "@/hooks/useToast";
import { ToastStatus } from "@/components/common/Toast/Toast.types";
import PillGroup from "@/components/common/PillGroup";

const ModalDynamicSample = dynamic(() => import("@/components/modals/ModalDynamicSample"));

// Home component
const Home = () => {
  // destructuring props
  const options = [
    {
      id: "123",
      name: "asdf",
      surname: "",
      age: "",
    },
    {
      id: "435",
      name: "sdfgf",
      surname: "",
      age: "",
    },
    {
      id: "4355asdfasdf",
      name: "İÖÇŞĞÜ - türkçe karakter deneme",
      surname: "",
      age: "",
    },
    {
      id: "13",
      name: "İSMET",
      surname: "",
      age: "",
    },
    {
      id: "4355",
      name: "erov",
      surname: "",
      age: "",
    },
    {
      id: "54",
      name: "iaş",
      surname: "",
      age: "",
    },
    {
      id: "26",
      name: "nd",
      surname: "",
      age: "",
    },
    {
      id: "77",
      name: "sdf",
      surname: "",
      age: "",
    },
    {
      id: "55",
      name: "bgr",
      surname: "",
      age: "",
    },
    {
      id: "44",
      name: "xcv",
      surname: "",
      age: "",
    },
    {
      id: "33",
      name: "wg",
      surname: "",
      age: "",
    },
    {
      id: "22",
      name: "gsd",
      surname: "",
      age: "",
    },
    {
      id: "435512",
      name: "asdşfkkj",
      surname: "",
      age: "",
    },
    {
      id: "43553254",
      name: "asdşfkkj",
      surname: "",
      age: "",
    },
    {
      id: "43552111",
      name: "asdşfkkj",
      surname: "",
      age: "",
    },
    {
      id: "43553331",
      name: "asdşfkkj",
      surname: "",
      age: "",
    },
    {
      id: "43555124",
      name: "asdşfkkj",
      surname: "",
      age: "",
    },
    {
      id: "43532455",
      name: "asdşfkkj",
      surname: "",
      age: "",
    },
  ];

  // state
  const [activePillGroupItemIndex, setActivePillGroupItemIndex] = useState<number>(0);
  const [isQueryEnabled, setIsQueryEnabled] = useState<boolean>(true);
  const [valMask, setValMask] = useState<string>("");
  const [inputValue, setInputValue] = useState<string>("");
  const [singleSelectValue, setSingleSelectValue] = useState<Option | null>(
    mapEntityToOption(
      options.find((option) => option.id === "44"),
      "name",
      "id"
    )
  );
  const [multiSelectValue, setMultiSelectValue] = useState<Option[] | null>();
  const [creatableSingleValue, setCreatableSingleValue] = useState<Option | null>();
  const [creatableMultiValue, setCreatableMultiValue] = useState<Option[] | null>();
  const [singleSelectDynamicValue, setSingleSelectDynamicValue] = useState<Option | null>(null);
  const [datePickerValue, setDatePickerValue] = useState<Date | null>();
  // context hooks
  const { showToast } = useToast();
  const { modals, showModal } = useModal();
  // queries

  // mutations

  // formik
  const formik = useFormik({
    initialValues: initialValuesBankAccountCreate,
    validationSchema: () => validationSchemaBankAccountCreate(),
    onSubmit: (values) => {
      alert(values.bankAccountName);
      // trigger mutation
    },
    enableReinitialize: true,
    validateOnChange: true,
  });
  // effect

  // other variables/functions/handlers

  // render
  return (
    <>
      <PageContent
        title="Lorem ipsum"
        subtitle="Dev Test AŞ"
        controls={
          <>
            <Button id="Btn-Settings" leftIcon={<FiSettings />} onlyIcon onClick={() => null} styleVariant={StyleVariant.outlined} />
            <Button
              id="Btn-BtnDownloadExcel"
              hasMinWidth
              leftIcon={<RiFileExcel2Line />}
              text="Excel"
              onClick={() => null}
              styleVariant={StyleVariant.outlined}
            />
            <Button id="Btn-New" hasMinWidth leftIcon={<FiFolderPlus />} text="Yeni" onClick={() => null} styleVariant={StyleVariant.outlined} />
          </>
        }
      >
        <Card title="Formik Sample">
          <Input
            columnSize={ColumnSize.ColumnSize6}
            id="bankAccountName"
            value={formik.values.bankAccountName}
            onChange={formik.handleChange}
            error={formik.errors.bankAccountName}
            suffixxButton={{
              text: "Kaydet",
              onClick: () => {
                formik.handleSubmit();
              },
            }}
          />
          <Checkbox
            columnSize={ColumnSize.ColumnSize3}
            id="trackDocument1"
            name="trackDocument"
            label="İrsaliye Yerine Geçer"
            onChange={(e) => {
              formik.handleChange(e);
            }}
            value="1"
          />
          <Checkbox
            columnSize={ColumnSize.ColumnSize3}
            id="trackDocument2"
            name="trackDocument"
            label="İrsaliye Yerine Geçer"
            onChange={(e) => {
              formik.handleChange(e);
            }}
            value="2"
          />
          <Checkbox
            columnSize={ColumnSize.ColumnSize3}
            id="trackDocument3"
            name="trackDocument"
            label="İrsaliye Yerine Geçer"
            onChange={(e) => {
              formik.handleChange(e);
            }}
            value="3"
          />
          <Checkbox
            columnSize={ColumnSize.ColumnSize3}
            id="trackDocument4"
            name="trackDocument"
            label="İrsaliye Yerine Geçer"
            onChange={(e) => {
              formik.handleChange(e);
            }}
            value="1"
          />
        </Card>
        <Card title="lorem ipsum">
          <Col>
            <ButtonGroup>
              <Button
                id="ModalSmallButton"
                text="Modal Small"
                onClick={() => {
                  showModal(ModalTypes.ModalSmall, {
                    data: {
                      id: "",
                    },
                  });
                }}
              />
              <Button
                id="ModalDefaultButton"
                text="Modal Default"
                onClick={() => {
                  showModal(ModalTypes.ModalDefault, {
                    data: {
                      id: "",
                      name: "",
                    },
                  });
                }}
              />
              <Button
                id="ModalLargeButton"
                text="Modal Large"
                onClick={() => {
                  showModal(ModalTypes.ModalLarge, {
                    data: {
                      id: "",
                    },
                  });
                }}
              />
              <Button
                id="ModalDynamicSampleButton"
                text="Modal Dynamic"
                onClick={() => {
                  showModal(ModalTypes.ModalDynamicSample, {
                    data: {
                      id: "",
                    },
                  });
                }}
              />
            </ButtonGroup>
          </Col>
          <Col>
            <ButtonGroup>
              <Button
                id="ToastError"
                text="Toast Error"
                onClick={() => {
                  showToast({
                    text: `Hey! ${Math.random()}`,
                    status: ToastStatus.error,
                  });
                }}
              />
              <Button
                id="ToastSuccess"
                text="Toast Success"
                onClick={() => {
                  showToast({
                    text: `Hey! ${Math.random()}`,
                    status: ToastStatus.success,
                  });
                }}
              />
              <Button
                id="ToaastInfo"
                text="Toast Info"
                onClick={() => {
                  showToast({
                    text: `Hey! ${Math.random()}`,
                    status: ToastStatus.info,
                  });
                }}
              />
              <Button
                id="ToastNeutral"
                text="Toast Neutral"
                onClick={() => {
                  showToast({
                    text: `Hey! ${Math.random()}`,
                    status: ToastStatus.neutral,
                  });
                }}
              />
            </ButtonGroup>
          </Col>
          <Input
            columnSize={ColumnSize.ColumnSize4}
            id="inputWithHOC"
            label="Adınız"
            required
            value={inputValue}
            onChange={(e) => {
              setInputValue(e.target.value);
            }}
            placeholder="isim giriniz"
            prefix="@datasoft/"
            suffix=".com"
            prefixButton={{
              text: "Ara",
              onClick: () => {
                alert("Clicked");
              },
            }}
          />
          <DatePicker
            columnSize={ColumnSize.ColumnSize4}
            id="DatePickerSample"
            value={formik.values.createDate}
            onChange={(date) => {
              formik.setFieldValue("createDate", date);
            }}
          />
          <Input
            columnSize={ColumnSize.ColumnSize4}
            id="inputWithHOC"
            label="Adınız"
            required
            value={inputValue}
            onChange={(e) => {
              setInputValue(e.target.value);
            }}
            placeholder="isim giriniz"
            prefix="@datasoft/"
            suffix=".com"
            prefixButton={{
              text: "Ara",
              onClick: () => {
                alert("Clicked");
              },
            }}
          />
          <SelectCreatable
            label="Single Creatable Select - Statik Option Örneği"
            id="singleCreatableSelectStaticOptions"
            columnSize={ColumnSize.ColumnSize12}
            hasButtonEdit
            value={creatableSingleValue}
            onChange={(option) => {
              setCreatableSingleValue(option);
              console.log(option);
            }}
            labelKeyForOptionMapping="name"
            valueKeyForOptionMapping="id"
            options={options}
          />
          <SelectCreatable
            isMulti
            label="Multi Creatable Select - Statik Option Örneği"
            id="multiCreatableSelectStaticOptions"
            columnSize={ColumnSize.ColumnSize12}
            hasButtonEdit
            value={creatableMultiValue}
            onChange={(options) => {
              setCreatableMultiValue(options);
            }}
            labelKeyForOptionMapping="name"
            valueKeyForOptionMapping="id"
            options={options}
          />
          <SelectAsync
            label="Single Select - Dinamik Option Örneği"
            id="singleSelectDynamicOptions"
            columnSize={ColumnSize.ColumnSize12}
            hasButtonEdit
            value={singleSelectDynamicValue}
            onChange={(option) => {
              setSingleSelectDynamicValue(option);
            }}
            labelKeyForOptionMapping={["code", "name"]}
            valueKeyForOptionMapping="id"
            searchFn={(searchTerm, pageNumber) =>
              TaxOffice.search({
                body: {
                  data: {
                    name: searchTerm,
                  },
                  pagingOptions: { ...defaultPagingOptionValues, pageNumber },
                },
              })
            }
          />
          <Select
            isMulti
            label="Multi Select - Statik Option Örneği"
            id="multiSelectStaticOptions"
            columnSize={ColumnSize.ColumnSize12}
            hasButtonEdit
            value={multiSelectValue}
            onChange={(options) => {
              setMultiSelectValue(options);
              console.log(options);
            }}
            labelKeyForOptionMapping="name"
            valueKeyForOptionMapping="id"
            options={options}
          />
          <Select
            label="Single Select - Statik Option Örneği"
            id="singleSelectStaticOptions"
            columnSize={ColumnSize.ColumnSize12}
            hasButtonEdit
            // menuIsOpen
            value={singleSelectValue}
            onChange={(option) => {
              setSingleSelectValue(option);
              console.log(option);
            }}
            labelKeyForOptionMapping="name"
            valueKeyForOptionMapping="id"
            options={options}
          />
        </Card>
        <Col>
          <PillGroup
            activePillGroupItemIndex={activePillGroupItemIndex}
            setActivePillGroupItemIndex={setActivePillGroupItemIndex}
            pillGroupItems={[
              {
                text: "Geçen 7 Gün",
              },
              {
                text: "Geçen 1 Ay",
              },
              {
                text: "Geçen 3 Ay",
              },
              {
                text: "Geçen Yıl",
              },
            ]}
          />
        </Col>
      </PageContent>
      {modals[ModalTypes.ModalDynamicSample] && <ModalDynamicSample />}
      {modals[ModalTypes.ModalSmall] && <ModalSmall />}
      {modals[ModalTypes.ModalLarge] && <ModalLarge />}
    </>
  );
};

// export
export default Home;
