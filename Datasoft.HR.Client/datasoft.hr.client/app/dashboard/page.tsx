"use client";

import PillGroup from "@/components/common/PillGroup";
import { PiHandArrowDownFill } from "react-icons/pi";
import { PiHandArrowUpFill } from "react-icons/pi";
import PageContent from "@/components/page/PageContent";
import { useState } from "react";
import FinancialStatusCard from "@/components/financialStatus/FinancialStatusCard";
import { FinancialStatusItemColorVariant } from "@/components/financialStatus/FinancialStatusCard/components/FinancialStatusItem/FinancialStatusItem.types";
import BudgetSummaryCard from "@/components/financialStatus/BudgetSummaryCard";
import { BudgetSummaryStatus } from "@/types/BudgetSummaryStatus";
import SectionHeader from "@/components/financialStatus/SectionHeader";
import SectionField from "@/components/financialStatus/SectionField";
import FinancialStatsDonutChartCard from "@/components/financialStatus/FinancialStatsDonutChartCard";
import DateRangePicker from "@/components/financialStatus/DateRangePicker";
import Container from "@/components/grid/Container";
import Row from "@/components/grid/Row";
import { IPillGroupItem } from "@/components/common/PillGroup/components/PillGroupItem/PillGroupItem.types";
import { ChartColorPalette } from "@/components/financialStatus/FinancialStatsDonutChartCard/FinancialStatsDonutChartCard.types";

// DashboardPage component
const DashboardPage = () => {
  // destructuring props

  // state
  const [activePillGroupItemIndex, setActivePillGroupItemIndex] = useState<number>(0);
  const [rangeStartDate, setRangeStartDate] = useState<Date | null>(new Date());
  const [rangeEndDate, setRangeEndDate] = useState<Date | null>(null);
  // context hooks

  // queries

  // mutations

  // formik

  // effect

  // other variables/functions/handlers
  const pillGroups: IPillGroupItem[] = [
    {
      text: "Geçen 7 Gün",
    },
    {
      text: "Geçen 1 Ay",
    },
    {
      text: "Geçen 3 Ay",
    },
    {
      text: "Geçen Yıl",
    },
  ];
  // render
  return (
    <PageContent title="">
      <Container>
        <Row>
          <SectionHeader
            title="Finansal Durum"
            controls={
              <>
                <PillGroup
                  activePillGroupItemIndex={activePillGroupItemIndex}
                  setActivePillGroupItemIndex={setActivePillGroupItemIndex}
                  pillGroupItems={pillGroups}
                />
                <DateRangePicker
                  minDate={new Date()}
                  startDate={rangeStartDate}
                  endDate={rangeEndDate}
                  onChange={(dates) => {
                    setRangeStartDate(dates[0]);
                    setRangeEndDate(dates[1]);
                  }}
                />
              </>
            }
          />
          <FinancialStatusCard
            id="Incoming"
            title="Varlıklarım"
            value="₺10.000"
            items={[
              {
                title: "Kasalarım",
                value: "₺50.000",
                colorVariant: FinancialStatusItemColorVariant.primary,
              },
              {
                title: "Banka Hesaplarım",
                value: "-₺10.000",
                colorVariant: FinancialStatusItemColorVariant.secondary,
              },
              {
                title: "Kredi Kartları",
                value: "-₺20.000",
                colorVariant: FinancialStatusItemColorVariant.success,
              },
            ]}
          />
          <FinancialStatusCard
            id="Charges"
            title="Tahsilatlarım"
            value="₺150,000"
            items={[
              {
                title: "Yapılacak Tahsilatlar",
                value: "₺100,000",
                colorVariant: FinancialStatusItemColorVariant.info,
              },
              {
                title: "Banka Hesaplarım",
                value: "₺20,000",
                colorVariant: FinancialStatusItemColorVariant.secondary,
              },
              {
                title: "Kredi Kartları",
                value: "₺30,000",
                colorVariant: FinancialStatusItemColorVariant.success,
              },
            ]}
          />
          <FinancialStatusCard
            id="Payments"
            title="Ödemeler"
            value="₺120.000"
            items={[
              {
                title: "Yapılacak Ödemeler",
                value: "₺100.000",
                colorVariant: FinancialStatusItemColorVariant.primary,
              },
              {
                title: "Geciken Ödemeler",
                value: "₺20.000",
                colorVariant: FinancialStatusItemColorVariant.danger,
              },
              {
                title: "Eşleşmeyen Ödemeler",
                value: "₺30.000",
                colorVariant: FinancialStatusItemColorVariant.warning,
              },
            ]}
          />
          <BudgetSummaryCard title="Güncel Durumu" value="₺125.201" status={BudgetSummaryStatus.debtor} />
          <SectionHeader
            title="Alış/Satış Durumu"
            controls={
              <>
                <PillGroup
                  activePillGroupItemIndex={activePillGroupItemIndex}
                  setActivePillGroupItemIndex={setActivePillGroupItemIndex}
                  pillGroupItems={pillGroups}
                />
                <DateRangePicker
                  maxDate={new Date()}
                  startDate={rangeStartDate}
                  endDate={rangeEndDate}
                  onChange={(dates) => {
                    setRangeStartDate(dates[0]);
                    setRangeEndDate(dates[1]);
                  }}
                />
              </>
            }
          />
          <SectionField icon={<PiHandArrowUpFill />} title="Satışlarım" value="₺120.000">
            <FinancialStatsDonutChartCard
              title="Lorem ipsum dolor sit amet"
              text="Satılan Toplam Müşteri Sayısı"
              value="10,204"
              data={[
                { name: "Frontend", value: 400 },
                { name: "Backend", value: 300 },
                { name: "DevOps", value: 300 },
                { name: "QA", value: 200 },
                { name: "Analysis", value: 200 },
                { name: "Designer", value: 200 },
              ]}
            />
            <FinancialStatsDonutChartCard
              title="Lorem ipsum dolor sit amet"
              text="Satılan Toplam Müşteri Sayısı"
              value="10,204"
              chartColorPalette={ChartColorPalette.vivid}
              data={[
                { name: "Frontend", value: 400 },
                { name: "Backend", value: 300 },
                { name: "DevOps", value: 300 },
                { name: "QA", value: 200 },
                { name: "Analysis", value: 200 },
                { name: "Designer", value: 200 },
              ]}
            />
          </SectionField>
          <SectionField icon={<PiHandArrowDownFill />} title="Alışlarım" value="₺10.000">
            <FinancialStatsDonutChartCard
              title="Lorem ipsum dolor sit amet"
              text="Satılan Toplam Müşteri Sayısı"
              value="10,204"
              chartColorPalette={ChartColorPalette.pastel}
              data={[
                { name: "Frontend", value: 400 },
                { name: "Backend", value: 300 },
                { name: "DevOps", value: 300 },
                { name: "QA", value: 200 },
                { name: "Analysis", value: 200 },
                { name: "Designer", value: 200 },
              ]}
            />
            <FinancialStatsDonutChartCard
              title="Lorem ipsum dolor sit amet"
              text="Satılan Toplam Müşteri Sayısı"
              value="10,204"
              chartColorPalette={ChartColorPalette.dimmed}
              data={[
                { name: "Frontend", value: 400 },
                { name: "Backend", value: 300 },
                { name: "DevOps", value: 300 },
                { name: "QA", value: 200 },
                { name: "Analysis", value: 200 },
                { name: "Designer", value: 200 },
              ]}
            />
          </SectionField>
        </Row>
      </Container>
    </PageContent>
  );
};

// export
export default DashboardPage;
