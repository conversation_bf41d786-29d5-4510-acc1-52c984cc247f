import localFont from "next/font/local";
import { ServiceContextProvider } from "@/contexts/ServiceContext";
import { SidebarLeftContextProvider } from "@/contexts/SidebarLeftContext";
import MainLayout from "@/components/layout/main";
import { siteMetaData } from "@/utils/constants";
import "@/styles/main.scss";
import { ModalContextProvider } from "@/contexts/ModalContext";
export const metadata = siteMetaData;
import "@/lib/react-datepicker/locale.ts";
import { ToastContextProvider } from "@/contexts/ToastContext";

const axiforma = localFont({
  src: [
    {
      path: "../public/fonts/axiforma_thin-webfont.woff",
      weight: "100",
    },
    {
      path: "../public/fonts/axiforma_light-webfont.woff",
      weight: "200",
    },
    {
      path: "../public/fonts/axiforma_book-webfont.woff",
      weight: "300",
    },
    {
      path: "../public/fonts/axiforma_regular-webfont.woff",
      weight: "400",
    },
    {
      path: "../public/fonts/axiforma_medium-webfont.woff",
      weight: "500",
    },
    {
      path: "../public/fonts/axiforma_semibold-webfont.woff",
      weight: "600",
    },
    {
      path: "../public/fonts/axiforma_bold-webfont.woff",
      weight: "700",
    },
    {
      path: "../public/fonts/axiforma_extrabold-webfont.woff",
      weight: "800",
    },
    {
      path: "../public/fonts/axiforma_black-webfont.woff",
      weight: "900",
    },
    {
      path: "../public/fonts/axiforma_heavy-webfont.woff",
      weight: "1000",
    },
  ],
  variable: "--font-axiforma",
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={axiforma.className}>
      <body>
        <ToastContextProvider>
          <ServiceContextProvider>
            <ModalContextProvider>
              <SidebarLeftContextProvider>
                <MainLayout>{children}</MainLayout>
              </SidebarLeftContextProvider>
            </ModalContextProvider>
          </ServiceContextProvider>
        </ToastContextProvider>
      </body>
    </html>
  );
}
