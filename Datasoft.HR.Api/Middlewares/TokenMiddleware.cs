﻿using Datasoft.HR.ValidationInfrastructure;
using DatasoftCommon.ApiClients;
using DatasoftCommon.CacheServices.Redis;
using Microsoft.Extensions.Primitives;
using System.IdentityModel.Tokens.Jwt;
using System.Net;

namespace Datasoft.HR.Api.Middlewares
{
    public class TokenMiddleware : IMiddleware
    {
        private readonly IRedisCacheService _cacheService;
        private readonly TokenTransporter _tokenTransporter;

        public TokenMiddleware(IRedisCacheService cacheService, TokenTransporter tokenTransporter)
        {
            _cacheService = cacheService;
            _tokenTransporter = tokenTransporter;
        }

        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            var headers = context.Request.Headers;
            var haveAuthorizationHeader = headers.ContainsKey("Authorization");
            if (haveAuthorizationHeader)
            {
                var userIdClaim = context.User.Claims.FirstOrDefault(x => x.Type == "uid");
                if (userIdClaim == null)
                {
                    context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                    return;
                }

                var authorizationHeader = headers["Authorization"];

                if (authorizationHeader != StringValues.Empty)
                {
                    if (System.Diagnostics.Debugger.IsAttached)
                    {
                        // burası test amaçlıdır canlıya çıkınca kapana bilir
                        var requestToken = authorizationHeader.Single().Split(" ").Last();

                        if (string.IsNullOrWhiteSpace(requestToken))
                        {
                            context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                            return;
                        }

                        var isSystemClaim = context.User.Claims.FirstOrDefault(x => x.Type == "isSystem");
                        var isSystemUser = isSystemClaim != null && Convert.ToBoolean(isSystemClaim.Value);
                        if (!isSystemUser)
                        {
                            if (GetValueByKey(requestToken, "companyType") != "4")
                                throw ValidationException.Error("INVALID_TOKEN_COMPANY_TYPE", "Geçersiz oturum bilgisi, bu oturum insan kaynakları şirketine ait değil");

                            if (GetValueByKey(requestToken, "requiredBranch") == "True")
                                throw ValidationException.Error("INVALID_TOKEN_REQUIRED_BRANCH", "Şubeli şirketlerde şube seçimi yapılmalı");

                            if (GetValueByKey(requestToken, "requiredPeriod") == "True")
                                throw ValidationException.Error("INVALID_TOKEN_REQUIRED_PERIOD", "Dönem seçimi yapılmalı");

                            _tokenTransporter.Token = requestToken;
                        }
                    }
                    else
                    {
                        var userId = userIdClaim.Value;
                        var credential = await _cacheService.GetAsync(userId, new { AccessToken = "" });
                        if (string.IsNullOrWhiteSpace(credential?.AccessToken))
                        {
                            context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                            return;
                        }

                        var isSystemClaim = context.User.Claims.FirstOrDefault(x => x.Type == "isSystem");
                        var isSystemUser = isSystemClaim != null && Convert.ToBoolean(isSystemClaim.Value);
                        if (!string.IsNullOrWhiteSpace(credential?.AccessToken) && !isSystemUser)
                        {
                            var requestToken = authorizationHeader.Single().Split(" ").Last();
                            if (requestToken != credential?.AccessToken)
                            {
                                context.Response.StatusCode = (int)HttpStatusCode.IMUsed;
                                return;
                            }

                            if (GetValueByKey(requestToken, "companyType") != "4")
                                throw ValidationException.Error("INVALID_TOKEN_COMPANY_TYPE", "Geçersiz oturum bilgisi, bu oturum insan kaynakları şirketine ait değil");

                            if (GetValueByKey(requestToken, "requiredBranch") == "True")
                                throw ValidationException.Error("INVALID_TOKEN_REQUIRED_BRANCH", "Şubeli şirketlerde şube seçimi yapılmalı");

                            if (GetValueByKey(requestToken, "requiredPeriod") == "True")
                                throw ValidationException.Error("INVALID_TOKEN_REQUIRED_PERIOD", "Dönem seçimi yapılmalı");

                            _tokenTransporter.Token = requestToken;
                        }
                    }
                }
            }

            await next(context);
        }

        private string GetValueByKey(string jwtInput, string key)
        {
            var jwtHandler = new JwtSecurityTokenHandler();
            var isValid = jwtHandler.CanReadToken(jwtInput);

            if (!isValid)
                throw ValidationException.Error("INVALID_TOKEN", "Geçersiz token");

            var jwtSecurityToken = jwtHandler.ReadToken(jwtInput) as JwtSecurityToken;

            if (!jwtSecurityToken.Payload.ContainsKey(key))
                throw ValidationException.Error("INVALID_TOKEN", "Token içerisinde eksik key: " + key);

            var value = jwtSecurityToken.Payload[key]?.ToString();

            if (string.IsNullOrWhiteSpace(value))
                throw ValidationException.Error("INVALID_TOKEN", "Token içerisinde '" + key + "' için bir değer bulunamadı");

            return value;
        }
    }
}
