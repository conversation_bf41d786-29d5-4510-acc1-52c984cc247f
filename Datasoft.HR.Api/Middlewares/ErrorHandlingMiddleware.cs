﻿using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.ValidationInfrastructure;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System.Net;
using System.Reflection;

namespace Datasoft.HR.Api.Middlewares
{
    public class ErrorHandlingMiddleware
    {
        private readonly RequestDelegate _next;
        private static JsonSerializerSettings _serializerSetting = new JsonSerializerSettings
        {
            ContractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            },
            Formatting = Formatting.Indented
        };
        /// <summary>
        /// Constructor used by .NET
        /// </summary>
        public ErrorHandlingMiddleware(RequestDelegate next)
        {
            _next = next;
        }
        public async Task Invoke(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (ValidationException ex)
            {
                context.Response.ContentType = "application/json";
                context.Response.StatusCode = (int)HttpStatusCode.OK;

                var errorList = new List<ErrorModel>();
                JArray array = new JArray();
                foreach (var error in ex.Errors)
                {
                    var model = new ErrorModel();
                    if (error.CustomState != null)
                    {
                        var stateType = error.CustomState.GetType();
                        var stateProperties = stateType.GetProperties().ToList();

                        model.PropertyNames = TryGetValue<List<string>>(error.CustomState, stateProperties, "PropertyNames");
                        model.AttemptedValues = TryGetValue<Dictionary<string, object>>(error.CustomState, stateProperties, "AttemptedValues");
                        model.ExpectedValue = TryGetValue<object>(error.CustomState, stateProperties, "ExpectedValue");
                        model.MessageContext = TryGetValue<Dictionary<string, object>>(error.CustomState, stateProperties, "MessageContext");
                    }

                    if (model.PropertyNames == null)
                        model.PropertyNames = new List<string> { error.PropertyName ?? string.Empty };

                    if (model.AttemptedValues == null && error.AttemptedValue != null)
                        model.AttemptedValues = new Dictionary<string, object> { { error.PropertyName ?? string.Empty, error.AttemptedValue } };

                    model.ErrorCode = error.ErrorCode;
                    model.ErrorMessage = error.ErrorMessage;

                    errorList.Add(model);
                }

                var data = new Result<string> { Errors = errorList, IsSuccess = false };
                var result = JsonConvert.SerializeObject(data, _serializerSetting);

                await context.Response.WriteAsync(result);
                return;
            }
            catch (Exception ex)
            {
                context.Response.ContentType = "application/json";
                context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                var result = JsonConvert.SerializeObject(ex);
                await context.Response.WriteAsync(result);
                return;
            }

        }

        private static T TryGetValue<T>(object data, List<PropertyInfo> propertyInfos, string propertyName)
        {
            var property = propertyInfos.FirstOrDefault(p => p.Name == propertyName);
            if (property == null)
            {
                return default;
            }

            return (T)property.GetValue(data);
        }
    }
}
