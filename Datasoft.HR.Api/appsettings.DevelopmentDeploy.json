{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"Mongo": "mongodb://mongo-rs-master:6512,mongo-rs1:6513,mongo-rs2:6514/admin?replicaSet=dsset", "Redis": "redis-server"}, "TurmobOptions": {"BaseAddress": "http://**************:6516"}, "DatasoftERP": {"AdminAPIBaseURL": "http://admin-server-runtime", "TenantAPIBaseURL": "http://api-server-runtime", "SalesAPIBaseURL": "http://sales-api", "TenantAPIV2BaseURL": "http://pre-accounting-tenant-api:8080"}, "AccountingOptions": {"BaseAddress": "http://accounting-api"}, "EDefterOptions": {"BaseAddress": "http://edefter-api"}}