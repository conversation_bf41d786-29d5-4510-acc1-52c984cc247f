{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"Mongo": "mongodb://localhost:27017,localhost:27018/admin?replicaSet=mongo-set", "Redis": "localhost:6379"}, "CacheOptions": {"SystemCacheType": 10}, "Token": {"Secret": "c7affa529f7db8b12e3db00d53fd4f008d707a89", "ExpressMinutes": 480, "Issuer": "https://www.facebook.com/datasoft.yazilim", "Audience": "DataSoft Yazilim"}, "CommunicationOptions": {"SendErrorsToMail": ["<EMAIL>"], "Email": {"SmtpHost": "mail.datasoft.com.tr", "SmtpPort": "587", "SmtpUserName": "<EMAIL>", "SmtpPassword": "UL;G!VT(~OaZ", "FromEmail": "<EMAIL>", "EmailBccList": ["<EMAIL>"]}, "Sms": {"BaseAddress": "http://api2.ekomesaj.com", "UserName": "datasoft", "Password": "nxGTe6Nc", "Subject": "DATASOFT"}}, "DatasoftERP": {"AdminAPIBaseURL": "http://localhost:5500", "AdminAPIUserName": "q", "AdminAPIPassword": "-1j<PERSON><PERSON><PERSON>", "TenantAPIBaseURL": "http://localhost:5000", "TenantAPIUserName": "service", "TenantAPIPassword": "service", "SalesAPIBaseURL": "http://localhost:5750"}, "AllowedHosts": "*"}