﻿using Datasoft.HR.Business.Operations.Interfaces;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Parameters;
using Microsoft.AspNetCore.Mvc;

namespace Datasoft.HR.Api.Controllers
{
    /// <summary>
    /// Ticari sicil müdürlüğü
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CommercialRegistryOfficeController : ControllerBase
    {
        private readonly ICommercialRegistryOfficeOperations _commercialRegistryOfficeOperations;
        public CommercialRegistryOfficeController(ICommercialRegistryOfficeOperations commercialRegistryOfficeOperations)
        {
            _commercialRegistryOfficeOperations = commercialRegistryOfficeOperations;
        }

        /// <summary>
        /// Ticari sicil müdürlüğü arama apisi
        /// </summary>
        /// <param name="pagingInput"><see cref="PagingInput{CommercialRegistryOfficeSearchDto}"/></param>
        /// <returns><see cref="PagingOutput{IEnumerable{CommercialRegistryOffice}}"/></returns>
        [HttpPost("[action]")]
        public Task<Result<List<CommercialRegistryOffice>>> Search(PagingInput<CommercialRegistryOfficeSearchDto> pagingInput)
        {
            return _commercialRegistryOfficeOperations.SearchAsync(pagingInput);
        }
    }
}
