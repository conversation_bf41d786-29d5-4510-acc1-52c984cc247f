﻿using Datasoft.HR.Business.Operations.Interfaces.Parameters;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters.Resources;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Parameters;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Datasoft.HR.Api.Controllers
{
    /// <summary>
    /// Dil seçeneği controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class LanguageController : ControllerBase
    {
        private readonly ILanguageOperations _languageOperations;
        public LanguageController(ILanguageOperations languageOperations)
        {
            _languageOperations = languageOperations;
        }

        /// <summary>
        /// Dil seçeneği arama apisi
        /// </summary>
        /// <param name="pagingInput"><see cref="PagingInput{LanguageSearchRequestDto}"/></param>
        /// <returns><see cref="PagingOutput{IEnumerable{Language}}"/></returns>
        [HttpPost("[action]")]
        public Task<Result<List<Language>>> Search(PagingInput<LanguageSearchRequestDto> pagingInput)
        {
            return _languageOperations.SearchAsync(pagingInput);
        }
    }
}
