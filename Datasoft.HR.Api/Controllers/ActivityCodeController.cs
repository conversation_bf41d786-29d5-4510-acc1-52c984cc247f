using Datasoft.HR.Business.Operations.Interfaces;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Parameters;
using Microsoft.AspNetCore.Mvc;

namespace Datasoft.HR.Api.Controllers
{
    /// <summary>
    /// Faaliyet kodu/esas iş controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ActivityCodeController : ControllerBase
    {
        private readonly IActivityCodeOperations _activityCodeOperations;
        public ActivityCodeController(IActivityCodeOperations activityCodeOperations)
        {
            _activityCodeOperations = activityCodeOperations;
        }

        /// <summary>
        /// Faaliyet kodu / Esas iş arama apisi
        /// </summary>
        /// <param name="pagingInput"><see cref="PagingInput{ActivityCodeSearchDto}"/></param>
        /// <returns><see cref="PagingOutput{IEnumerable{ActivityCode}}"/></returns>
        [HttpPost("[action]")]
        public Task<Result<List<ActivityCode>>> Search(PagingInput<ActivityCodeSearchDto> pagingInput)
        {
            return _activityCodeOperations.SearchAsync(pagingInput);
        }
    }
}
