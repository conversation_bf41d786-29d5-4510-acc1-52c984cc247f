﻿using Datasoft.HR.Business.Operations.Interfaces;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Parameters;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Datasoft.HR.Api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class TaxOfficeController : ControllerBase
    {
        private readonly ITaxOfficeOperations _taxOfficeOperations;

        public TaxOfficeController(ITaxOfficeOperations taxOfficeOperations)
        {
            _taxOfficeOperations = taxOfficeOperations;
        }

        /// <summary>
        /// Vergi dairesi arama apisi
        /// </summary>
        /// <param name="pagingInput"><see cref="PagingInput{TaxOfficeSearchDto}"/></param>
        /// <returns><see cref="PagingOutput{IEnumerable{TaxOffice}}"/></returns>
        [HttpPost("[action]")]
        public Task<Result<List<TaxOffice>>> Search(PagingInput<TaxOfficeSearchDto> pagingInput)
        {
            return _taxOfficeOperations.SearchAsync(pagingInput);
        }
    }
}
