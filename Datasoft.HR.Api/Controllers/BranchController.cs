﻿using Datasoft.HR.Business.Operations.Interfaces;
using Datasoft.HR.Core.Common;
using Datasoft.HR.DTO.Branch;
using Datasoft.HR.DTO.Common.Results;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Datasoft.HR.Api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class BranchController : ControllerBase
    {
        private readonly IBranchOperations _branchOperations;

        public BranchController(IBranchOperations branchOperations)
        {
            _branchOperations = branchOperations;
        }

        [HttpPost("[action]")]
        public Task<Result<List<BranchDto>>> Search([FromBody] PagingInput<BranchQueryDto> request)
        {
            return _branchOperations.SearchAsync(request);
        }

        [HttpPost("[action]")]
        public Task<Result<string>> Upsert([FromBody] BranchDto request)
        {
            return _branchOperations.UpsertAsync(request);
        }

        [HttpGet("[action]/{id}")]
        public Task<Result<BranchDto>> Get(string id)
        {
            return _branchOperations.GetAsync(id);
        }

        [HttpGet("[action]/{id}")]
        public Task<Result> Remove(string id)
        {
            return _branchOperations.RemoveAsync(id);
        }
    }
}
