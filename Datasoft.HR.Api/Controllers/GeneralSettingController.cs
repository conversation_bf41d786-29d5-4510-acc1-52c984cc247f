﻿using Datasoft.HR.Business.Operations.Interfaces;
using Datasoft.HR.DTO.Company;
using Microsoft.AspNetCore.Mvc;

namespace Datasoft.HR.Api.Controllers
{
    /// <summary>
    /// Şirket genel ayarları Controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class GeneralSettingController : ControllerBase
    {

        private readonly IGeneralSettingOperations _generalSettingOperations;
        public GeneralSettingController(IGeneralSettingOperations generalSettingOperations)
        {
            _generalSettingOperations = generalSettingOperations;
        }
        /// <summary>
        /// Şirket genel ayarlarını getirir
        /// </summary>
        /// <returns><see cref="GeneralSettingDto"/></returns>
        [HttpGet("[action]")]
        public Task<GeneralSettingDto> GetDefault()
        {
            return _generalSettingOperations.GetDefaultAsync();
        }

        /// <summary>
        /// Şirket genel ayarlarını ekler/günceller
        /// </summary>
        /// <param name="dto"><see cref="GeneralSettingDto"/></param>
        /// <returns>void</returns>
        [HttpPost("[action]")]
        public Task Upsert(GeneralSettingDto dto)
        {
            return _generalSettingOperations.UpsertAsync(dto);
        }

    }
}
