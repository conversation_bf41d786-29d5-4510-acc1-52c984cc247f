﻿using Datasoft.HR.Business.Operations.Interfaces.Parameters;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters.Regions;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Parameters;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Datasoft.HR.Api.Controllers
{
    /// <summary>
    /// Region controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class RegionController : ControllerBase
    {
        private readonly IRegionOperations _regionOperations;
        public RegionController(IRegionOperations regionOperations)
        {
            _regionOperations = regionOperations;
        }

        /// <summary>
        /// Ülke arama apisi
        /// </summary>
        /// <param name="pagingInput"><see cref="PagingInput{RegionSearchDto}"/></param>
        /// <returns><see cref="PagingOutput{IEnumerable{Country}}"/></returns>
        [HttpPost("[action]")]
        public Task<Result<List<Country>>> SearchCountryAsync(PagingInput<RegionSearchDto> pagingInput)
        {
            return _regionOperations.SearchCountryAsync(pagingInput);
        }

        /// <summary>
        /// Eyalet arama apisi
        /// </summary>
        /// <param name="pagingInput"><see cref="PagingInput{RegionSearchDto}"/></param>
        /// <returns><see cref="PagingOutput{IEnumerable{State}}"/></returns>
        [HttpPost("[action]")]
        public Task<Result<List<State>>> SearchState(PagingInput<RegionSearchDto> pagingInput)
        {
            return _regionOperations.SearchStateAsync(pagingInput);
        }

        /// <summary>
        /// Şehir arama apisi
        /// </summary>
        /// <param name="pagingInput"><see cref="PagingInput{RegionSearchDto}"/></param>
        /// <returns><see cref="PagingOutput{IEnumerable{City}}"/></returns>
        [HttpPost("[action]")]
        public Task<Result<List<City>>> SearchCity(PagingInput<RegionSearchDto> pagingInput)
        {
            return _regionOperations.SearchCityAsync(pagingInput);
        }

        /// <summary>
        /// İlçe arama apisi
        /// </summary>
        /// <param name="pagingInput"><see cref="PagingInput{RegionSearchDto}"/></param>
        /// <returns><see cref="PagingOutput{IEnumerable{County}}"/></returns>
        [HttpPost("[action]")]
        public Task<Result<List<County>>> SearchCounty(PagingInput<RegionSearchDto> pagingInput)
        {
            return _regionOperations.SearchCountyAsync(pagingInput);
        }
    }
}
