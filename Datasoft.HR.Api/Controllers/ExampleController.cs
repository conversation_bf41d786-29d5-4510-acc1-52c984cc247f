using Datasoft.HR.Business.Operations.Interfaces;
using Datasoft.HR.Core.Common;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Examples;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Datasoft.HR.Api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ExampleController : ControllerBase
    {
        private readonly IExampleOperations _exampleOperations;
        public ExampleController(IExampleOperations exampleOperations)
        {
            _exampleOperations = exampleOperations;
        }

        [HttpPost("[action]")]
        public Task<Result<List<ExampleDto>>> Search([FromBody] PagingInput<string> request)
        {
            return _exampleOperations.SearchAsync(request);
        }

        [HttpPost("[action]")]
        public Task<Result<string>> Upsert([FromBody] ExampleDto request)
        {
            return _exampleOperations.UpsertAsync(request);
        }

        [HttpGet("[action]/{id}")]
        public Task<Result> Get(string id)
        {
            return _exampleOperations.GetAsync(id);
        }

        [HttpGet("[action]/{id}")]
        public Task<Result> Remove(string id)
        {
            return _exampleOperations.RemoveAsync(id);
        }
    }
}
