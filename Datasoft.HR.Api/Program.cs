using Datasoft.HR.Api.Middlewares;
using Datasoft.HR.Business;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Migration;
using DatasoftCommon.ApiClients;
using DatasoftCommon.CacheServices.Extensions;
using DatasoftCommon.CacheServices.Redis;
using DatasoftCommon.Configs;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.OpenApi.Models;
using MongoDB.Bson;
using MongoDB.Bson.IO;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Conventions;
using MongoDB.Bson.Serialization.Serializers;
using MongoDB.Driver;
using MongoDB.Driver.Core.Events;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;

var builder = WebApplication.CreateBuilder(args);


builder.Services.ConfigureJwtAuthService(builder.Environment);

builder.Services.AddScoped<TokenTransporter>();
builder.Services.AddRedis(options =>
{
    options.ConnectionString =builder.Configuration.GetConnectionString("Redis");
});
builder.Services.AddMemory();

// Add services to the container.

builder.Services.AddControllers(options =>
{
    options.OutputFormatters.RemoveType<HttpNoContentOutputFormatter>();
}).AddJsonOptions(options =>
{
    options.JsonSerializerOptions.Converters.Add(new NullableDateTimeConverter());
    options.JsonSerializerOptions.Converters.Add(new TrimmedStringConverter());
});

builder.Services.AddTransient<ErrorHandlingMiddleware>();
builder.Services.AddTransient<TokenMiddleware>();
builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
builder.Services.AddScoped(sp =>
{
    return sp.GetService<IHttpContextAccessor>()?.HttpContext?.User?.Identity as ClaimsIdentity;
});
builder.Services.AddScoped(sp =>
{
    var identity = sp.GetService<IHttpContextAccessor>()?.HttpContext?.User?.Identity as ClaimsIdentity;
    return new Datasoft.HR.Core.Common.LoginUser(identity);
});

BsonSerializer.RegisterSerializer(new MongoDBCustomDateTimeSerializer());
BsonSerializer.RegisterSerializer(typeof(decimal), new DecimalSerializer(BsonType.Decimal128));
BsonSerializer.RegisterSerializer(typeof(decimal?), new NullableSerializer<decimal>(new DecimalSerializer(BsonType.Decimal128)));

ConventionRegistry.Remove("__defaults__");
ConventionRegistry.Register("__defaults__", MongoDbDefaultConventionPack.Instance, t => true);
builder.Services.AddSingleton<IMongoClient>(sp =>
{
    var mongoClientSettings = MongoClientSettings.FromConnectionString(builder.Configuration.GetConnectionString("Mongo"));

    var jws = new JsonWriterSettings { Indent = true };

    mongoClientSettings.ClusterConfigurator = clusterBuilder =>
    {
        clusterBuilder.Subscribe<CommandStartedEvent>(e =>
        {
            Console.WriteLine(e.Command.ToJson(jws));
        });
        clusterBuilder.Subscribe<CommandSucceededEvent>(e =>
        {
            Console.WriteLine(e.Reply.ToJson(jws));
        });
    };

    return new MongoClient(mongoClientSettings);
});

builder.Services.AddScoped(sp => sp.GetService<IMongoClient>().StartSession());

BusinessDIModule.Inject(builder.Services, builder.Configuration, builder.Environment);
MigrationDIModule.Inject(builder.Services);

//builder.Services.AddSwaggerGen(c =>
//{
//    c.SwaggerDoc("v1", new OpenApiInfo { Title = "HR API V1", Version = "v1" });
//    c.ResolveConflictingActions(apiDescriptions => apiDescriptions.First());
//    c.CustomSchemaIds(x => x.FullName);
//    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
//    {
//        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
//        Name = "Authorization",
//        In = ParameterLocation.Header,
//        Type = SecuritySchemeType.ApiKey
//    });
//});

// automatic generated api documentation
builder.Services.AddSwaggerGen(x =>
{
    x.CustomSchemaIds(type =>
    {
        // return type.FullName;
        return GenerateTypeNames(type);
    });
    static string GenerateTypeNames(Type type)
    {
        if (type.IsGenericType)
        {
            var typeDef = type.GetGenericTypeDefinition();
            var baseName = typeDef.FullName?.Split('`')[0];
            var argNames = type.GenericTypeArguments.Select(t => t.IsGenericType ? GenerateTypeNames(t) : t.Name);
            return $"{baseName}_{string.Join("_", argNames)}".Replace(".", "_").Replace("+", "_");
        }
        if (type.IsArray)
        {
            return $"{GenerateTypeNames(type.GetElementType())}Array";
        }
        return type.FullName!.Replace(".", "_").Replace("+", "_");
    }

    x.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = $"Datasoft HR API ({Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")})",
        Version = "v1",
        Description = "API Documentation page for Datasoft ERP software"
    });

    // x.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "DataSoft.Api.xml"));
    x.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey
    });
});

var app = builder.Build();

app.UseSwagger();
app.UseSwaggerUI(c => c.SwaggerEndpoint("v1/swagger.json", "HR API V1"));
// Configure the HTTP request pipeline.

app.UseHttpsRedirection();
app.UseCors(builder => builder.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader());

app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

app.UseMiddleware<ErrorHandlingMiddleware>();
app.UseMiddleware<TokenMiddleware>();

app.UseEndpoints(endpoints =>
{
    endpoints.MapControllers().RequireAuthorization();
});

ServiceLocator.InjectServiceProvider(app.Services);

app.Services.GetRequiredService<RedisLockService>().Clear();
app.Services.CreateScope().ServiceProvider.GetRequiredService<Migrator>().MigrateAdminAsync().Wait();

app.Run();



public class NullableDateTimeConverter : JsonConverter<DateTime?>
{
    public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var stringValue = reader.GetString();
        if (string.IsNullOrWhiteSpace(stringValue))
            return null;

        return DateTime.Parse(stringValue);
    }

    public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
    {
        if (value != null)
            writer.WriteStringValue(value.Value);
    }
}

public class TrimmedStringConverter : JsonConverter<string>
{
    public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        return reader.GetString()?.Trim();
    }

    public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value?.Trim());
    }
}

public class MongoDBCustomDateTimeSerializer : DateTimeSerializer
{
    public override DateTime Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        var obj = base.Deserialize(context, args);
        return obj.ToLocalTime();
    }
}
public class MongoDBCustomEnumerableSerializer<T> : EnumerableSerializerBase<IEnumerable<T>, T>, IBsonArraySerializer, IChildSerializerConfigurable
{
    // constructors
    /// <summary>
    /// Initializes a new instance of the <see cref="MongoDBCustomEnumerableSerializer{T}"/> class.
    /// </summary>
    public MongoDBCustomEnumerableSerializer()
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="MongoDBCustomEnumerableSerializer{TItem}"/> class.
    /// </summary>
    /// <param name="itemSerializer">The item serializer.</param>
    public MongoDBCustomEnumerableSerializer(IBsonSerializer<T> itemSerializer)
        : base(itemSerializer)
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="MongoDBCustomEnumerableSerializer{T}" /> class.
    /// </summary>
    /// <param name="serializerRegistry">The serializer registry.</param>
    public MongoDBCustomEnumerableSerializer(IBsonSerializerRegistry serializerRegistry)
        : base(serializerRegistry)
    {
    }

    // public methods
    /// <summary>
    /// Returns a serializer that has been reconfigured with the specified item serializer.
    /// </summary>
    /// <param name="itemSerializer">The item serializer.</param>
    /// <returns>The reconfigured serializer.</returns>
    public MongoDBCustomEnumerableSerializer<T> WithItemSerializer(IBsonSerializer<T> itemSerializer)
    {
        return new MongoDBCustomEnumerableSerializer<T>(itemSerializer);
    }

    protected override void AddItem(object accumulator, T item)
    {
        ((List<T>)accumulator).Add(item);
    }

    protected override object CreateAccumulator()
    {
        return new List<T>();
    }

    protected override IEnumerable<T> EnumerateItemsInSerializationOrder(IEnumerable<T> value)
    {
        return value;
    }

    protected override IEnumerable<T> FinalizeResult(object accumulator)
    {
        return (List<T>)accumulator;
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, IEnumerable<T> value)
    {
        if (value == null)
            base.Serialize(context, args, Enumerable.Empty<T>());
        else
            base.Serialize(context, args, value);
    }

    // explicit interface implementations
    IBsonSerializer IChildSerializerConfigurable.ChildSerializer
    {
        get { return ItemSerializer; }
    }

    IBsonSerializer IChildSerializerConfigurable.WithChildSerializer(IBsonSerializer childSerializer)
    {
        return WithItemSerializer((IBsonSerializer<T>)childSerializer);
    }
}

public class MongoDbDefaultConventionPack : IConventionPack
{
    // private static fields
    private static readonly IConventionPack __defaultConventionPack = new MongoDbDefaultConventionPack();

    // private fields
    private readonly IEnumerable<IConvention> _conventions;

    // constructors
    /// <summary>
    /// Initializes a new instance of the <see cref="MongoDbDefaultConventionPack" /> class.
    /// </summary>
    private MongoDbDefaultConventionPack()
    {
        _conventions = new List<IConvention>
            {
                new ReadWriteMemberFinderConvention(),
                //new NamedIdMemberConvention(new [] { "Id", "id", "_id" }),
                new NamedExtraElementsMemberConvention(new [] { "ExtraElements" }),
                new IgnoreIfNullConvention(true),
                new IgnoreExtraElementsConvention(true),
                new ImmutableTypeClassMapConvention(),
                new NamedParameterCreatorMapConvention(),
                new StringObjectIdIdGeneratorConvention(), // should be before LookupIdGeneratorConvention
                new LookupIdGeneratorConvention(),
            };
    }

    // public static properties
    /// <summary>
    /// Gets the instance.
    /// </summary>
    public static IConventionPack Instance
    {
        get { return __defaultConventionPack; }
    }

    // public properties
    /// <summary>
    /// Gets the conventions.
    /// </summary>
    public IEnumerable<IConvention> Conventions
    {
        get { return _conventions; }
    }
}