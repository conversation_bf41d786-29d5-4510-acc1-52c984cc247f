﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
    <PackageReference Include="SkiaSharp" Version="2.88.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\DatasoftCommon\DatasoftCommon\DatasoftCommon.csproj" />
    <ProjectReference Include="..\Datasoft.HR.Conversions\Datasoft.HR.Conversions.csproj" />
    <ProjectReference Include="..\Datasoft.HR.Core\Datasoft.HR.Core.csproj" />
    <ProjectReference Include="..\Datasoft.HR.Repository\Datasoft.HR.Repository.csproj" />
    <ProjectReference Include="..\Datasoft.HR.Utility\Datasoft.HR.Utility.csproj" />
    <ProjectReference Include="..\Datasoft.HR.ValidationInfrastructure\Datasoft.HR.ValidationInfrastructure.csproj" />
  </ItemGroup>

</Project>
