﻿using Datasoft.HR.Core.Common;
using Datasoft.HR.Utility;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace Datasoft.HR.Business.Common
{
    public static class OrderByExtensions
    {
        public static IAggregateFluent<T> OrderBy<T>(this IAggregateFluent<T> source, List<OrderInput> orders)
        {
            if (source == null || orders?.Any() != true)
                return source;

            var sortProps = orders.Select(s => $"{s.PropertyName.CapitalizeFirstLetter()}:{(s.Asc ? 1 : -1)}");
            source = source.Sort("{" + string.Join(",", sortProps) + "}");

            return source;
        }

        public static IOrderedMongoQueryable<T> OrderBy<T>(this IMongoQueryable<T> source, List<OrderInput> orders)
        {
            if (source == null || orders == null)
                return (IOrderedMongoQueryable<T>)source;

            var thenBy = false;

            foreach (var order in orders)
            {
                source = OrderBy((IOrderedMongoQueryable<T>)source, order.PropertyName, !order.Asc, thenBy);
                thenBy = true;
            }

            return (IOrderedMongoQueryable<T>)source;
        }


        private static PropertyInfo GetNestedPropertyInfo(string nestedPropertyName, Type type)
        {
            PropertyInfo info = null;
            foreach (var part in nestedPropertyName.Split('.'))
            {
                info = type.GetProperty(part.CapitalizeFirstLetter());
                type = info.PropertyType;
            }

            return info;
        }

        private static IOrderedMongoQueryable<T> OrderBy<T>(this IQueryable<T> source, string propertyName, bool desc, bool thenBy)
        {
            string prefix = thenBy ? "Then" : "Order";
            string command = prefix + (desc ? "ByDescending" : "By");

            var type = typeof(T);
            var property = GetNestedPropertyInfo(propertyName, type);
            var parameter = Expression.Parameter(type, "p");

            Expression propertyAccess = parameter;
            foreach (var nestedPropertyName in propertyName.Split('.'))
                propertyAccess = Expression.Property(propertyAccess, nestedPropertyName.CapitalizeFirstLetter());

            var orderByExpression = Expression.Lambda(propertyAccess, parameter);

            var resultExpression = Expression.Call(typeof(Queryable),
                command,
                new Type[] { type, property.PropertyType },
                source.Expression,
                Expression.Quote(orderByExpression));

            return (IOrderedMongoQueryable<T>)source.Provider.CreateQuery<T>(resultExpression);
        }
    }
}
