﻿using Datasoft.HR.Core.Common;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

namespace Datasoft.HR.Business.Common
{
    /// <summary>
    /// Static helper methods for paged results.
    /// </summary>
    public static class PagingExtensions
    {

        /// <summary>
        /// Applies given paging properties to the result set.
        /// </summary>
        /// <typeparam name="T">Type of the object that will be returned</typeparam>
        /// <param name="queryable">Data source</param>
        /// <param name="pagingOptions">Paging options to be applied</param>
        private static List<T> ToPagedListData<T>(this IEnumerable<T> queryable, PagingInput pagingOptions)
        {
            if (pagingOptions == null || !pagingOptions.EnablePaging)
                return queryable.ToList();

            if (pagingOptions.PageSize < 1)
            {
                pagingOptions.PageSize = 10;
            }

            var rowsToSkip = (pagingOptions.PageNumber - 1) * pagingOptions.PageSize;
            return queryable.Skip(rowsToSkip).Take(pagingOptions.PageSize).ToList();
        }

        /// <summary>
        /// Returns the paging output, given the data source and paging options
        /// </summary>
        /// <typeparam name="TEntity">Type of the resulting entity</typeparam>
        /// <param name="query">Data source</param>
        /// <param name="pagingOptions">Paging options to be applied</param>
        /// <returns>A PagingOutput object, containing information about results</returns>
        public static PagedData<List<TEntity>> ToPagedList<TEntity>(this IEnumerable<TEntity> query, PagingInput pagingOptions)
        {
            if (pagingOptions == null || !pagingOptions.EnablePaging)
            {
                var data = query.ToList();
                return new PagedData<List<TEntity>>
                {
                    Data = data,
                    RecordCount = data.Count,
                    PageCount = 1,
                    PageIndex = 1
                };
            }

            var count = query.Count();
            var totalPages = (int)Math.Ceiling((double)count / pagingOptions.PageSize);

            return new PagedData<List<TEntity>>
            {
                Data = query.ToPagedListData(pagingOptions),
                RecordCount = count,
                PageCount = totalPages,
                PageIndex = pagingOptions.PageNumber
            };
        }

        /// <summary>
        /// Returns the paging output, given the data source and paging options
        /// </summary>
        /// <typeparam name="TEntity">Type of the resulting entity</typeparam>
        /// <param name="query">Data source</param>
        /// <param name="pagingOptions">Paging options to be applied</param>
        /// <returns>A PagingOutput object, containing information about results</returns>
        public static PagedData<List<TEntity>> ToPagedList<TEntity>(this IOrderedEnumerable<TEntity> query, PagingInput pagingOptions)
        {
            if (pagingOptions == null || !pagingOptions.EnablePaging)
            {
                var data = query.ToList();
                return new PagedData<List<TEntity>>
                {
                    Data = data,
                    RecordCount = data.Count,
                    PageCount = 1,
                    PageIndex = 1
                };
            }

            var count = query.Count();
            var totalPages = (int)Math.Ceiling((double)count / pagingOptions.PageSize);

            return new PagedData<List<TEntity>>
            {
                Data = query.ToPagedListData(pagingOptions),
                RecordCount = count,
                PageCount = totalPages,
                PageIndex = pagingOptions.PageNumber
            };
        }

        /// <summary>
        /// Applies given paging properties to the result set.
        /// </summary>
        /// <typeparam name="T">Type of the object that will be returned</typeparam>
        /// <param name="queryable">Data source</param>
        /// <param name="pagingOptions">Paging options to be applied</param>
        private static async Task<List<T>> ToPagedListDataAsync<T>(this IMongoQueryable<T> queryable, PagingInput pagingOptions)
        {
            if (pagingOptions == null || !pagingOptions.EnablePaging)
                return await queryable.ToListAsync();

            if (pagingOptions.PageSize < 1)
                pagingOptions.PageSize = 10;

            var rowsToSkip = (pagingOptions.PageNumber - 1) * pagingOptions.PageSize;
            return await queryable.Skip(rowsToSkip).Take(pagingOptions.PageSize).ToListAsync();
        }

        /// <summary>
        /// Applies given paging properties to the result set.
        /// </summary>
        /// <typeparam name="T">Type of the object that will be returned</typeparam>
        /// <param name="queryable">Data source</param>
        /// <param name="pagingOptions">Paging options to be applied</param>
        private static List<T> ToPagedListData<T>(this IQueryable<T> queryable, PagingInput pagingOptions)
        {
            if (pagingOptions == null || !pagingOptions.EnablePaging)
                return queryable.ToList();

            if (pagingOptions.PageSize < 1)
                pagingOptions.PageSize = 10;

            var rowsToSkip = (pagingOptions.PageNumber - 1) * pagingOptions.PageSize;
            return queryable.Skip(rowsToSkip).Take(pagingOptions.PageSize).ToList();
        }


        /// <summary>
        /// Returns the paging output, given the data source and paging options
        /// </summary>
        /// <typeparam name="TEntity">Type of the resulting entity</typeparam>
        /// <param name="query">Data source</param>
        /// <param name="pagingOptions">Paging options to be applied</param>
        /// <returns>A PagingOutput object, containing information about results</returns>
        public static async Task<PagedData<List<TEntity>>> ToPagedListAsync<TEntity>(this IMongoQueryable<TEntity> query, PagingInput pagingOptions)
        {
            if (pagingOptions == null || !pagingOptions.EnablePaging)
            {
                var data = await query.ToListAsync();
                return new PagedData<List<TEntity>>
                {
                    Data = data,
                    RecordCount = data.Count,
                    PageCount = 1,
                    PageIndex = 1
                };
            }

            var count = await query.CountAsync();
            var totalPages = (int)Math.Ceiling((double)count / pagingOptions.PageSize);

            return new PagedData<List<TEntity>>
            {
                Data = await query.ToPagedListDataAsync(pagingOptions),
                RecordCount = count,
                PageCount = totalPages,
                PageIndex = pagingOptions.PageNumber
            };
        }

        /// <summary>
        /// Returns the paging output, given the data source and paging options
        /// </summary>
        /// <typeparam name="TEntity">Type of the resulting entity</typeparam>
        /// <param name="query">Data source</param>
        /// <param name="pagingOptions">Paging options to be applied</param>
        /// <returns>A PagingOutput object, containing information about results</returns>
        public static async Task<PagedData<List<TEntity>>> ToPagedListAsync<TEntity>(this IOrderedMongoQueryable<TEntity> query, PagingInput pagingOptions)
        {
            if (pagingOptions == null || !pagingOptions.EnablePaging)
            {
                var data = await query.ToListAsync();
                return new PagedData<List<TEntity>>
                {
                    Data = data,
                    RecordCount = data.Count,
                    PageCount = 1,
                    PageIndex = 1
                };
            }

            var count = await query.CountAsync();
            var totalPages = (int)Math.Ceiling((double)count / pagingOptions.PageSize);

            return new PagedData<List<TEntity>>
            {
                Data = await query.ToPagedListDataAsync(pagingOptions),
                RecordCount = count,
                PageCount = totalPages,
                PageIndex = pagingOptions.PageNumber
            };
        }

        /// <summary>
        /// Returns the paging output, given the data source and paging options
        /// </summary>
        /// <typeparam name="TEntity">Type of the resulting entity</typeparam>
        /// <param name="query">Data source</param>
        /// <param name="pagingOptions">Paging options to be applied</param>
        /// <returns>A PagingOutput object, containing information about results</returns>
        public static PagedData<List<TEntity>> ToPagedList<TEntity>(this IQueryable<TEntity> query, PagingInput pagingOptions)
        {
            if (pagingOptions == null || !pagingOptions.EnablePaging)
            {
                var data = query.ToList();
                return new PagedData<List<TEntity>>
                {
                    Data = data,
                    RecordCount = data.Count,
                    PageCount = 1,
                    PageIndex = 1
                };
            }

            var count = query.Count();
            var totalPages = (int)Math.Ceiling((double)count / pagingOptions.PageSize);

            return new PagedData<List<TEntity>>
            {
                Data = query.ToPagedListData(pagingOptions),
                RecordCount = count,
                PageCount = totalPages,
                PageIndex = pagingOptions.PageNumber
            };
        }

        /// <summary>
        /// Returns the paging output, given the data source and paging options
        /// </summary>
        /// <typeparam name="TEntity"></typeparam>
        /// <param name="aggregation">Aggregation data source</param>
        /// <param name="pagingOptions">Paging options to be applied</param>
        /// <returns>A PagingOutput object, containing information about results</returns>
        public static PagedData<List<TEntity>> ToPagedList<TEntity>(this IAggregateFluent<TEntity> aggregation, PagingInput pagingOptions)
        {
            if (pagingOptions == null || pagingOptions.EnablePaging == false)
                return new PagedData<List<TEntity>>
                {
                    Data = aggregation.ToList()
                };

            var countFacet = AggregateFacet.Create("count",
                PipelineDefinition<TEntity, AggregateCountResult>.Create(new[]
                {
                    PipelineStageDefinitionBuilder.Count<TEntity>()
                }));

            var dataFacet = AggregateFacet.Create("data",
                PipelineDefinition<TEntity, TEntity>.Create(new[]
                {
                    PipelineStageDefinitionBuilder.Skip<TEntity>((pagingOptions.PageNumber - 1) * pagingOptions.PageSize),
                    PipelineStageDefinitionBuilder.Limit<TEntity>(pagingOptions.PageSize),
                }));

            var facets = aggregation.Facet(countFacet, dataFacet).ToList().First().Facets;
            var count = facets.FirstOrDefault(x => x.Name == "count")?.Output<AggregateCountResult>()?.FirstOrDefault()?.Count ?? 0;
            var data = facets.FirstOrDefault(x => x.Name == "data")?.Output<TEntity>()?.ToList();

            return new PagedData<List<TEntity>>
            {
                Data = data,
                PageIndex = pagingOptions.PageNumber,
                PageCount = (int)Math.Ceiling((double)count / pagingOptions.PageSize),
                RecordCount = (int)count
            };
        }

        /// <summary>
        /// Returns the paging output, given the data source and paging options
        /// </summary>
        /// <typeparam name="TEntity"></typeparam>
        /// <param name="aggregation">Aggregation data source</param>
        /// <param name="pagingOptions">Paging options to be applied</param>
        /// <returns>A PagingOutput object, containing information about results</returns>
        public static async Task<PagedData<List<TEntity>>> ToPagedListAsync<TEntity>(this IAggregateFluent<TEntity> aggregation, PagingInput pagingOptions)
        {
            if (pagingOptions == null || pagingOptions.EnablePaging == false)
                return new PagedData<List<TEntity>>
                {
                    Data = aggregation.ToList()
                };

            var countFacet = AggregateFacet.Create("count",
                PipelineDefinition<TEntity, AggregateCountResult>.Create(new[]
                {
                    PipelineStageDefinitionBuilder.Count<TEntity>()
                }));

            var dataFacet = AggregateFacet.Create("data",
                PipelineDefinition<TEntity, TEntity>.Create(new[]
                {
                    PipelineStageDefinitionBuilder.Skip<TEntity>((pagingOptions.PageNumber - 1) * pagingOptions.PageSize),
                    PipelineStageDefinitionBuilder.Limit<TEntity>(pagingOptions.PageSize),
                }));

            var facets = (await aggregation.Facet(countFacet, dataFacet).ToListAsync()).First().Facets;
            var count = facets.FirstOrDefault(x => x.Name == "count")?.Output<AggregateCountResult>()?.FirstOrDefault()?.Count ?? 0;
            var data = facets.FirstOrDefault(x => x.Name == "data")?.Output<TEntity>()?.ToList();

            return new PagedData<List<TEntity>>
            {
                Data = data,
                PageIndex = pagingOptions.PageNumber,
                PageCount = (int)Math.Ceiling((double)count / pagingOptions.PageSize),
                RecordCount = (int)count
            };
        }
    }
}
