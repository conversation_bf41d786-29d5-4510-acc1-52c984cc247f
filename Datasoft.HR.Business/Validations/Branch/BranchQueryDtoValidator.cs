﻿using Datasoft.HR.DTO.Branch;
using Datasoft.HR.ValidationInfrastructure;
using Datasoft.HR.ValidationInfrastructure.ErrorCodes;


namespace Datasoft.HR.Business.Validations.Branch
{
    public class BranchQueryDtoValidator : AbstractValidator<BranchQueryDto>
    {
        protected override void ProccessRules()
        {
            // Eğer ikisi de doluysa BranchCodeStart <= BranchCodeEnd olmalı
            if (!string.IsNullOrWhiteSpace(_instanceToValidate.BranchCodeStart) &&
                !string.IsNullOrWhiteSpace(_instanceToValidate.BranchCodeEnd))
            {
                var start = _instanceToValidate.BranchCodeStart!;
                var end = _instanceToValidate.BranchCodeEnd!;

                if (string.Compare(start, end, StringComparison.OrdinalIgnoreCase) > 0)
                {
                    AddError(x => x.BranchCodeStart, opt =>
                    {
                        opt.ErrorCode = ErrorCodeValues.InvalidRange;
                        opt.ErrorMessage = "BranchCodeStart, BranchCodeEnd'den büyük olamaz.";
                    });
                }
            }

            // Country, City, County kontrolleri aynı şekilde devam eder
            if (_instanceToValidate.Country != null && _instanceToValidate.Country.Id <= 0)
            {
                AddError(x => x.Country, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.InvalidRange;
                    opt.ErrorMessage = "Geçersiz ülke seçimi.";
                });
            }

            if (_instanceToValidate.City != null && _instanceToValidate.City.Id <= 0)
            {
                AddError(x => x.City, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.InvalidRange;
                    opt.ErrorMessage = "Geçersiz şehir seçimi.";
                });
            }

            if (_instanceToValidate.County != null && _instanceToValidate.County.Id <= 0)
            {
                AddError(x => x.County, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.InvalidRange;
                    opt.ErrorMessage = "Geçersiz ilçe seçimi.";
                });
            }
        }
    }

}
