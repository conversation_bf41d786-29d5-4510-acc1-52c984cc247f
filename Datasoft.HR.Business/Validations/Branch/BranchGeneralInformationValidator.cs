﻿using Datasoft.HR.Core.Entities.Branch;
using Datasoft.HR.Core.Enums;
using Datasoft.HR.ValidationInfrastructure;

namespace Datasoft.HR.Business.Validations.Branch
{
    public class BranchGeneralInformationValidator : AbstractValidator<BranchGeneralInformation>
    {
        protected override void ProccessRules()
        {
            // BranchCode zorunlu ve maksimum 15 karakter
            IsNotNullOrWhiteSpace(x => x.BranchCode, "Şube kodu zorunludur.");
            MaxLength(x => x.BranchCode, 15, true, "Şube kodu en fazla 15 karakter olabilir.");

            // BranchName zorunlu ve maksimum 40 karakter
            IsNotNullOrWhiteSpace(x => x.<PERSON>ame, "Şube adı zorunludur.");
            MaxLength(x => x.BranchName, 40, true, "Şube adı en fazla 40 karakter olabilir.");

            // BranchType zorunlu (enum kontrolü)
            NotNull(x => x.BranchType, "Şube türü zorunludur.");

            // AuthorizedBranch, BranchType mükellefiyetsiz ise zorunlu
            NotNull(
                x => x.AuthorizedBranch,
                x => x.BranchType == BranchType.NonTaxable,
                "Yetkili şube zorunludur çünkü şube türü mükellefiyetsiz.");

            // MersisNo zorunlu değil, ona dair kural yok

            // TaxOffice zorunlu
            NotNull(x => x.TaxOffice, "Vergi dairesi bilgisi zorunludur.");
        }
    }
}
