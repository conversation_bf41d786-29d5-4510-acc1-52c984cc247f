﻿using Datasoft.HR.Business.Validations.Common;
using Datasoft.HR.Business.Validations.Companies;
using Datasoft.HR.DTO.Branch;
using Datasoft.HR.ValidationInfrastructure;

namespace Datasoft.HR.Business.Validations.Branch
{
    public class BranchValidator : AbstractValidator<BranchDto>
    {
        protected override void ProccessRules()
        {
            if (_instanceToValidate.GeneralInformation != null)
                new BranchGeneralInformationValidator()
                    .SetThrowOnException()
                    .Validate(_instanceToValidate.GeneralInformation);

            if (_instanceToValidate.SgkInformation != null)
                new SgkInformationValidator()
                    .SetThrowOnException()
                    .Validate(_instanceToValidate.SgkInformation);

            if (_instanceToValidate.Address != null)
                new AddressValidator()
                    .SetThrowOnException()
                    .Validate(_instanceToValidate.Address);

            if (_instanceToValidate.SMMMInformation != null)
                new SMMMInformationValidatior()
                    .SetThrowOnException()
                    .Validate(_instanceToValidate.SMMMInformation);

        }
    }
}
