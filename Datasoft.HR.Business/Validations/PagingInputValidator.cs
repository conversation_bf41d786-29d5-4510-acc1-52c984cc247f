﻿using Datasoft.HR.Core.Common;
using Datasoft.HR.ValidationInfrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Validations
{
    public class PagingInputValidator : AbstractValidator<IPagingInput>
    {
        protected override void ProccessRules()
        {
            if (_instanceToValidate.PagingOptions?.EnablePaging ?? false)
            {
                GreaterThan(x => x.PagingOptions.PageNumber, 0);
                GreaterThan(x => x.PagingOptions.PageSize, 0);
            }
        }
    }
}
