﻿using Datasoft.HR.Business.Validations.Common;
using Datasoft.HR.Core.Entities.Common;
using Datasoft.HR.Core.Entities.Company;
using Datasoft.HR.ValidationInfrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Validations.Companies
{
    public class YMMInformationValidatior : AbstractValidator<YMMInformation>
    {
        protected override void ProccessRules()
        {
            IsNotNullOrWhiteSpace(x => x.Name);
            IsNotNullOrWhiteSpace(x => x.Surname);
            IsNotNullOrWhiteSpace(x => x.Title);
            IsNotNullOrWhiteSpace(x => x.PersonalTitle);

            var tcknOrVknRequiredFields = new List<Expression<Func<YMMInformation, object>>>
            {
                x=>x.IdentityNumber,
                x=>x.TaxNumber
            };

            if (AtLeastOneRequired(tcknOrVknRequiredFields, errorMessage: "TCKN veya VKN alanlarından biri zorunludur."))
            {
                if (!string.IsNullOrEmpty(_instanceToValidate.IdentityNumber))
                {
                    OnlyContainsNumber(x => x.IdentityNumber, errorMessage: "Kimlik numarası sayısal değerlerden oluşmalı");
                    ExactLength(x => x.IdentityNumber, 11, errorMessage: "Kimlik numarası 11 haneli olmalı");
                }

                if (!string.IsNullOrEmpty(_instanceToValidate.TaxNumber))
                {
                    OnlyContainsNumber(x => x.TaxNumber, errorMessage: "Vergi numarası sayısal değerlerden oluşmalı");
                    ExactLength(x => x.TaxNumber, 10, errorMessage: "Vergi numarası 10 haneli olmalı");
                }
            }

            IsNotNullOrWhiteSpace(x => x.RegistrationNumber);
            IsNotNullOrWhiteSpace(x => x.StampNumber);
            NotNull(x => x.ContractDate);
            IsNotNullOrWhiteSpace(x => x.ContractNumber);
            IsNotNullOrWhiteSpace(x => x.ContractType);

            if (_instanceToValidate.Address != null)
            {
                var validationResult = new AddressValidator()
                    .SetThrowOffException()
                    .CombineSetPrefix(nameof(_instanceToValidate.Address))
                    .Validate(_instanceToValidate.Address);

                ImportValidationResult(validationResult);
            }

            if (_instanceToValidate.Contact != null)
            {
                var validationResult = new ContactValidator()
                    .SetIgnoredNames(nameof(Contact.Name), nameof(Contact.Surname))
                    .SetThrowOffException()
                    .CombineSetPrefix(nameof(_instanceToValidate.Contact))
                    .Validate(_instanceToValidate.Contact);

                ImportValidationResult(validationResult);
            }
        }
    }
}
