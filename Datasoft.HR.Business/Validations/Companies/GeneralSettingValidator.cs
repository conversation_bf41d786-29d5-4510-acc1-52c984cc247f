﻿using Datasoft.HR.Business.Validations.Common;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Enums;
using Datasoft.HR.DTO.Company;
using Datasoft.HR.ValidationInfrastructure;
using Datasoft.HR.ValidationInfrastructure.ErrorCodes;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Validations.Companies
{
    public class GeneralSettingValidator : AbstractValidator<GeneralSettingDto>
    {
        private readonly LoginUser _loginUser;
        public GeneralSettingValidator(LoginUser loginUser)
        {
            _loginUser = loginUser;
        }

        protected override void ProccessRules()
        {

            // Şirket adı boş geçilemez
            IsNotNullOrWhiteSpace(x => x.CompanyName, errorMessage: "Şirket adı boş geçilemez");

            // Vergi mevzuatı ülkesi boş geçilemez
            if (NotNull(x => x.TaxLegislationCountry, errorMessage: "Vergi mevzuatı ülkesi boş geçilemez"))
                GreaterThan(x => x.TaxLegislationCountry.Id, 0, errorMessage: "Vergi mevzuatı ülkesi 'Id' değeri 0'dan büyük olmalı");

            // Dil boş geçilemez
            if (NotNull(x => x.Language))
                GreaterThan(x => x.Language.Id, 0, errorMessage: "Dil seçiminde 'Id' değeri 0'dan büyük olmalı");

            // Hukuki yapı boş geçilemez
            MustBeNotEqual(x => x.LegalStructure, LegalStructure.None, errorMessage: "Hukiki yapı boş geçilemez");

            // Gerçek kişi ise
            if (_instanceToValidate.LegalStructure == LegalStructure.Real)
            {
                // TCKN Boş geçilemez, sadece sayısal değer içermeli, 11 haneli olmalı 
                if (IsNotNullOrWhiteSpace(x => x.IdentityNumber, errorMessage: "Kimlik numarası boş geçilemez"))
                {
                    OnlyContainsNumber(x => x.IdentityNumber, errorMessage: "Kimlik numarası sayısal değerlerden oluşmalı");
                    ExactLength(x => x.IdentityNumber, 11, errorMessage: "Kimlik numarası 11 haneli olmalı");
                }

                // Ad boş geçilemez
                IsNotNullOrWhiteSpace(x => x.Name, errorMessage: "Ad boş geçilemez");

                // Soyad boş geçilemez
                IsNotNullOrWhiteSpace(x => x.Surname, errorMessage: "Soyad boş geçilemez");
            }

            // Tüzel kişi ise
            if (_instanceToValidate.LegalStructure == LegalStructure.Real)
            {
                // VKN Boş geçilemez, sadece sayısal değer içermeli, 10 haneli olmalı
                if (IsNotNullOrWhiteSpace(x => x.TaxNumber, errorMessage: "Vergi numarası boş geçilemez"))
                {
                    OnlyContainsNumber(x => x.TaxNumber, errorMessage: "Vergi numarası sayısal değerlerden oluşmalı");
                    ExactLength(x => x.TaxNumber, 10, errorMessage: "Vergi numarası 10 haneli olmalı");

                    IsNotNullOrWhiteSpace(x => x.PersonalTitle, errorMessage: "Ünvan boş geçilemez");
                }
            }

            // Vergi dairesi Id'si "0" olamaz
            if (_instanceToValidate.TaxOffice.Id.HasValue)
            {
                GreaterThan(x => x.TaxOffice.Id, 0, errorMessage: "Vergi dairesi seçiminde 'Id' değeri 0'dan büyük olmalı");

                // Vergi dairesi kodu boş geçilemez ve sayısal bir değer olmalıdır
                if (IsNotNullOrWhiteSpace(x => x.TaxOffice.AuditOfficeCode, errorMessage: "Vergi dairesi kodu boş geçilemez"))
                    OnlyContainsNumber(x => x.TaxOffice.AuditOfficeCode, errorMessage: "Vergi dairesi kodu sayısal değerlerden oluşmalı");

                // Vergi dairesi adı boş geçilemez
                IsNotNullOrWhiteSpace(x => x.TaxOffice.TaxOfficeName, errorMessage: "Vergi dairesi adı boş geçilemez");
            }
            else
            {
                // Vergi dairesi kodu veya adı boş değilse
                if (!string.IsNullOrWhiteSpace(_instanceToValidate.TaxOffice?.AuditOfficeCode) || !string.IsNullOrWhiteSpace(_instanceToValidate.TaxOffice?.TaxOfficeName))
                {
                    // Vergi dairesi kodu boş geçilemez ve sayısal bir değer olmalıdır
                    if (IsNotNullOrWhiteSpace(x => x.TaxOffice.AuditOfficeCode, errorMessage: "Vergi dairesi kodu boş geçilemez"))
                        OnlyContainsNumber(x => x.TaxOffice.AuditOfficeCode, errorMessage: "Vergi dairesi kodu sayısal değerlerden oluşmalı");

                    // Vergi dairesi adı boş geçilemez
                    IsNotNullOrWhiteSpace(x => x.TaxOffice.TaxOfficeName, errorMessage: "Vergi dairesi adı boş geçilemez");
                }
            }

            // Mernis no sayısal değer olmalı
            if (!string.IsNullOrEmpty(_instanceToValidate.MernisNumber))
                OnlyContainsNumber(x => x.MernisNumber, errorMessage: "Mernis no sayısal değerlerden oluşmalı");

            // Şirket adresi
            if (_instanceToValidate.Address != null)
                new AddressValidator().SetThrowOnException().Validate(_instanceToValidate.Address);

            if (!string.IsNullOrWhiteSpace(_instanceToValidate.Stamp))
            {
                if (TryParseBase64ToImage(_instanceToValidate.Stamp, out SKBitmap image, out long length))
                {
                    if ((length / 1024) > 40)
                        AddError(x => x.Stamp, opt =>
                        {
                            opt.ErrorMessage = "Resim dosyası maximum 40 kb olabilir.";
                            opt.ErrorCode = ErrorCodeValues.InvalidImageData;
                            opt.AttemptedValue = (length / 1024) + "kb";
                        });
                }
                else
                    AddError(x => x.Stamp, opt =>
                    {
                        opt.ErrorMessage = "Geçersiz resim dosyası";
                        opt.ErrorCode = ErrorCodeValues.InvalidImageData;
                    });
            }

            if (!string.IsNullOrWhiteSpace(_instanceToValidate.Logo))
            {
                if (TryParseBase64ToImage(_instanceToValidate.Logo, out SKBitmap image, out long length))
                {
                    if ((length / 1024) > 40)
                        AddError(x => x.Logo, opt =>
                        {
                            opt.ErrorMessage = "Resim dosyası maximum 40 kb olabilir.";
                            opt.ErrorCode = ErrorCodeValues.InvalidImageData;
                            opt.AttemptedValue = (length / 1024) + "kb";
                        });
                }
                else
                {
                    AddError(x => x.Logo, opt =>
                    {
                        opt.ErrorMessage = "Geçersiz resim dosyası";
                        opt.ErrorCode = ErrorCodeValues.InvalidImageData;
                    });
                }
            }

            if (_instanceToValidate.SMMMInformation != null)
                new SMMMInformationValidatior().SetThrowOnException().Validate(_instanceToValidate.SMMMInformation);
            
            if (_instanceToValidate.SgkInformation != null)
                new SgkInformationValidator().SetThrowOnException().Validate(_instanceToValidate.SgkInformation);
        }

        private bool TryParseBase64ToImage(string base64String, out SKBitmap image, out long length)
        {
            try
            {
                if (base64String.StartsWith("data:"))
                {
                    var start = base64String.IndexOf(",") + 1;
                    var end = base64String.Length - start;
                    base64String = base64String.Substring(start, end);
                }
                byte[] imageBytes = Convert.FromBase64String(base64String);
                using (var original = SKBitmap.Decode(imageBytes))
                {
                    image = original;
                    length = imageBytes.Length;
                }

                return true;
            }
            catch
            {
                length = 0;
                image = null;
                return false;
            }
        }
    }

}
