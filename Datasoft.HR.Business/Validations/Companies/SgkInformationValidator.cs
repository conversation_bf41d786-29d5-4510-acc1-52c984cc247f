﻿using Datasoft.HR.Core.Entities.Company;
using Datasoft.HR.ValidationInfrastructure;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Validations.Companies
{
    public class SgkInformationValidator : AbstractValidator<SgkInformation>
    {
        protected override void ProccessRules()
        {
            // Terk durumu seçiliyse Terk Tarihi zorunlu
            NotNull(x => x.DiscontinuationDate, x => x.IsDiscontinued == true, "Faaliyet sona erdiyse Terk Tarihi zorunludur.");

            // WorkplaceRegistrationCode: max 4 haneli numerik
            MaxLength(x => x.WorkplaceRegistrationCode, 4, true, "İşyeri kodu en fazla 4 karakter olmalıdır.");
            OnlyContainsNumber(x => x.WorkplaceRegistrationCode, "İşyeri kodu sadece sayılardan oluşmalıdır.");

            // SocialSecurityOfficeName: max 40 karakter
            MaxLength(x => x.SocialSecurityOfficeName, 40, true, "SGK müdürlüğü adı en fazla 40 karakter olmalıdır.");

            // SocialSecurityNumber: regex formatı
            RegexMatch(
                x => x.SocialSecurityNumber,
                @"^\d\.\d{5}\.\d{2}\.\d{2}\.\d{7}\.\d{3}\.\d{2}$",
                errorMessage: "SGK sicil numarası geçerli formatta olmalıdır. Örnek: 1.23456.78.91.2345678.912.34"
            );

            // VerificationCode: 2 haneli numerik
            ExactLength(x => x.VerificationCode, 2, true, "Kontrol numarası 2 haneli olmalıdır.");
            OnlyContainsNumber(x => x.VerificationCode, "Kontrol numarası sadece rakamlardan oluşmalıdır.");

            // SubcontractorCompanyNumber: 3 haneli numerik
            ExactLength(x => x.SubcontractorCompanyNumber, 3, true, "Taşeron firma kodu 3 haneli olmalıdır.");
            OnlyContainsNumber(x => x.SubcontractorCompanyNumber, "Taşeron firma kodu sadece rakamlardan oluşmalıdır.");

            // OccupationalBranchCode: 1–9 arası olmalı
            InclusiveBetween(x => x.OccupationalBranchCode??0, 1, 9, "İşkolu kodu 1 ile 9 arasında olmalıdır.");            
            
        }
    }
}
