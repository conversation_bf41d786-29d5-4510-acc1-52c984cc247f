﻿using Datasoft.HR.Business.Validations.Common;
using Datasoft.HR.Core.Common;
using Datasoft.HR.DTO.Company;
using Datasoft.HR.ValidationInfrastructure;
using Datasoft.HR.ValidationInfrastructure.ErrorCodes;
using DatasoftCommon.ApiClients.Accounting.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Validations.Companies
{
    public class BranchSettingValidator : AbstractValidator<BranchSettingDto>
    {
        private readonly LoginUser _loginUser;
        public BranchSettingValidator(LoginUser loginUser)
        {
            _loginUser = loginUser;

        }

        protected override void ProccessRules()
        {
            if (!_loginUser?.HaveBranch ?? false)
            {
                AddError(x => x, opt =>
                {
                    opt.ErrorMessage = "Şube ayarları sadece şubeli şirketlerde düzenlenebilir";
                    opt.ErrorCode = BranchSettingErrorCode.CannotConfigureBranchSetting;
                });
            }
            else
            {
                // Şirket adı boş geçilemez
                IsNotNullOrWhiteSpace(x => x.BranchName);

                // Kuruluş tarihi boş değil ise minimum value olamaz
                if (_instanceToValidate.EstablishmentInformation?.Date.HasValue ?? false)
                    GreaterThan(x => x.EstablishmentInformation.Date, DateTime.MinValue);

                // Kuruluş terk tarihi boş değil ise minimum value olamaz
                if (_instanceToValidate.EstablishmentInformation?.LeaveDate.HasValue ?? false)
                    GreaterThan(x => x.EstablishmentInformation.LeaveDate, DateTime.MinValue);

                // Şirket adresi
                if (_instanceToValidate.Address != null)
                {
                    var validationResult = new AddressValidator()
                        .SetThrowOffException()
                        .CombineSetPrefix(Prefix, nameof(_instanceToValidate.Address))
                        .Validate(_instanceToValidate.Address);

                    ImportValidationResult(validationResult);
                }

                if (_instanceToValidate.SMMMInformation != null)
                {
                    var validationResult = new SMMMInformationValidatior()
                        .SetThrowOffException()
                        .CombineSetPrefix(Prefix, nameof(_instanceToValidate.SMMMInformation))
                        .Validate(_instanceToValidate.SMMMInformation);

                    ImportValidationResult(validationResult);
                }
                if (_instanceToValidate.YMMInformation != null)
                {
                    var validationResult = new YMMInformationValidatior()
                        .SetThrowOffException()
                        .CombineSetPrefix(Prefix, nameof(_instanceToValidate.SMMMInformation))
                        .Validate(_instanceToValidate.YMMInformation);

                    ImportValidationResult(validationResult);
                }

                if (!string.IsNullOrWhiteSpace(_instanceToValidate.SGKRegistrationNumber))
                    IsValidSGKRegistirationNumber(x => x.SGKRegistrationNumber);
            }
        }
    }

}
