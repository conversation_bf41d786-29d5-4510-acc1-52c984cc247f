﻿using Datasoft.HR.DTO.Parameters;
using Datasoft.HR.ValidationInfrastructure;

namespace Datasoft.HR.Business.Validations.Parameters
{
    public class CitySearchValidator : AbstractValidator<RegionSearchDto>
    {
        protected override void ProccessRules()
        {
            GreaterThan(x => x.LanguageId, 0);

            if (_instanceToValidate.Id.HasValue)
            {
                GreaterThan(x => x.Id, 0);
            }
            else
            {
                if (_instanceToValidate.RootStateId.HasValue)
                    GreaterThan(x => x.RootStateId, 0);

                if (NotNull(x => x.RootCountryId))
                    GreaterThan(x => x.RootCountryId, 0);
            }
        }
    }
}
