﻿using Datasoft.HR.DTO.Parameters;
using Datasoft.HR.ValidationInfrastructure;

namespace Accounting.Business.Validations.Parameters
{
    public class CountrySearchValidator : AbstractValidator<RegionSearchDto>
    {
        protected override void ProccessRules()
        {
            if (_instanceToValidate.Id.HasValue)
                GreaterThan(x => x.Id, 0);

            GreaterThan(x => x.LanguageId, 0);
        }
    }
}
