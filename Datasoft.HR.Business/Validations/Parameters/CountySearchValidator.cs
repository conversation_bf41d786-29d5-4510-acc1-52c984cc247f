﻿using Datasoft.HR.DTO.Parameters;
using Datasoft.HR.ValidationInfrastructure;

namespace Datasoft.HR.Business.Validations.Parameters
{
    public class CountySearchValidator : AbstractValidator<RegionSearchDto>
    {
        protected override void ProccessRules()
        {
            GreaterThan(x => x.LanguageId, 0);

            if (_instanceToValidate.Id.HasValue)
            {
                GreaterThan(x => x.Id, 0);
            }
            else
            {
                if (!_instanceToValidate.IsForDeclaration)
                    if (NotNull(x => x.RootCityId))
                        GreaterThan(x => x.RootCityId, 0);
            }
        }
    }
}
