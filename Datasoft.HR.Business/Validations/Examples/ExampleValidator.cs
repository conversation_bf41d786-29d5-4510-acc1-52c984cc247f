﻿using Datasoft.HR.DTO.Examples;
using Datasoft.HR.ValidationInfrastructure;

namespace Datasoft.HR.Business.Validations.Examples
{
    public class ExampleValidator : AbstractValidator<ExampleDto>
    {
        protected override void ProccessRules()
        {            
            if (IsNotNullOrWhiteSpace(p => p.Code, "Kod alanı boş geçilemez"))
            {
                OnlyContainsNumber(p => p.Code, "Kod alanı sadece sayısal değerler içerebilir");
                ExactLength(p => p.Code!, 3, false, "Kod alanı 3 haneli olmak zorunda");
            }

            if (IsNotNullOrWhiteSpace(p => p.Name, "Ad alanı boş geçilemez"))
            {
                OnlyContainsAlphaNumberic(p => p.Name!, "Ad alanı sadece alfa numeric değerler içerebilir");
                MinLength(p => p.Name!, 3, false, "Ad alanı en az 3 haneli olmak zorunda");
                MaxLength(p => p.Name!, 25, false, "Ad alanı en fazla 25 haneli olmak zorunda");
            }
        }
    }
}
