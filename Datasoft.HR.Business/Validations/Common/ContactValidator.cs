﻿using Datasoft.HR.Core.Entities.Common;
using Datasoft.HR.ValidationInfrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Validations.Common
{
    public class ContactValidator : AbstractValidator<Contact>
    {
        private IEnumerable<string> _ignoredNames = Enumerable.Empty<string>();

        public ContactValidator SetIgnoredNames(params string[] names)
        {
            _ignoredNames = names;
            return this;
        }

        protected override void ProccessRules()
        {
            if (_ignoredNames == null || !_ignoredNames.Contains(nameof(Contact.Name)))
                IsNotNullOrWhiteSpace(p => p.Name);

            if (_ignoredNames == null || !_ignoredNames.Contains(nameof(Contact.Surname)))
                IsNotNullOrWhiteSpace(p => p.Surname);


            if (_ignoredNames == null || !_ignoredNames.Contains(nameof(Contact.PhoneCode)))
                IsNotNullOrWhiteSpace(p => p.PhoneCode);

            if (_ignoredNames == null || !_ignoredNames.Contains(nameof(Contact.Phone)))
                IsNotNullOrWhiteSpace(p => p.Phone);

            if (_ignoredNames == null || !_ignoredNames.Contains(nameof(Contact.Email)))
                IsNotNullOrWhiteSpace(p => p.Email);
        }
    }
}
