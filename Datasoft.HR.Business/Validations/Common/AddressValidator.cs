﻿using Datasoft.HR.Core.Entities.Common;
using Datasoft.HR.ValidationInfrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Validations.Common
{
    public class AddressValidator : AbstractValidator<Address>
    {
        protected override void ProccessRules()
        {
            // Ülke boş geçilemez
            if (NotNull(p => p.Country))
                GreaterThan(p => p.Country.Id, 0, "Ülke seçiminde 'Id' değeri 0'dan büyük olmalı");

            // Şehir boş geçilemez
            if (NotNull(p => p.City))
                GreaterThan(p => p.City.Id, 0, "Şehir seçiminde 'Id' değeri 0'dan büyük olmalı");

            // İlçe boş geçilemez
            if (NotNull(p => p.County))
                GreaterThan(p => p.County.Id, 0, "İlçe seçiminde 'Id' değeri 0'dan büyük olmalı");

            // Bina no boş geçilemez
            IsNotNullOrWhiteSpace(p => p.BuildingNo, "Bina no boş geçilemez");

            // Sokak,Cadde veya Mahalle bilgilerinden en az biri dolu olmalı
            var atLeastOneRequiredProps = new List<Expression<Func<Address, object>>>
            {
                x=>x.Street,
                x=>x.Avenue,
                x=>x.Neighborhood
            };

            AtLeastOneRequired(atLeastOneRequiredProps, "Sokak,Cadde veya Mahalle bilgilerinden en az biri dolu olmalı");
        }
    }
}
