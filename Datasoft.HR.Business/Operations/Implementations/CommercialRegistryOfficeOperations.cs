﻿using Datasoft.HR.Business.Common;
using Datasoft.HR.Business.Operations.Interfaces;
using Datasoft.HR.Business.Validations;
using Datasoft.HR.Conversions;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Parameters;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.AdminApi.Clients;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.CommercialRegistryOffice;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Common;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Tax;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Operations.Implementations
{
    public class CommercialRegistryOfficeOperations : ICommercialRegistryOfficeOperations
    {
        private readonly CommercialRegistryOfficeApiHttpClient _commercialRegistryOfficeApiHttpClient;
        private readonly PagingInputValidator _pagingInputValidator;
        
        public CommercialRegistryOfficeOperations(
            CommercialRegistryOfficeApiHttpClient commercialRegistryOfficeApiHttpClient,
            PagingInputValidator pagingInputValidator)
        {
            _commercialRegistryOfficeApiHttpClient = commercialRegistryOfficeApiHttpClient;
            _pagingInputValidator = pagingInputValidator;        
        }
        
        public async Task<Result<List<CommercialRegistryOffice>>> SearchAsync(PagingInput<CommercialRegistryOfficeSearchDto> pagingInput)
        {
            _pagingInputValidator.Validate(pagingInput);            
            var request = pagingInput.Map<PreAccountancyPagingInput<PreAccountancyCommercialRegistryOfficeSearchRequest>>();    
            var result = await _commercialRegistryOfficeApiHttpClient.SearchAsync(request);
            var pagedList = result.Data.Map<List<CommercialRegistryOffice>>().ToPagedList(pagingInput.PagingOptions);            
            return Result<List<CommercialRegistryOffice>>.CreateSuccessResult(pagedList);
        }
    }
}
