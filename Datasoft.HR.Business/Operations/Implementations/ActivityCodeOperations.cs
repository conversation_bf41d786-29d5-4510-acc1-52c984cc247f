﻿using Datasoft.HR.Business.Common;
using Datasoft.HR.Business.Operations.Interfaces;
using Datasoft.HR.Business.Validations;
using Datasoft.HR.Conversions;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Parameters;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.AdminApi.Clients;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.ActivityCode;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.CommercialRegistryOffice;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Operations.Implementations
{
    public class ActivityCodeOperations : IActivityCodeOperations
    {
        private readonly ActivityCodeApiHttpClient _activityCodeApiHttpClient;
        private readonly PagingInputValidator _pagingInputValidator;        
        public ActivityCodeOperations(
            ActivityCodeApiHttpClient activityCodeApiHttpClient,
            PagingInputValidator pagingInputValidator)
        {
            _activityCodeApiHttpClient = activityCodeApiHttpClient;
            _pagingInputValidator = pagingInputValidator;

        }

        public async Task<Result<List<ActivityCode>>> SearchAsync(PagingInput<ActivityCodeSearchDto> pagingInput)
        {
            _pagingInputValidator.Validate(pagingInput);
            var request = pagingInput.Map<PreAccountancyPagingInput<PreAccountancyActivityCodeSearchRequest>>();            
            var result = await _activityCodeApiHttpClient.SearchAsync(request);
            var pagedList = result.Data.Map<List<ActivityCode>>().ToPagedList(pagingInput.PagingOptions);
            return Result<List<ActivityCode>>.CreateSuccessResult(pagedList);            
        }
    }
}
