﻿using AutoMapper.QueryableExtensions;
using Datasoft.HR.Business.Common;
using Datasoft.HR.Business.Operations.Interfaces;
using Datasoft.HR.Business.Validations.Branch;
using Datasoft.HR.Conversions;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Branch;
using Datasoft.HR.DTO.Branch;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.Repository.Repositories.Implementations;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

namespace Datasoft.HR.Business.Operations.Implementations
{
    public class BranchOperations : IBranchOperations
    {
        private readonly IBranchRepository _branchRepository;
        private readonly BranchValidator _branchValidator;

        public BranchOperations(IBranchRepository branchRepository,
            BranchValidator branchValidator)
        {
            _branchRepository = branchRepository;
            _branchValidator = branchValidator;
        }

        public async Task<Result<string>> UpsertAsync(BranchDto dto)
        {
            _branchValidator.Validate(dto);

            var entity = dto.Map<Branch>();
            await _branchRepository.UpsertAsync(entity);

            return Result<string>.CreateSuccessResult(entity.Id);
        }

        public async Task<Result> RemoveAsync(string id)
        {
            await _branchRepository.DeleteAsync(id);
            return Result.CreateSuccessResult();
        }

        public async Task<Result<BranchDto>> GetAsync(string id)
        {
            var data = await _branchRepository.GetById<BranchDto>(id).FirstOrDefaultAsync();
            return Result<BranchDto>.CreateSuccessResult(data);
        }

        public async Task<Result<List<BranchDto>>> SearchAsync(PagingInput<BranchQueryDto> pagingInput)
        {
            var query = _branchRepository.Search(pagingInput.Data);
            var pagedList = await query.ProjectTo<BranchDto>().ToPagedListAsync(pagingInput.PagingOptions);

            return Result<List<BranchDto>>.CreateSuccessResult(pagedList);
        }
    }
}
