﻿using Datasoft.HR.Business.Common;
using Datasoft.HR.Business.Operations.Interfaces.Parameters;
using Datasoft.HR.Business.Validations;
using Datasoft.HR.Conversions;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Company;
using Datasoft.HR.Core.Entities.Parameters;
using Datasoft.HR.Core.Entities.Parameters.Units;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Parameters;
using Datasoft.HR.Repository.Repositories.Interfaces;
using Datasoft.HR.Utility;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.AdminApi.Clients;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Common;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Currency;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Operations.Implementations.Parameters
{
    public class CurrencyOperations : ICurrencyOperations
    {
        private readonly CurrencyApiHttpClient _currencyApiHttpClient;
        private readonly PagingInputValidator _pagingInputValidator;
        private readonly IGeneralSettingRepository _generalSettingRepository;

        public CurrencyOperations(
            CurrencyApiHttpClient currencyApiHttpClient,
            PagingInputValidator pagingInputValidator,            
            IGeneralSettingRepository generalSettingRepositor)
        {
            _currencyApiHttpClient = currencyApiHttpClient;
            _pagingInputValidator = pagingInputValidator;            
            _generalSettingRepository = generalSettingRepositor;
        }

        public async Task<Result<List<Currency>>> SearchAsync(PagingInput<CurrencySearchRequestDto> pagingInput)
        {
            _pagingInputValidator.Validate(pagingInput);            

            var request = pagingInput.Map<PreAccountancyPagingInput<PreAccountancyCurrencySearchRequest>>();
            var result = await _currencyApiHttpClient.SearchAsync(request);           
            var pagedList = result.Data.Map<List<Currency>>().ToPagedList(pagingInput.PagingOptions);
            return Result<List<Currency>>.CreateSuccessResult(pagedList);
            
        }
        /*
        public async Task<Result<List<Currency>>> SearchFromGeneralSettingAsync(PagingInput<CurrencySearchRequestDto> pagingInput)
        {
            _pagingInputValidator.Validate(pagingInput);            

            if (!_generalSettingRepository.Any(Builders<GeneralSetting>.Filter.SizeGt(p => p.Currencies, 0)))
            {
                var abc = await SearchAsync(pagingInput);

                // Default olanın seçimi
                Currency defaultCurrency = abc.Data.FirstOrDefault(x => x.Code == "TRY");
                if (defaultCurrency != null)
                {
                    defaultCurrency.IsDefault = true;
                }

                return abc;
            }

            var currencies = _generalSettingRepository
                .GetAll()
                .SelectMany(p => p.Currencies);

            if (pagingInput?.Data?.IgnoredIds?.Any() ?? false)
                currencies = currencies.Where(x => !pagingInput.Data.IgnoredIds.Contains(x.Id));

            if (pagingInput?.Data?.Ids?.Any() ?? false)
                currencies = currencies.Where(x => pagingInput.Data.Ids.Contains(x.Id));

            if (pagingInput?.Data?.Code?.Any() ?? false)
            {
                var word = Transform.ToRegexContainsText(pagingInput.Data.Code);
                var regex = new Regex(word, RegexOptions.IgnoreCase);

                currencies = currencies.Where(x => x.Code != null && x.Code != string.Empty && regex.IsMatch(x.Code));
            }

            if (pagingInput?.Data?.Name?.Any() ?? false)
            {
                var word = Transform.ToRegexContainsText(pagingInput.Data.Name);
                var regex = new Regex(word, RegexOptions.IgnoreCase);

                currencies = currencies.Where(x => x.Name != null && x.Name != string.Empty && regex.IsMatch(x.Name));
            }            
            var pagedList = currencies.ToPagedList(pagingInput.PagingOptions);
            return Result<List<Currency>>.CreateSuccessResult(pagedList);            
        }
        */
    }
}
