﻿using Accounting.Business.Validations.Parameters;
using Datasoft.HR.Business.Common;
using Datasoft.HR.Business.Operations.Interfaces.Parameters;
using Datasoft.HR.Business.Validations;
using Datasoft.HR.Business.Validations.Parameters;
using Datasoft.HR.Conversions;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters;
using Datasoft.HR.Core.Entities.Parameters.Regions;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Parameters;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.AdminApi.Clients;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Common;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Region;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Operations.Implementations.Parameters
{
    public class RegionOperations : IRegionOperations
    {
        private readonly RegionApiHttpClient _regionApiHttpClient;
        private readonly PagingInputValidator _pagingInputValidator;
        private readonly CountrySearchValidator _countrySearchValidator;
        private readonly StateSearchValidator _stateSearchValidator;
        private readonly CitySearchValidator _citySearchValidator;
        private readonly CountySearchValidator _countySearchValidator;
        public RegionOperations(
            RegionApiHttpClient regionApiHttpClient,
            PagingInputValidator pagingInputValidator,
            CountrySearchValidator countrySearchValidator,
            StateSearchValidator stateSearchValidator,
            CitySearchValidator citySearchValidator,
            CountySearchValidator countySearchValidator
            )
        {
            _regionApiHttpClient = regionApiHttpClient;
            _pagingInputValidator = pagingInputValidator;
            _countrySearchValidator = countrySearchValidator;
            _stateSearchValidator = stateSearchValidator;
            _citySearchValidator = citySearchValidator;
            _countySearchValidator = countySearchValidator;
        }

        public async Task<Result<List<Country>>> SearchCountryAsync(PagingInput<RegionSearchDto> pagingInput)
        {
            _pagingInputValidator.Validate(pagingInput);
            _countrySearchValidator.Validate(pagingInput.Data);
            var request = pagingInput.Map<PreAccountancyPagingInput<PreAccountancyCountrySearchRequest>>();
            var result = await _regionApiHttpClient.SearchCountryAsync(request);
            var pagedList = result.Data.Map<List<Country>>().ToPagedList(pagingInput.PagingOptions);            
            return Result<List<Country>>.CreateSuccessResult(pagedList);
        }

        public async Task<Result<List<State>>> SearchStateAsync(PagingInput<RegionSearchDto> pagingInput)
        {
            _pagingInputValidator.Validate(pagingInput);
            _stateSearchValidator.Validate(pagingInput.Data);
            var request = pagingInput.Map<PreAccountancyPagingInput<PreAccountancyStateSearchRequest>>();
            var result = await _regionApiHttpClient.SearchStateAsync(request);
            var pagedList = result.Data.Map<List<State>>().ToPagedList(pagingInput.PagingOptions);
            return Result<List<State>>.CreateSuccessResult(pagedList);
        }

        public async Task<Result<List<City>>> SearchCityAsync(PagingInput<RegionSearchDto> pagingInput)
        {
            _pagingInputValidator.Validate(pagingInput);
            _citySearchValidator.Validate(pagingInput.Data);
            var request = pagingInput.Map<PreAccountancyPagingInput<PreAccountancyCitySearchRequest>>();
            var result = await _regionApiHttpClient.SearchCityAsync(request);
            var pagedList = result.Data.Map<List<City>>().ToPagedList(pagingInput.PagingOptions);
            return Result<List<City>>.CreateSuccessResult(pagedList);            
        }

        public async Task<Result<List<County>>> SearchCountyAsync(PagingInput<RegionSearchDto> pagingInput)
        {
            _pagingInputValidator.Validate(pagingInput);
            _countySearchValidator.Validate(pagingInput.Data);
            var request = pagingInput.Map<PreAccountancyPagingInput<PreAccountancyCountySearchRequest>>();
            var result = await _regionApiHttpClient.SearchCountyAsync(request);
            var pagedList = result.Data.Map<List<County>>().ToPagedList(pagingInput.PagingOptions);
            return Result<List<County>>.CreateSuccessResult(pagedList);

        }
    }
}
