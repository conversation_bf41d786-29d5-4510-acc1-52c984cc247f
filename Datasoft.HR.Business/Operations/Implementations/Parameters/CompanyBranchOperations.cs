﻿using Datasoft.HR.Business.Common;
using Datasoft.HR.Business.Operations.Interfaces.Parameters;
using Datasoft.HR.Business.Validations;
using Datasoft.HR.Conversions;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Company;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.ConfigurationApi.Clients;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Common;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Company;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Operations.Implementations.Parameters
{
    public class CompanyBranchOperations : ICompanyBranchOperations
    {
        private readonly CompanyBranchApiHttpClient _companyBranchApiHttpClient;
        private readonly PagingInputValidator _pagingInputValidator;

        public CompanyBranchOperations(CompanyBranchApiHttpClient companyBranchApiHttpClient, PagingInputValidator pagingInputValidator)
        {
            _companyBranchApiHttpClient = companyBranchApiHttpClient;
            _pagingInputValidator = pagingInputValidator;
        }

        public async Task<BranchInfoDto> GetBranchInformationAsync()
        {
            var result = await _companyBranchApiHttpClient.GetBranchInformationAsync();
            if (!result.Success)
            {

            }

            return result.Data.Map<BranchInfoDto>();
        }

        public async Task<Result<List<CompanyBranchSearchResponseDto>>> SearchCompanyBranchForCurrentUserAsync(PagingInput<BranchFilterDto> pagingInput)
        {
            _pagingInputValidator.Validate(pagingInput);
            var request = pagingInput.Map<PreAccountancyPagingInput<PreAccountancyBranchFilterDto>>();

            var result = await _companyBranchApiHttpClient.GetAllForCurrentUserAsync(request);
            var pagedList = result.Data.Map<List<CompanyBranchSearchResponseDto>>().ToPagedList(pagingInput.PagingOptions);
            return Result<List<CompanyBranchSearchResponseDto>>.CreateSuccessResult(pagedList);            
        }

        public async Task<bool> UpdateBranchNameAsync(string name)
        {
            var result = await _companyBranchApiHttpClient.UpdateBranchNameAsync(name);
            return result.Success;
        }

        public async Task<bool> UpdateBranchAsync(PreAccountancyCompanyBranchCreateDto request)
        {
            var result = await _companyBranchApiHttpClient.UpdateBranchAsync(request);
            return result.Success;
        }
    }
}
