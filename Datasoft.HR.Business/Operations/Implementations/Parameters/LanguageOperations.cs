﻿using Datasoft.HR.Business.Common;
using Datasoft.HR.Business.Operations.Interfaces.Parameters;
using Datasoft.HR.Business.Validations;
using Datasoft.HR.Conversions;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters.Regions;
using Datasoft.HR.Core.Entities.Parameters.Resources;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Parameters;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.AdminApi.Clients;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Common;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Language;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Operations.Implementations.Parameters
{
    public class LanguageOperations : ILanguageOperations
    {
        private readonly LanguageApiHttpClient _languageApiHttpClient;
        private readonly PagingInputValidator _pagingInputValidator;        
        public LanguageOperations(
            LanguageApiHttpClient languageApiHttpClient,
            PagingInputValidator pagingInputValidator)
        {
            _languageApiHttpClient = languageApiHttpClient;
            _pagingInputValidator = pagingInputValidator;            
        }

        public async Task<Result<List<Language>>> SearchAsync(PagingInput<LanguageSearchRequestDto> pagingInput)
        {
            _pagingInputValidator.Validate(pagingInput);
            var request = pagingInput.Map<PreAccountancyPagingInput<PreAccountancyLanguageSearchRequest>>();
            var result = await _languageApiHttpClient.SearchAsync(request);
            var pagedList = result.Data.Map<List<Language>>().ToPagedList(pagingInput.PagingOptions);
            return Result<List<Language>>.CreateSuccessResult(pagedList);            
        }
    }
}
