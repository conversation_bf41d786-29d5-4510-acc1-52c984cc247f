﻿using Datasoft.HR.Business.Operations.Interfaces;
using Datasoft.HR.Business.Operations.Interfaces.Company;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters.Regions;
using Datasoft.HR.Core.Entities.Parameters;
using Datasoft.HR.DTO.Company;
using Datasoft.HR.DTO.Parameters;
using DatasoftCommon.ApiClients.Accounting.Models.Enums;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Company;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Datasoft.HR.Business.Operations.Interfaces.Parameters;
using Datasoft.HR.Repository.Repositories.Interfaces;
using Datasoft.HR.Core.Entities.Company;
using Datasoft.HR.Conversions;
using Datasoft.HR.Business.Validations.Companies;
using Datasoft.HR.DTO.Common.Results;

namespace Datasoft.HR.Business.Operations.Implementations.Company
{
    public class BranchSettingOperations : IBranchSettingOperations
    {
        private readonly IClientSessionHandle _clientSessionHandle;
        private readonly IBranchSettingRepository _branchSettingRepository;
        private readonly IRegionOperations _regionOperations;
        private readonly ICompanyBranchOperations _companyBranchOperations;
        private readonly IActivityCodeOperations _activityCodeOperations;
        private readonly ICommercialRegistryOfficeOperations _commercialRegistryOfficeOperations;
        private readonly BranchSettingValidator _branchSettingValidator;
        
        private readonly IGeneralSettingRepository _generalSettingRepository;        

        public async Task UpsertAsync(BranchSettingDto dto)
        {
            try
            {
                _clientSessionHandle.StartTransaction();

                // set branch no
                var info = await _companyBranchOperations.GetBranchInformationAsync();
                dto.BranchNo = info?.BranchCode;

                // validation
                _branchSettingValidator.Validate(dto);

                // conversions
                var entity = await BindValuesFromIdsAsync(dto).MapAsync<BranchSettingDto, BranchSetting>();

                var oldValues = await _branchSettingRepository.FirstOrDefaultAsync(x => new BranchSetting { Id = x.Id, BranchEditableCode = x.BranchEditableCode, BranchName = x.BranchName });
                if (oldValues != null && (entity.BranchEditableCode != oldValues.BranchEditableCode || entity.BranchName != oldValues.BranchName))
                    await _companyBranchOperations.UpdateBranchAsync(new PreAccountancyCompanyBranchCreateDto { Name = entity.BranchName, EditableCode = entity.BranchEditableCode });

                // set id
                entity.Id = oldValues?.Id;
                if (string.IsNullOrWhiteSpace(entity.Id))
                    entity.Id = ObjectId.GenerateNewId().ToString();

                // operations
                await _branchSettingRepository.UpsertAsync(entity);

                await _clientSessionHandle.CommitTransactionAsync();
            }
            catch
            {
                await _clientSessionHandle.AbortTransactionAsync();
                throw;
            }
        }
        public async Task<BranchSettingDto> BindValuesFromIdsAsync(BranchSettingDto dto)
        {
            long languageId = 1;
            var defaultPagingInput = new PagingInput { EnablePaging = false };

            var activityCodeTask = dto.ActivityCode?.Id != null
                ? _activityCodeOperations.SearchAsync(new PagingInput<ActivityCodeSearchDto>
                {
                    PagingOptions = defaultPagingInput,
                    Data = new ActivityCodeSearchDto { Id = dto.ActivityCode.Id }
                })
                : Task.FromResult(new Result<List<ActivityCode>>());

            var commercialRegistryOfficeTask = dto.CommercialRegistryOffice?.Id != null
                ? _commercialRegistryOfficeOperations.SearchAsync(new PagingInput<CommercialRegistryOfficeSearchDto>
                {
                    PagingOptions = defaultPagingInput,
                    Data = new CommercialRegistryOfficeSearchDto { Id = dto.ActivityCode.Id }
                })
                : Task.FromResult(new Result<List<CommercialRegistryOffice>>());

            var addressCountriesTask = dto?.Address?.Country?.Id != null
                ? _regionOperations.SearchCountryAsync(new PagingInput<RegionSearchDto>
                {
                    PagingOptions = defaultPagingInput,
                    Data = new RegionSearchDto { LanguageId = languageId, Id = dto.Address.Country?.Id }
                })
                : Task.FromResult(new Result<List<Country>>());

            var addressStatesTask = dto?.Address?.State?.Id != null
                ? _regionOperations.SearchStateAsync(new PagingInput<RegionSearchDto>
                {
                    PagingOptions = defaultPagingInput,
                    Data = new RegionSearchDto { LanguageId = languageId, Id = dto.Address.State?.Id }
                })
                : Task.FromResult(new Result<List<State>>());

            var addressCityTask = dto?.Address?.City?.Id != null
                ? _regionOperations.SearchCityAsync(new PagingInput<RegionSearchDto>
                {
                    PagingOptions = defaultPagingInput,
                    Data = new RegionSearchDto { LanguageId = languageId, Id = dto.Address.City?.Id }
                })
                : Task.FromResult(new Result<List<City>>());

            var addressCountiesTask = dto?.Address?.County?.Id != null
                ? _regionOperations.SearchCountyAsync(new PagingInput<RegionSearchDto>
                {
                    PagingOptions = defaultPagingInput,
                    Data = new RegionSearchDto { LanguageId = languageId, Id = dto.Address.County?.Id }
                })
                : Task.FromResult(new Result<List<County>>());

            await Task.WhenAll(
                activityCodeTask,
                commercialRegistryOfficeTask,
                addressCountriesTask,
                addressStatesTask,
                addressCityTask,
                addressCountiesTask);

            if (activityCodeTask?.Result?.Data?.Any() ?? false)
                dto.ActivityCode = activityCodeTask.Result.Data.FirstOrDefault();

            if (commercialRegistryOfficeTask?.Result?.Data?.Any() ?? false)
                dto.CommercialRegistryOffice = commercialRegistryOfficeTask.Result.Data.FirstOrDefault();

            if (addressCountriesTask?.Result?.Data?.Any() ?? false)
                dto.Address.Country = addressCountriesTask.Result.Data.FirstOrDefault();

            if (addressStatesTask?.Result?.Data?.Any() ?? false)
                dto.Address.State = addressStatesTask.Result.Data.FirstOrDefault();

            if (addressCityTask?.Result?.Data?.Any() ?? false)
                dto.Address.City = addressCityTask.Result.Data.FirstOrDefault();

            if (addressCountiesTask?.Result?.Data?.Any() ?? false)
                dto.Address.County = addressCountiesTask.Result.Data.FirstOrDefault();

            return dto;
        }
        public async Task<BranchSettingDto> GetDefaultAsync()
        {
            var dto = await _branchSettingRepository.FirstOrDefaultAsync<BranchSettingDto>();
            var haveData = dto != null;

            if (dto == null)
            {
                dto = _generalSettingRepository.FirstOrDefault<BranchSettingDto>() ?? new BranchSettingDto();                
            }

            var info = await _companyBranchOperations.GetBranchInformationAsync();
            if (info != null)
            {
                if (!haveData)
                {
                    // şube no
                    dto.BranchNo = info.BranchCode;
                    // şube kodu
                    dto.BranchEditableCode = info.BranchEditableCode;
                    // şube adı
                    dto.BranchName = info.BranchName;
                }

                dto.CompanyName = info.CompanyName;
                dto.TotalCount = info.TotalBranchCount;
                dto.BranchNo = dto.BranchNo.PadLeft(4, '0');
            }
            return dto;
        }

    }
}
