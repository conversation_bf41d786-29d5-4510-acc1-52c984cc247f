﻿using Datasoft.HR.Business.Operations.Implementations.Parameters;
using Datasoft.HR.Business.Operations.Interfaces;
using Datasoft.HR.Business.Operations.Interfaces.Parameters;
using Datasoft.HR.Business.Validations.Companies;
using Datasoft.HR.Conversions;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Company;
using Datasoft.HR.Core.Entities.Parameters;
using Datasoft.HR.Core.Entities.Parameters.Regions;
using Datasoft.HR.Core.Entities.Parameters.Resources;
using Datasoft.HR.Core.Entities.Parameters.Units;
using Datasoft.HR.Core.Enums;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Company;
using Datasoft.HR.DTO.Parameters;
using Datasoft.HR.DTO.Parameters.Units;
using Datasoft.HR.Repository.Repositories.Interfaces;
using DatasoftCommon.ApiClients.Accounting.Models.Enums;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.AdminApi.Clients;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.ConfigurationApi.Clients;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Common;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Company;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Region;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.SalesApi.Clients;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Operations.Implementations
{
    public class GeneralSettingOperations : IGeneralSettingOperations
    {
        private readonly IClientSessionHandle _clientSessionHandle;
        private readonly IGeneralSettingRepository _generalSettingRepository;
        private readonly ICurrencyOperations _currencyOperations;
        private readonly IRegionOperations _regionOperations;
        private readonly ILanguageOperations _languageOperations;
        private readonly IActivityCodeOperations _activityCodeOperations;
        private readonly ICommercialRegistryOfficeOperations _commercialRegistryOfficeOperations;
        private readonly GeneralSettingValidator _generalSettingValidator;
        private readonly RegionApiHttpClient _regionApiHttpClient;
        private readonly CompanyPeriodApiHttpClient _companyPeriodApiHttpClient;
        private readonly CompanyBranchApiHttpClient _companyBranchApiHttpClient;
        private readonly CompanyApiHttpClient _companyApiHttpClient;
        private readonly CommonGeneralSettingHttpClient _commonGeneralSettingHttpClient;
        private readonly TaxOfficeApiHttpClient _taxOfficeApiHttpClient;
        private readonly LoginUser _loginUser;
        public GeneralSettingOperations(IClientSessionHandle clientSessionHandle,
            IGeneralSettingRepository generalSettingRepository,
            ICurrencyOperations currencyOperations,
            IRegionOperations regionOperations,
            ILanguageOperations languageOperations,
            IActivityCodeOperations activityCodeOperations,
            ICommercialRegistryOfficeOperations commercialRegistryOfficeOperations,
            GeneralSettingValidator generalSettingValidator,
            RegionApiHttpClient regionApiHttpClient,
            CompanyPeriodApiHttpClient companyPeriodApiHttpClient,
            CompanyBranchApiHttpClient companyBranchApiHttpClient,
            CompanyApiHttpClient companyApiHttpClient,
            CommonGeneralSettingHttpClient commonGeneralSettingHttpClient,
            TaxOfficeApiHttpClient taxOfficeApiHttpClient,
            LoginUser loginUser
            )
        {
            _generalSettingRepository = generalSettingRepository;
            _currencyOperations = currencyOperations;
            _languageOperations = languageOperations;
            _activityCodeOperations = activityCodeOperations;
            _commercialRegistryOfficeOperations = commercialRegistryOfficeOperations;
            _regionApiHttpClient = regionApiHttpClient;
            _companyPeriodApiHttpClient = companyPeriodApiHttpClient;
            _companyBranchApiHttpClient = companyBranchApiHttpClient;
            _companyApiHttpClient = companyApiHttpClient;
            _taxOfficeApiHttpClient = taxOfficeApiHttpClient; 
            _loginUser = loginUser;
            _commonGeneralSettingHttpClient = commonGeneralSettingHttpClient;
            _regionOperations = regionOperations;
            _clientSessionHandle = clientSessionHandle;
            _generalSettingValidator = generalSettingValidator;

        }
        public async Task<GeneralSettingDto> GetDefaultAsync()
        {            
            var generalSetting = await _generalSettingRepository.FirstOrDefaultAsync<GeneralSettingDto>();
            var defaultPagingInput = new PagingInput { EnablePaging = false };

            var periodTask = _companyPeriodApiHttpClient.GetCurrentAsync();
            var branchInfoTask = _companyBranchApiHttpClient.GetBranchInformationAsync();
            var companyTask = _companyApiHttpClient.GetByIdAsync(_loginUser.CompanyId);
            


            await Task.WhenAll(periodTask, branchInfoTask, companyTask);

                        
            var period = periodTask?.Result?.Data?.Map<PeriodInfoDto>();

            if (generalSetting == null)
            {
                long languageId = 1;
                var regionSearchDto = new RegionSearchDto { LanguageId = languageId, SearchText = "Türkiye" };
                generalSetting = new GeneralSettingDto();

                var commonGeneralSettingRequest = await _commonGeneralSettingHttpClient.GetSettingsForAccountingOrEDefter(_loginUser.CompanyId);

                if (commonGeneralSettingRequest.IsSuccess && commonGeneralSettingRequest.Data != null)
                {
                    generalSetting = commonGeneralSettingRequest.Data.Map<GeneralSettingDto>();

                    var countryResponse = await _regionApiHttpClient
                        .SearchCountryAsync(new PreAccountancyPagingInput<PreAccountancyCountrySearchRequest>
                        {
                            Data = new PreAccountancyCountrySearchRequest
                            {
                                Id = generalSetting.Address.Country.Id
                            }
                        });

                    var stateResponse = await _regionApiHttpClient
                         .SearchStateAsync(new PreAccountancyPagingInput<PreAccountancyStateSearchRequest>
                         {
                             Data = new PreAccountancyStateSearchRequest
                             {
                                 Id = generalSetting.Address.State.Id
                             }
                         });

                    var cityResponse = await _regionApiHttpClient
                        .SearchCityAsync(new PreAccountancyPagingInput<PreAccountancyCitySearchRequest>
                        {
                            Data = new PreAccountancyCitySearchRequest
                            {
                                Id = generalSetting.Address.City.Id
                            }
                        });

                    var countyResponse = await _regionApiHttpClient
                        .SearchCountyAsync(new PreAccountancyPagingInput<PreAccountancyCountySearchRequest>
                        {
                            Data = new PreAccountancyCountySearchRequest
                            {
                                Id = generalSetting.Address.County.Id
                            }
                        });

                    var taxOfficeResponse = generalSetting.TaxOffice != null && generalSetting.TaxOffice.Id.HasValue ? await _taxOfficeApiHttpClient.GetByIdAsync(generalSetting.TaxOffice.Id.Value) : null;

                    generalSetting.TaxOffice = taxOfficeResponse != null && taxOfficeResponse.Success ? taxOfficeResponse.Data.Map<TaxOffice>() : null;
                    generalSetting.Address.Country = countryResponse.Success ? countryResponse.Data.FirstOrDefault().Map<Country>() : null;
                    generalSetting.Address.State = stateResponse.Success ? stateResponse.Data.FirstOrDefault().Map<State>() : null;
                    generalSetting.Address.City = cityResponse.Success ? cityResponse.Data.FirstOrDefault().Map<City>() : null;
                    generalSetting.Address.County = countyResponse.Success ? countyResponse.Data.FirstOrDefault().Map<County>() : null;
                    languageId = generalSetting.Language.Id;
                    regionSearchDto.SearchText = "";
                    regionSearchDto.Id = generalSetting.TaxLegislationCountry.Id;
                }

                var countryTask = _regionOperations.SearchCountryAsync(new PagingInput<RegionSearchDto>
                {
                    PagingOptions = defaultPagingInput,
                    Data = regionSearchDto
                });

                var languageTask = _languageOperations.SearchAsync(new PagingInput<LanguageSearchRequestDto>
                {
                    PagingOptions = defaultPagingInput,
                    Data = new LanguageSearchRequestDto { LanguageId = languageId, IsDefault = true }
                });

                await Task.WhenAll(countryTask, languageTask);

                generalSetting.CompanyName = _loginUser?.CompanyName;
                generalSetting.TaxLegislationCountry = countryTask.Result?.Data?.FirstOrDefault();
                generalSetting.Language = languageTask.Result?.Data?.FirstOrDefault();

            }



            generalSetting.PeriodInformation = period;
            //generalSetting.HaveBranch = (branchInfoTask?.Result?.Data?.TotalBranchCount ?? 0) > 0;
            //generalSetting.BranchCount = (branchInfoTask?.Result?.Data?.TotalBranchCount ?? 0);
            generalSetting.CompanyCode = companyTask?.Result?.Data?.Code;
            //generalSetting.Currencies = generalSetting.Currencies.OrderByDescending(x => x.IsDefault).ThenBy(x => x.Id).ToList();
            return generalSetting;
        }
        public async Task UpsertAsync(GeneralSettingDto dto, bool updateCommonGeneralSettings = true)
        {
            try
            {
                _clientSessionHandle.StartTransaction();

                // validation
                _generalSettingValidator.Validate(dto);

                // conversions
                var entity = await BindValuesFromIdsAsync(dto).MapAsync<GeneralSettingDto, GeneralSetting>();

                // Update Company Name
                var oldValues = await _generalSettingRepository
                    .FirstOrDefaultAsync(x => new GeneralSetting
                    {
                        Id = x.Id,
                        CompanyName = x.CompanyName,
                        IdentityNumber = x.IdentityNumber,
                        TaxNumber = x.TaxNumber,
                        Address = x.Address,
                        CompanyType = x.CompanyType,
                        Language = x.Language,
                        LegalStructure = x.LegalStructure,
                        MernisNumber = x.MernisNumber,
                        Name = x.Name,
                        PartnerType = x.PartnerType,
                        PersonalTitle = x.PersonalTitle,
                        Surname = x.Surname,
                        TaxLegislationCountry = x.TaxLegislationCountry,
                        TaxOffice = x.TaxOffice,
                        Title = x.Title
                    });

                var haveAnyChange = oldValues == null || entity?.CompanyName != oldValues?.CompanyName;
                /*
                if (haveAnyChange)
                {
                    var request = entity.Map<PreAccountancyCompanyNameAndEstablishmentDto>();

                    if (request.Date.HasValue)
                        request.Date = request.Date.Value.ToUniversalTime();

                    if (request.LeaveDate.HasValue)
                        request.LeaveDate = request.LeaveDate.Value.ToUniversalTime();

                    await _companyOperations.UpdateCompanyAsync(request);
                }

                haveAnyChange = oldValues == null || entity?.IdentityNumber != oldValues?.IdentityNumber
                  || entity?.TaxNumber != oldValues?.TaxNumber;

                if (haveAnyChange)
                {
                    var identifier = (entity.LegalStructure == Core.Enums.LegalStructure.Legal || entity.LegalStructure == Core.Enums.LegalStructure.Partnership) ? entity.TaxNumber : (entity.LegalStructure == Core.Enums.LegalStructure.Real) ? entity.IdentityNumber : null;
                    await _companyOperations.UpdateCompanyIdentifierAsync(identifier);
                }
                */


                // set id
                entity.Id = oldValues?.Id;
                if (string.IsNullOrWhiteSpace(entity.Id))
                    entity.Id = ObjectId.GenerateNewId().ToString();

                // operation
                await _generalSettingRepository.UpsertAsync(entity);

                await _clientSessionHandle.CommitTransactionAsync();
            }
            catch (Exception ex)
            {
                await _clientSessionHandle.AbortTransactionAsync();
                throw;


            }

        }
        private async Task<GeneralSettingDto> BindValuesFromIdsAsync(GeneralSettingDto dto)
        {
            long languageId = 1;
            var defaultPagingInput = new PagingInput { EnablePaging = false };

            var countriesTask = dto.TaxLegislationCountry?.Id != null
                ? _regionOperations.SearchCountryAsync(new PagingInput<RegionSearchDto>
                {
                    PagingOptions = defaultPagingInput,
                    Data = new RegionSearchDto { LanguageId = languageId, Id = dto.TaxLegislationCountry.Id }
                })
                : Task.FromResult(new Result<List<Country>>());

            var languagesTask = dto.Language?.Id != null
                ? _languageOperations.SearchAsync(new PagingInput<LanguageSearchRequestDto>
                {
                    PagingOptions = defaultPagingInput,
                    Data = new LanguageSearchRequestDto { LanguageId = languageId, IsDefault = true }
                })
                : Task.FromResult(new Result<List<Language>>());

            var addressCountriesTask = dto?.Address?.Country?.Id != null
                ? _regionOperations.SearchCountryAsync(new PagingInput<RegionSearchDto>
                {
                    PagingOptions = defaultPagingInput,
                    Data = new RegionSearchDto { LanguageId = languageId, Id = dto.Address.Country?.Id }
                })
                : Task.FromResult(new Result<List<Country>>());

            var addressStatesTask = dto?.Address?.State?.Id != null
                ? _regionOperations.SearchStateAsync(new PagingInput<RegionSearchDto>
                {
                    PagingOptions = defaultPagingInput,
                    Data = new RegionSearchDto { LanguageId = languageId, Id = dto.Address.State?.Id }
                })
                : Task.FromResult(new Result<List<State>>());

            var addressCityTask = dto?.Address?.City?.Id != null
                ? _regionOperations.SearchCityAsync(new PagingInput<RegionSearchDto>
                {
                    PagingOptions = defaultPagingInput,
                    Data = new RegionSearchDto { LanguageId = languageId, Id = dto.Address.City?.Id }
                })
                : Task.FromResult(new Result<List<City>>());

            var addressCountiesTask = dto?.Address?.County?.Id != null
                ? _regionOperations.SearchCountyAsync(new PagingInput<RegionSearchDto>
                {
                    PagingOptions = defaultPagingInput,
                    Data = new RegionSearchDto { LanguageId = languageId, Id = dto.Address.County?.Id }
                })
                : Task.FromResult(new Result<List<County>>());

            await Task.WhenAll(
                   countriesTask,                                      
                   languagesTask,                                     
                   addressCountriesTask,
                   addressStatesTask,
                   addressCityTask,
                   addressCountiesTask);

            if (countriesTask?.Result?.Data?.Any() ?? false)
                dto.TaxLegislationCountry = countriesTask.Result.Data.FirstOrDefault();


            if (languagesTask?.Result?.Data?.Any() ?? false)
                dto.Language = languagesTask.Result.Data.FirstOrDefault();


            if (addressCountriesTask?.Result?.Data?.Any() ?? false)
                dto.Address.Country = addressCountriesTask.Result.Data.FirstOrDefault();

            if (addressStatesTask?.Result?.Data?.Any() ?? false)
                dto.Address.State = addressStatesTask.Result.Data.FirstOrDefault();

            if (addressCityTask?.Result?.Data?.Any() ?? false)
                dto.Address.City = addressCityTask.Result.Data.FirstOrDefault();

            if (addressCountiesTask?.Result?.Data?.Any() ?? false)
                dto.Address.County = addressCountiesTask.Result.Data.FirstOrDefault();

            return dto;
        }

    }
}
