﻿using AutoMapper.QueryableExtensions;
using Datasoft.HR.Business.Common;
using Datasoft.HR.Business.Operations.Interfaces;
using Datasoft.HR.Business.Validations.Examples;
using Datasoft.HR.Conversions;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Examples;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Examples;
using Datasoft.HR.Repository.Repositories.Interfaces;
using Datasoft.HR.Utility;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using System.Linq.Expressions;
using System.Text.RegularExpressions;

namespace Datasoft.HR.Business.Operations.Implementations
{
    public class ExampleOperations : IExampleOperations
    {
        private readonly IExampleRepository _exampleRepository;
        private readonly ExampleValidator _exampleValidator;
        public ExampleOperations(IExampleRepository exampleRepository, ExampleValidator exampleValidator)
        {
            _exampleRepository = exampleRepository;
            _exampleValidator = exampleValidator;
        }
        public async Task<Result<string>> UpsertAsync(ExampleDto dto)
        {
            _exampleValidator.Validate(dto);

            var entity = dto.Map<ExampleEntity>();
            await _exampleRepository.UpsertAsync(entity);

            return Result<string>.CreateSuccessResult(entity.Id);
        }

        public async Task<Result> RemoveAsync(string id)
        {
            await _exampleRepository.DeleteAsync(id);
            return Result.CreateSuccessResult();
        }

        public async Task<Result> GetAsync(string id)
        {
            var data = await _exampleRepository.GetById<ExampleDto>(id).FirstOrDefaultAsync();
            return Result<ExampleDto>.CreateSuccessResult(data);
        }

        public async Task<Result<List<ExampleDto>>> SearchAsync(PagingInput<string> pagingInput)
        {
            var query = _exampleRepository.GetAll();
            if (!string.IsNullOrWhiteSpace(pagingInput.Data))
            {
                var word = Transform.ToRegexContainsText(pagingInput.Data.Trim());
                var regex = new Regex(word, RegexOptions.IgnoreCase);
                Expression<Func<ExampleEntity, bool>> filter = x => regex.IsMatch(x.Name) || x.Code.StartsWith(word) || regex.IsMatch(x.Code);

                query = query.Where(filter);
            }

            var pagedList = await query.ProjectTo<ExampleDto>().ToPagedListAsync(pagingInput.PagingOptions);

            return Result<List<ExampleDto>>.CreateSuccessResult(pagedList);
        }
    }
}
