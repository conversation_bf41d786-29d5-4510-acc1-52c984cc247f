﻿using Datasoft.HR.Business.Common;
using Datasoft.HR.Business.Operations.Interfaces;
using Datasoft.HR.Business.Validations;
using Datasoft.HR.Conversions;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Examples;
using Datasoft.HR.DTO.Parameters;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.AdminApi.Clients;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Common;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Tax;
using SharpCompress.Common;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Operations.Implementations
{
    public class TaxOfficeOperations : ITaxOfficeOperations
    {
        private readonly TaxOfficeApiHttpClient _taxOfficeApiHttpClient;
        private readonly PagingInputValidator _pagingInputValidator;
        public TaxOfficeOperations(
            TaxOfficeApiHttpClient taxOfficeApiHttpClient,
            PagingInputValidator pagingInputValidator)
        {
            _taxOfficeApiHttpClient = taxOfficeApiHttpClient;
            _pagingInputValidator = pagingInputValidator;            
        }
        public async Task<Result<List<TaxOffice>>> SearchAsync(PagingInput<TaxOfficeSearchDto> pagingInput)
        {
            _pagingInputValidator.Validate(pagingInput);
            var request = pagingInput.Map<PreAccountancyPagingInput<PreAccountancyTaxOfficeSearchRequest>>();
            var result = await _taxOfficeApiHttpClient.SearchAsync(request);
            var pagedList = result.Data.Map<List<TaxOffice>>().ToPagedList(pagingInput.PagingOptions);
            return Result<List<TaxOffice>>.CreateSuccessResult(pagedList);
        }
    }
}
