﻿using Datasoft.HR.Core.Common;
using Datasoft.HR.DTO.Branch;
using Datasoft.HR.DTO.Common.Results;

namespace Datasoft.HR.Business.Operations.Interfaces
{
    public interface IBranchOperations
    {
        Task<Result<string>> UpsertAsync(BranchDto dto);
        Task<Result> RemoveAsync(string id);
        Task<Result<BranchDto>> GetAsync(string id);
        Task<Result<List<BranchDto>>> SearchAsync(PagingInput<BranchQueryDto> pagingInput);
    }
}
