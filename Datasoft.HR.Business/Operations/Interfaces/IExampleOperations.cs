﻿using Datasoft.HR.Core.Common;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Examples;

namespace Datasoft.HR.Business.Operations.Interfaces
{
    public interface IExampleOperations
    {
        Task<Result<string>> UpsertAsync(ExampleDto dto);
        Task<Result> RemoveAsync(string id);
        Task<Result> GetAsync(string id);
        Task<Result<List<ExampleDto>>> SearchAsync(PagingInput<string> pagingInput);
    }
}
