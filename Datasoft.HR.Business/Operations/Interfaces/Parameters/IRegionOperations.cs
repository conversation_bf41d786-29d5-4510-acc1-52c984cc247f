﻿using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters.Regions;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Parameters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Operations.Interfaces.Parameters
{
    public interface IRegionOperations
    {
        Task<Result<List<Country>>> SearchCountryAsync(PagingInput<RegionSearchDto> pagingInput);
        Task<Result<List<State>>> SearchStateAsync(PagingInput<RegionSearchDto> pagingInput);
        Task<Result<List<City>>> SearchCityAsync(PagingInput<RegionSearchDto> pagingInput);
        Task<Result<List<County>>> SearchCountyAsync(PagingInput<RegionSearchDto> pagingInput);
    }
}
