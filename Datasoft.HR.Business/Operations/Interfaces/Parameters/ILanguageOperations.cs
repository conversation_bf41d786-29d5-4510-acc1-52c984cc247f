﻿using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters.Resources;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Parameters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Operations.Interfaces.Parameters
{
    public interface ILanguageOperations
    {
        Task<Result<List<Language>>> SearchAsync(PagingInput<LanguageSearchRequestDto> pagingInput);
    }
}
