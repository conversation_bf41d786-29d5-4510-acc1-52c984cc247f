﻿using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters.Units;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Parameters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Operations.Interfaces.Parameters
{
    public interface ICurrencyOperations
    {
        Task<Result<List<Currency>>> SearchAsync(PagingInput<CurrencySearchRequestDto> pagingInput);

       // Task<Result<List<Currency>>> SearchFromGeneralSettingAsync(PagingInput<CurrencySearchRequestDto> pagingInput);
    }
}
