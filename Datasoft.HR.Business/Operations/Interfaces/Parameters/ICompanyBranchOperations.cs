﻿using Datasoft.HR.Core.Common;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Company;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Company;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Operations.Interfaces.Parameters
{
    public interface ICompanyBranchOperations
    {
        Task<BranchInfoDto> GetBranchInformationAsync();

        Task<Result<List<CompanyBranchSearchResponseDto>>> SearchCompanyBranchForCurrentUserAsync(PagingInput<BranchFilterDto> pagingInput);

        Task<bool> UpdateBranchNameAsync(string name);

        Task<bool> UpdateBranchAsync(PreAccountancyCompanyBranchCreateDto request);
    }
}
