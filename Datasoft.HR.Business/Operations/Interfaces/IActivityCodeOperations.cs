﻿using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Parameters;
using Datasoft.HR.DTO.Common.Results;
using Datasoft.HR.DTO.Parameters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Business.Operations.Interfaces
{
    public interface IActivityCodeOperations
    {
        Task<Result<List<ActivityCode>>> SearchAsync(PagingInput<ActivityCodeSearchDto> pagingInput);
    }
    
}
