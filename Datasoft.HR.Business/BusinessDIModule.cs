﻿using Datasoft.HR.Business.Operations.Implementations;
using Datasoft.HR.Business.Operations.Interfaces;
using Datasoft.HR.Business.Validations;
using Datasoft.HR.Business.Validations.Examples;
using Datasoft.HR.Conversions;
using Datasoft.HR.Repository;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using DatasoftCommon.ApiClients.Accounting.Extensions;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Extensions;
using DatasoftCommon.ApiClients.eBeyanname;
using DatasoftCommon.ApiClients.Turmob.Extensions;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Business.Operations.Interfaces.Parameters;
using Datasoft.HR.Business.Operations.Implementations.Parameters;
using Datasoft.HR.Business.Validations.Common;
using Datasoft.HR.Business.Validations.Companies;
using Accounting.Business.Validations.Parameters;
using Datasoft.HR.Business.Validations.Parameters;
using Datasoft.HR.Business.Validations.Branch;

namespace Datasoft.HR.Business
{
    public static class BusinessDIModule
    {
        public static void Inject(IServiceCollection services, IConfiguration configuration, IHostEnvironment environment)
        {
            // Inject sub layers
            ConversionsDIModule.Inject(services, configuration);
            RepositoryDIModule.Inject(services, configuration);

            services.AddAccountingApiClients(config =>
            {
                config.BaseAddress = configuration["DatasoftERP:TenantAPIBaseURL"];
                config.SalesBaseAddress = configuration["DatasoftERP:SalesAPIBaseURL"];
                config.LoginApiOptions.Username = configuration["DatasoftERP:TenantAPIUserName"];
                config.LoginApiOptions.Password = configuration["DatasoftERP:TenantAPIPassword"];
                config.LoginApiOptions.Insistent = true;
            }, config =>
            {
                config.BaseAddress = configuration["AccountingOptions:BaseAddress"];
            });

            services.AddDatasoftPreAccountancyAdminApiClients(config =>
            {
                config.BaseAddress = configuration["DatasoftERP:AdminAPIBaseURL"];
                config.LoginApiOptions.Username = configuration["DatasoftERP:AdminAPIUserName"];
                config.LoginApiOptions.Password = configuration["DatasoftERP:AdminAPIPassword"];
                config.LoginApiOptions.Insistent = true;
            });

            services.AddDatasoftPreAccountancyTenantApiClients(config =>
            {
                config.BaseAddress = configuration["DatasoftERP:TenantAPIBaseURL"];
                config.SalesBaseAddress = configuration["DatasoftERP:SalesAPIBaseURL"];
                config.LoginApiOptions.Username = configuration["DatasoftERP:TenantAPIUserName"];
                config.LoginApiOptions.Password = configuration["DatasoftERP:TenantAPIPassword"];
                config.LoginApiOptions.Insistent = true;
            });

            services.AddDatasoftPreAccountancyTenantWithTokenApiClients(configuration["DatasoftERP:TenantAPIBaseURL"]);
            services.AddDatasoftSalesWithTokenApiClients(configuration["DatasoftERP:SalesAPIBaseURL"]);
            // Inject Operations
            // TODO: Auto injection
            services.AddTransient<IExampleOperations, ExampleOperations>();
            services.AddTransient<ITaxOfficeOperations, TaxOfficeOperations>();
            services.AddTransient<ICommercialRegistryOfficeOperations, CommercialRegistryOfficeOperations>();
            services.AddTransient<IActivityCodeOperations, ActivityCodeOperations>();
            services.AddTransient<IGeneralSettingOperations, GeneralSettingOperations>();
            services.AddTransient<ICurrencyOperations, CurrencyOperations>();
            services.AddTransient<ILanguageOperations, LanguageOperations>();
            services.AddTransient<IRegionOperations, RegionOperations>();
            services.AddTransient<IBranchOperations, BranchOperations>();



            // Inject Validators
            // TODO: Auto injection
            services.AddTransient<ExampleValidator>();
            services.AddTransient<PagingInputValidator>();
            services.AddTransient<AddressValidator>();
            services.AddTransient<ContactValidator>();
            services.AddTransient<GeneralSettingValidator>();
            services.AddTransient<SMMMInformationValidatior>();
            services.AddTransient<YMMInformationValidatior>();
            services.AddTransient<SgkInformationValidator>();
            services.AddTransient<CountrySearchValidator>();
            services.AddTransient<StateSearchValidator>();
            services.AddTransient<CitySearchValidator>();
            services.AddTransient<CountySearchValidator>();
            services.AddTransient<BranchValidator>();
            services.AddTransient<BranchGeneralInformationValidator>();
            services.AddTransient<BranchQueryDtoValidator>();


        }
    }
}
