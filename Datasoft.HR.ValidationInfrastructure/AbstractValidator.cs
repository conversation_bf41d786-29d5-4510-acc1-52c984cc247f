﻿using System.Dynamic;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using Datasoft.HR.ValidationInfrastructure.ErrorCodes;

namespace Datasoft.HR.ValidationInfrastructure
{

    public interface IValidator<T>
    {
        ValidationResult Validate(T instanceToValidate);

        ValidationResult Validate(T instanceToValidate, Dictionary<string, object> validationContext);

        ValidationResult Validate(T instanceToValidate, Dictionary<string, object> validationContext, params string[] includingRuleSets);

        ValidationResult Validate(T instanceToValidate, params string[] includingRuleSets);


        IValidator<T> CombineSetPrefix(params string[] names);
        IValidator<T> SetThrowOnException();
        IValidator<T> SetThrowOffException();
    }

    public abstract class AbstractValidator<T> : IValidator<T>
    {
        public T? _instanceToValidate;
        protected Dictionary<string, object> _validationContext = new Dictionary<string, object>();
        protected readonly ValidationResult _validationResult = new ValidationResult();
        protected List<string> _includingRuleSets = new List<string>();
        public string Prefix { get; protected set; } = string.Empty;
        public bool ThrowOnException { get; protected set; } = true;
        protected bool _isValid => _validationResult.IsValid;

        protected string GetPropertyName<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector)
        {
            return string.Join(".", new[] { Prefix, propertyNameSelector.GetExpressionPropertyChain() }.Where(s => !string.IsNullOrWhiteSpace(s)));
        }

        private TProperty GetPropertyValue<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector)
        {
            return propertyNameSelector.Compile().Invoke(_instanceToValidate);
        }

        public void AddError(Action<ValidationFailure> failureOption)
        {
            var failure = new ValidationFailure();
            failureOption.Invoke(failure);

            _validationResult.Errors.Add(failure);
        }

        public void AddError<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, Action<ValidationFailure> failureOption)
        {
            var failure = new ValidationFailure();
            failureOption.Invoke(failure);

            failure.PropertyName = GetPropertyName(propertyNameSelector);
            failure.AttemptedValue = GetPropertyValue(propertyNameSelector);

            _validationResult.Errors.Add(failure);
        }

        public void AddWarning<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, Action<ValidationFailure> failureOption)
        {
            var failure = new ValidationFailure();
            failureOption.Invoke(failure);
            failure.Severity = ValidationSeverity.Warning;

            failure.PropertyName = GetPropertyName(propertyNameSelector);
            failure.AttemptedValue = GetPropertyValue(propertyNameSelector);

            _validationResult.Errors.Add(failure);
        }

        public void AddWarning(Action<ValidationFailure> failureOption)
        {
            var failure = new ValidationFailure();
            failureOption.Invoke(failure);
            failure.Severity = ValidationSeverity.Warning;

            _validationResult.Errors.Add(failure);
        }

        public void AddErrorToMultipleProperty<TProperty>(IEnumerable<Expression<Func<T, TProperty>>> propertyNameSelectors, TProperty expectedValue, Action<ValidationFailure> failureOption)
        {
            var failure = new ValidationFailure();
            failureOption.Invoke(failure);

            dynamic customState = new ExpandoObject();

            if (expectedValue != null)
                customState.ExpectedValue = expectedValue;

            if (propertyNameSelectors != null)
            {
                customState.PropertyNames = propertyNameSelectors.Select(selector => string.Join(".", new[] { Prefix, selector.GetExpressionPropertyChain() }.Where(s => !string.IsNullOrWhiteSpace(s))));
                customState.AttemptedValues = propertyNameSelectors.Select(selector =>
                {
                    var key = string.Join(".", new[] { Prefix, selector.GetExpressionPropertyChain() }.Where(s => !string.IsNullOrWhiteSpace(s)));
                    var value = selector.Compile().Invoke(_instanceToValidate);
                    return new KeyValuePair<string, object?>(key, value);
                }).ToDictionary(x => x.Key, x => x.Value);
            }

            failure.CustomState = customState;

            _validationResult.Errors.Add(failure);
        }

        public void AddErrorToMultipleProperty<TProperty>(IEnumerable<Expression<Func<T, TProperty>>> propertyNameSelectors, Action<ValidationFailure> failureOption)
        {
            var failure = new ValidationFailure();
            failureOption.Invoke(failure);

            dynamic customState = new ExpandoObject();

            if (propertyNameSelectors != null)
            {
                customState.PropertyNames = propertyNameSelectors.Select(selector => string.Join(".", new[] { Prefix, selector.GetExpressionPropertyChain() }.Where(s => !string.IsNullOrWhiteSpace(s))));
                customState.AttemptedValues = propertyNameSelectors.Select(selector =>
                {
                    var key = string.Join(".", new[] { Prefix, selector.GetExpressionPropertyChain() }.Where(s => !string.IsNullOrWhiteSpace(s)));
                    var value = selector.Compile().Invoke(_instanceToValidate);
                    return new KeyValuePair<string, object?>(key, value);
                }).ToDictionary(x => x.Key, x => x.Value);
            }

            failure.CustomState = customState;

            _validationResult.Errors.Add(failure);
        }

        public void ImportValidationResult(ValidationResult validationResult)
        {
            _validationResult.Errors.AddRange(validationResult.Errors);
        }

        public void AddError<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, string errorCode) => AddError(propertyNameSelector, (opt) => opt.ErrorCode = errorCode);

        public ValidationResult Validate(T instanceToValidate)
        {
            _instanceToValidate = instanceToValidate;
            _validationResult.ClearErrors();
            ProccessRules();

            if (ThrowOnException && !_isValid)
                throw new ValidationException(_validationResult.Errors);

            return _validationResult;
        }

        public ValidationResult Validate(T instanceToValidate, Dictionary<string, object> validationContext)
        {
            _validationContext = validationContext;
            return Validate(instanceToValidate);
        }

        public ValidationResult Validate(T instanceToValidate, Dictionary<string, object> validationContext, params string[] includingRuleSets)
        {
            _includingRuleSets = includingRuleSets?.ToList() ?? _includingRuleSets;
            return Validate(instanceToValidate, validationContext);
        }

        public ValidationResult Validate(T instanceToValidate, params string[] includingRuleSets)
        {
            _includingRuleSets = includingRuleSets?.ToList() ?? _includingRuleSets;
            return Validate(instanceToValidate);
        }

        protected abstract void ProccessRules();

        /// <summary>
        /// Regex match
        /// </summary>
        /// <param name="propertyNameSelector">Property seçici</param>
        /// <param name="pattern">Regex pattern</param>
        /// <param name="errorCode">Hata kodu</param>
        /// <param name="errorMessage">Hata mesajı</param>
        /// <returns></returns>
        protected bool RegexMatch(Expression<Func<T, string>> propertyNameSelector, string pattern, string errorCode = ErrorCodeValues.InvalidFormat, string? errorMessage = null)
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            if (!Regex.IsMatch(value, pattern))
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = errorCode;
                    opt.ErrorMessage = errorMessage;
                });

                return false;
            }

            return true;
        }

        /// <summary>
        /// Regex match
        /// </summary>
        /// <param name="propertyNameSelector">Property seçici</param>
        /// <param name="errorMessage">Hata mesajı</param>
        /// <returns></returns>
        protected bool IsValidSGKRegistirationNumber(Expression<Func<T, string>> propertyNameSelector, string? errorMessage = null)
        {
            return RegexMatch(propertyNameSelector, "^(1|2|3|4)\\s\\d{4}\\s\\d{2}\\s\\d{2}\\s\\d{7}\\s\\d{3}\\s\\d{2}-\\d{2}\\s\\d{3}$", ErrorCodeValues.InvalidFormat, errorMessage);
        }

        /// <summary>
        /// Is valid tax number
        /// </summary>
        /// <param name="propertyNameSelector"></param>
        /// <returns></returns>
        protected bool IsValidTaxNumber(Expression<Func<T, string>> propertyNameSelector)
        {
            // Boş olamaz
            if (!IsNotNullOrWhiteSpace(propertyNameSelector!)) return false;

            // Sadece sayısal değerler içerebilir.
            var isValid = OnlyContainsNumber(propertyNameSelector!);

            // 10 haneli olmalı
            isValid &= ExactLength(propertyNameSelector!, 10);

            var taxNumber = propertyNameSelector.Compile().Invoke(_instanceToValidate!);

            int tmp;
            int sum = 0;

            int lastDigit = Convert.ToInt32(Char.GetNumericValue(taxNumber[9]));
            for (int i = 0; i < 9; i++)
            {
                // (i + 1). sıradaki rakam
                int digit = Convert.ToInt32(taxNumber[i]);

                // Rakama 10 eklenir ve sıra değeri(i + 1) çıkarılır modül 10 a göre değeri alınır.
                tmp = (digit + 10 - (i + 1)) % 10;

                // i + 1 = sıra değeri
                // Elde edilen rakam 9 ise işlem yapılmadan bu değer alınır. 9 dan farklı ise 2^(10 - (i + 1)) ile çarpılır ve modül 9 a göre değeri alınır.
                // Her bir basamak için bu değerler toplanır.
                sum = (int)(tmp == 9 ? sum + tmp : sum + ((tmp * (Math.Pow(2, 10 - (i + 1)))) % 9));
            }

            // Toplam değerin modül 10 a göre değeri alınır. 10'dan çıkarılır. Ardından tekrar modül 10'a göre değeri alınır.
            // Çıkan sonuç son basamaktaki rakama eşit olmalıdır.
            if (lastDigit != (10 - (sum % 10)) % 10)
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.TaxNumberIsNotValid;
                });

                return false;
            }

            return true;
        }

        /// <summary>
        /// Returns true if IdentityNumber is valid
        /// </summary>
        /// <param name="propertyNameSelector"></param>
        /// <returns></returns>
        protected bool IsValidIdentityNumber(Expression<Func<T, string>> propertyNameSelector)
        {
            // Boş olamaz
            if (!IsNotNullOrWhiteSpace(propertyNameSelector!)) return false;

            // Sadece sayısal değerler içerebilir.
            var isValid = OnlyContainsNumber(propertyNameSelector!);

            // 11 haneli olmalı
            isValid &= ExactLength(propertyNameSelector!, 11);

            // 0 ile başlayamaz
            isValid &= CanNotStartWith(propertyNameSelector!, "0");

            // eğer yukarıdaki koşullar doğru değilse aşağıdaki kontrollere gerek yok
            if (!isValid)
                return false;

            var identityNumber = propertyNameSelector.Compile().Invoke(_instanceToValidate!);

            // 1,3,5,7,9 basamak toplamı
            int[] digitOrder = [1, 3, 5, 7, 9];
            var sumOfOddDigits = identityNumber.Where((number, index) => digitOrder.Contains(index + 1)).Sum(char.GetNumericValue);
            // 2,4,6,8 basamak toplamı
            digitOrder = [2, 4, 6, 8];
            var sumOfEvenDigits = identityNumber.Where((number, index) => digitOrder.Contains(index + 1)).Sum(char.GetNumericValue);

            // Stringi rakam listesine çevirme.
            double[] arrTC = identityNumber.Select(char.GetNumericValue).ToArray();

            // 1, 3, 5, 7, 9 basamaklarının toplamının 7 katından, 2, 4, 6, 8 basamaklarının toplamını çıkarttığımızda elde ettiğimiz sonucun 10’a bölümünden kalan sayı (MOD10)  10. basamaktaki sayıyı vermelidir. 
            if (arrTC[9] != (((sumOfOddDigits * 7) - sumOfEvenDigits) % 10))
            {
                AddError(propertyNameSelector, ErrorCodeValues.IdentityNumberIsNotValid);
                return false;
            }

            // İlk 10 hanenin toplamından elde edilen sonucun 10’a bölümünden kalan sayı (MOD10) 11. basamaktaki sayıyı vermelidir.
            if ((arrTC.Sum() - arrTC.Last()) % 10 != arrTC.Last())
            {
                AddError(propertyNameSelector, ErrorCodeValues.IdentityNumberIsNotValid);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Boş olamaz
        /// </summary>
        /// <typeparam name="TProperty">Property Type</typeparam>
        /// <param name="propertyNameSelector">Property seçici</param>
        /// <param name="errorMessage">Hata mesajı</param>
        /// <returns>Eğer "null" ise "false" döner ve "Required" hatası ekler</returns>
        protected bool NotNull<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, string? errorMessage = null)
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            if (value == null)
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.Required;
                    opt.ErrorMessage = errorMessage;
                });

                return false;
            }

            return true;
        }

        /// <summary>
        /// Boş olamaz
        /// </summary>
        /// <typeparam name="TProperty">Property Type</typeparam>
        /// <param name="propertyNameSelector">Property seçici</param>
        /// <param name="validateIf">Koşul geçerli ise validate et, koşul geçerli değil ise validasyon başarılı sayılır</param>
        /// <param name="errorMessage">Hata mesajı</param>
        /// <returns>Eğer "null" ise "false" döner ve "Required" hatası ekler</returns>
        protected bool NotNull<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, Expression<Func<T, bool>> validateIf, string? errorMessage = null)
        {
            if (!validateIf.Compile().Invoke(_instanceToValidate))
                return true;

            return NotNull(propertyNameSelector, errorMessage);
        }

        /// <summary>
        /// Array null veya empty olamaz
        /// </summary>
        /// <typeparam name="IEnumerable{TProperty}">Property Type</typeparam>
        /// <param name="propertyNameSelector">Property seçici</param>
        /// <param name="errorMessage">Hata mesajı</param>
        /// <returns>Eğer "null" ise "false" döner ve "Required" hatası ekler</returns>
        protected bool NotNullOrEmpty<TProperty>(Expression<Func<T, IEnumerable<TProperty>?>> propertyNameSelector, string? errorMessage = null)
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            if (value == null || value.Count() == 0)
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.RequiredArrayItem;
                    opt.ErrorMessage = errorMessage;
                });

                return false;
            }

            return true;
        }

        /// <summary>
        /// Array boş veya empty olmalı
        /// </summary>
        /// <typeparam name="IEnumerable{TProperty}">Property Type</typeparam>
        /// <param name="propertyNameSelector">Property seçici</param>
        /// <param name="errorMessage">Hata mesajı</param>
        /// <returns>Eğer "null" ise "false" döner ve "Required" hatası ekler</returns>
        protected bool MustBeNullOrEmpty<TProperty>(Expression<Func<T, IEnumerable<TProperty>>> propertyNameSelector, string? errorMessage = null)
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);
            if (value == null || !value.Any())
                return true;

            AddError(propertyNameSelector, opt =>
            {
                opt.ErrorCode = ErrorCodeValues.ArrayItemMustBeNullOrEmpty;
                opt.ErrorMessage = errorMessage;
            });

            return false;
        }


        /// <summary>
        /// "Null" olmalı
        /// </summary>
        /// <param name="propertyNameSelector">Property seçici</param>
        /// <param name="errorMessage">Hata mesajı</param>
        /// <returns>Eğer "null" değil ise "false" döner ve "MustBeNull" hatası ekler</returns>
        protected bool MustBeNull(Expression<Func<T, object>> propertyNameSelector, string? errorMessage = null)
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);
            if (value != null)
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MustBeNull;
                    opt.ErrorMessage = errorMessage;
                });

                return false;
            }

            return true;
        }

        /// <summary>
        /// "Null" olmalı
        /// </summary>
        /// <typeparam name="TProperty">Property Type</typeparam>
        /// <param name="propertyNameSelector">Property seçici</param>
        /// <param name="errorMessage">Hata mesajı</param>
        /// <returns>Eğer "null" değil ise "false" döner ve "MustBeNull" hatası ekler</returns>
        protected bool MustBeNull<TProperty>(Expression<Func<T, TProperty?>> propertyNameSelector, string? errorMessage = null) where TProperty : struct
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);
            if (value != null)
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MustBeNull;
                    opt.ErrorMessage = errorMessage;
                });

                return false;
            }

            return true;
        }

        /// <summary>
        /// "Null" olmalı
        /// </summary>
        /// <param name="propertyNameSelector">Property seçici</param>
        /// <param name="validateIf">Koşul geçerli ise validate et, koşul geçerli değil ise validasyon başarılı sayılır</param>
        /// <param name="errorMessage">Hata mesajı</param>
        /// <returns>Eğer "null" değil ise "false" döner ve "MustBeNull" hatası ekler</returns>
        protected bool MustBeNull(Expression<Func<T, object>> propertyNameSelector, Expression<Func<T, bool>> validateIf, string? errorMessage = null)
        {
            if (!validateIf.Compile().Invoke(_instanceToValidate))
                return true;

            return MustBeNull(propertyNameSelector, errorMessage);
        }

        /// <summary>
        /// String null veya sadece boş karakter olamaz.
        /// </summary>
        /// <param name="propertyNameSelector">Property seçici</param>
        /// <param name="errorMessage">Hata mesajı</param>
        /// <returns>Eğer "null" veya sadece boş karakter ise "false" döner ve "Required" hatası ekler</returns>
        protected bool IsNotNullOrWhiteSpace(Expression<Func<T, string?>> propertyNameSelector, string? errorMessage = null)
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            if (string.IsNullOrWhiteSpace(value))
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.Required;
                    opt.ErrorMessage = errorMessage;
                });

                return false;
            }

            return true;
        }

        protected bool OnlyContainsNumber(Expression<Func<T, string?>> propertyNameSelector, string? errorMessage = null)
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            if (string.IsNullOrWhiteSpace(value))
                return false;

            if (value.Any(c => !char.IsDigit(c)))
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MustBeDigit;
                    opt.ErrorMessage = errorMessage;
                });

                return false;
            }

            return true;
        }

        protected bool CanNotStartWith(Expression<Func<T, string?>> propertyNameSelector, string startValue, string? errorMessage = null)
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            if (string.IsNullOrWhiteSpace(value))
                return false;

            if (value.StartsWith(startValue))
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.CanNotStartWith;
                    opt.ErrorMessage = errorMessage;
                });

                return false;
            }

            return true;
        }

        protected bool OnlyContainsAlphaNumberic(Expression<Func<T, string>> propertyNameSelector, string? errorMessage = null)
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            if (string.IsNullOrWhiteSpace(value))
                return false;

            if (value.Any(c => !char.IsDigit(c) && !char.IsLetter(c)))
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MustBeAlphaNumeric;
                    opt.ErrorMessage = errorMessage;
                });

                return false;
            }

            return true;
        }

        protected bool OnlyContainsLetters(Expression<Func<T, string>> propertyNameSelector, string? errorMessage = null)
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            if (string.IsNullOrWhiteSpace(value))
                return false;

            if (value.Any(c => !char.IsLetter(c)))
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MustBeLetters;
                    opt.ErrorMessage = errorMessage;
                });

                return false;
            }

            return true;
        }


        protected bool ExactLength(Expression<Func<T, string>> propertyNameSelector, int exactLength, bool trim = false, string? errorMessage = null)
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            var length = trim ? value?.Trim()?.Length ?? 0 : value?.Length ?? 0;
            if (length != exactLength)
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.ExactLength;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object>
                        {
                            {"ATTEMPTED_VALUE_LENGTH", value?.Length ?? 0 },
                            {"ATTEMPTED_TRIMMED_VALUE_LENGTH", value?.Trim()?.Length ?? 0 },
                            {"EXACT_LENGTH", exactLength },
                        }
                    };
                });

                return false;
            }

            return true;
        }



        protected bool MinLength(Expression<Func<T, string>> propertyNameSelector, int minLength, bool trim = false, string? errorMessage = null)
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);
            var length = trim ? value?.Trim()?.Length ?? 0 : value?.Length ?? 0;

            if (length < minLength)
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MinimumLength;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object>
                        {
                            {"ATTEMPTED_VALUE_LENGTH", length },
                            {"REQUIRED_MINIMUM_LENGTH", minLength },
                        }
                    };
                });

                return false;
            }

            return true;
        }

        protected bool MaxLength(Expression<Func<T, string>> propertyNameSelector, int maxLength, bool trim = false, string? errorMessage = null)
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);
            var length = trim ? value?.Trim()?.Length ?? 0 : value?.Length ?? 0;

            if (length > maxLength)
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MaximumLength;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object>
                        {
                            {"ATTEMPTED_VALUE_LENGTH", length },
                            {"REQUIRED_MAX_LENGTH", maxLength },
                        }
                    };
                });

                return false;
            }

            return true;
        }

        protected bool GreaterThan<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, TProperty compareValue, string? errorMessage = null) where TProperty : struct
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            var valueExpression = Expression.Constant(value);
            var compareValueExpression = Expression.Constant(compareValue);
            var greaterThanExpr = Expression.GreaterThan(valueExpression, compareValueExpression);

            if (!Expression.Lambda<Func<bool>>(greaterThanExpr).Compile().Invoke())
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MustBeGreaterThan;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object>
                        {
                            { "MUST_BE_GREATER_THAN", compareValue }
                        }
                    };
                });

                return false;
            }

            return true;
        }

        protected bool GreaterThan<TProperty>(Expression<Func<T, TProperty?>> propertyNameSelector, TProperty? compareValue, string? errorMessage = null) where TProperty : struct
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);
            if (value == null)
                return false;

            var valueExpression = Expression.Constant(value);
            var compareValueExpression = Expression.Constant(compareValue);
            var greaterThanExpr = Expression.GreaterThan(valueExpression, compareValueExpression);

            if (!Expression.Lambda<Func<bool>>(greaterThanExpr).Compile().Invoke())
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MustBeGreaterThan;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object?>
                        {
                            { "MUST_BE_GREATER_THAN", compareValue }
                        }
                    };
                });

                return false;
            }

            return true;
        }

        protected bool GreaterThan<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, TProperty compareValue, Expression<Func<T, bool>> validateIf, string? errorMessage = null) where TProperty : struct
        {
            if (!validateIf.Compile().Invoke(_instanceToValidate))
                return true;

            return GreaterThan(propertyNameSelector, compareValue, errorMessage);
        }

        protected bool GreaterThan<TProperty>(Expression<Func<T, TProperty?>> propertyNameSelector, TProperty? compareValue, Expression<Func<T, bool>> validateIf, string? errorMessage = null) where TProperty : struct
        {
            if (!validateIf.Compile().Invoke(_instanceToValidate))
                return true;

            return GreaterThan(propertyNameSelector, compareValue, errorMessage);
        }
        protected bool GreaterThanOrEqual<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, TProperty compareValue, string? errorMessage = null) where TProperty : struct
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            var valueExpression = Expression.Constant(value);
            var compareValueExpression = Expression.Constant(compareValue);
            var greaterThanExpr = Expression.GreaterThanOrEqual(valueExpression, compareValueExpression);

            if (!Expression.Lambda<Func<bool>>(greaterThanExpr).Compile().Invoke())
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MustBeGreaterThanOrEqualTo;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object>
                        {
                            { "MUST_BE_GREATER_THAN_OR_EQUAL_TO", compareValue }
                        }
                    };
                });

                return false;
            }

            return true;
        }

        protected bool GreaterThanOrEqual<TProperty>(Expression<Func<T, TProperty?>> propertyNameSelector, TProperty? compareValue, string? errorMessage = null) where TProperty : struct
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);
            if (value == null)
                return false;

            var valueExpression = Expression.Constant(value);
            var compareValueExpression = Expression.Constant(compareValue);
            var greaterThanExpr = Expression.GreaterThanOrEqual(valueExpression, compareValueExpression);

            if (!Expression.Lambda<Func<bool>>(greaterThanExpr).Compile().Invoke())
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MustBeGreaterThanOrEqualTo;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object?>
                        {
                            { "MUST_BE_GREATER_THAN_OR_EQUAL_TO", compareValue }
                        }
                    };
                });

                return false;
            }

            return true;
        }

        protected bool GreaterThanOrEqual<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, TProperty compareValue, Expression<Func<T, bool>> validateIf, string? errorMessage = null) where TProperty : struct
        {
            if (!validateIf.Compile().Invoke(_instanceToValidate))
                return true;

            return GreaterThanOrEqual(propertyNameSelector, compareValue, errorMessage);
        }

        protected bool GreaterThanOrEqual<TProperty>(Expression<Func<T, TProperty?>> propertyNameSelector, TProperty? compareValue, Expression<Func<T, bool>> validateIf, string? errorMessage = null) where TProperty : struct
        {
            if (!validateIf.Compile().Invoke(_instanceToValidate))
                return true;

            return GreaterThanOrEqual(propertyNameSelector, compareValue, errorMessage);
        }

        protected bool LessThan<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, TProperty compareValue, string? errorMessage = null) where TProperty : struct
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            var valueExpression = Expression.Constant(value);
            var compareValueExpression = Expression.Constant(compareValue);
            var lessThanExpr = Expression.LessThan(valueExpression, compareValueExpression);

            if (!Expression.Lambda<Func<bool>>(lessThanExpr).Compile().Invoke())
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MustBeLessThan;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object>
                        {
                            { "MUST_BE_LESS_THAN", compareValue }
                        }
                    };
                });

                return false;
            }

            return true;
        }

        protected bool LessThan<TProperty>(Expression<Func<T, TProperty?>> propertyNameSelector, TProperty? compareValue, string? errorMessage = null) where TProperty : struct
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);
            if (value == null)
                return false;

            var valueExpression = Expression.Constant(value);
            var compareValueExpression = Expression.Constant(compareValue);
            var lessThanExpr = Expression.LessThan(valueExpression, compareValueExpression);

            if (!Expression.Lambda<Func<bool>>(lessThanExpr).Compile().Invoke())
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MustBeLessThan;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object?>
                        {
                            { "MUST_BE_LESS_THAN", compareValue }
                        }
                    };
                });

                return false;
            }

            return true;
        }

        protected bool LessThan<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, TProperty compareValue, Expression<Func<T, bool>> validateIf, string? errorMessage = null) where TProperty : struct
        {
            if (!validateIf.Compile().Invoke(_instanceToValidate))
                return true;

            return LessThan(propertyNameSelector, compareValue, errorMessage);
        }

        protected bool LessThan<TProperty>(Expression<Func<T, TProperty?>> propertyNameSelector, TProperty? compareValue, Expression<Func<T, bool>> validateIf, string? errorMessage = null) where TProperty : struct
        {
            if (!validateIf.Compile().Invoke(_instanceToValidate))
                return true;

            return LessThan(propertyNameSelector, compareValue, errorMessage);
        }

        protected bool LessThanOrEqual<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, TProperty compareValue, string? errorMessage = null) where TProperty : struct
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            var valueExpression = Expression.Constant(value);
            var compareValueExpression = Expression.Constant(compareValue);
            var lesserThanExpr = Expression.LessThanOrEqual(valueExpression, compareValueExpression);

            if (!Expression.Lambda<Func<bool>>(lesserThanExpr).Compile().Invoke())
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MustBeLessThanOrEqualTo;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object>
                        {
                            { "MUST_BE_LESS_THAN_OR_EQUAL_TO", compareValue }
                        }
                    };
                });

                return false;
            }

            return true;
        }

        protected bool LessThanOrEqual<TProperty>(Expression<Func<T, TProperty?>> propertyNameSelector, TProperty? compareValue, string? errorMessage = null) where TProperty : struct
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);
            if (value == null)
                return false;

            var valueExpression = Expression.Constant(value);
            var compareValueExpression = Expression.Constant(compareValue);
            var lesserThanExpr = Expression.LessThanOrEqual(valueExpression, compareValueExpression);

            if (!Expression.Lambda<Func<bool>>(lesserThanExpr).Compile().Invoke())
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MustBeLessThanOrEqualTo;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object?>
                        {
                            { "MUST_BE_LESS_THAN_OR_EQUAL_TO", compareValue }
                        }
                    };
                });

                return false;
            }

            return true;
        }

        protected bool LessThanOrEqual<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, TProperty compareValue, Expression<Func<T, bool>> validateIf, string? errorMessage = null) where TProperty : struct
        {
            if (!validateIf.Compile().Invoke(_instanceToValidate))
                return true;

            return LessThanOrEqual(propertyNameSelector, compareValue, errorMessage);
        }

        protected bool LessThanOrEqual<TProperty>(Expression<Func<T, TProperty?>> propertyNameSelector, TProperty? compareValue, Expression<Func<T, bool>> validateIf, string? errorMessage = null) where TProperty : struct
        {
            if (!validateIf.Compile().Invoke(_instanceToValidate))
                return true;

            return LessThanOrEqual(propertyNameSelector, compareValue, errorMessage);
        }

        /// <summary>
        /// Eşit Olmalı
        /// </summary>
        /// <typeparam name="TProperty"></typeparam>
        /// <param name="propertyNameSelector"></param>
        /// <param name="equalValue"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        protected bool MustBeEqual<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, TProperty equalValue, string? errorMessage = null)
        {
            TProperty value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            // eşit değil ise hata
            if (!value.Equals(equalValue))
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MustBe;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object?>
                        {
                            { "MUST_BE_EQUAL", equalValue }
                        }
                    };
                });

                return false;
            }

            return true;
        }

        /// <summary>
        /// Eşit Olmalı
        /// </summary>
        /// <typeparam name="TProperty"></typeparam>
        /// <param name="propertyNameSelector"></param>
        /// <param name="equalValue"></param>
        /// <param name="validateIf"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        protected bool MustBeEqual<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, TProperty equalValue, Expression<Func<T, bool>> validateIf, string? errorMessage = null)
        {
            if (!validateIf.Compile().Invoke(_instanceToValidate))
                return true;

            return MustBeEqual(propertyNameSelector, equalValue, errorMessage);
        }

        /// <summary>
        /// Eşit Olmalı
        /// </summary>
        /// <param name="propertyNameSelector"></param>
        /// <param name="equalValue"></param>
        /// <param name="validateIf"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        protected bool MustBeEqual(Expression<Func<T, string>> propertyNameSelector, string equalValue, Expression<Func<T, bool>> validateIf, string? errorMessage = null)
        {
            if (!validateIf.Compile().Invoke(_instanceToValidate))
                return true;

            return MustBeEqual(propertyNameSelector, equalValue, errorMessage);
        }

        /// <summary>
        /// Eşit Olmamalı
        /// </summary>
        /// <typeparam name="TProperty"></typeparam>
        /// <param name="propertyNameSelector"></param>
        /// <param name="notEqualValue"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        protected bool MustBeNotEqual<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, TProperty notEqualValue, string? errorMessage = null)
        {
            TProperty value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            // eşitse hata
            if (value.Equals(notEqualValue))
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.NotEqual;
                    opt.ErrorMessage = errorMessage;
                });

                return false;
            }

            return true;
        }

        protected bool AtLeastOneRequired(IEnumerable<Expression<Func<T, object>>> properties, string? errorMessage = null)
        {
            if (!properties.Select(p => p.Compile().Invoke(_instanceToValidate!)).Any(v => v is string ? !string.IsNullOrWhiteSpace((string)v) : v != null))
            {
                AddErrorToMultipleProperty(properties!, null, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.AtLeastOneRequired;
                    opt.ErrorMessage = errorMessage;
                });

                return false;
            }

            return true;
        }

        protected bool AtLeastOneRequiredEqual<TProperty>(IEnumerable<Expression<Func<T, TProperty>>> properties, TProperty equalValue, string? errorMessage = null)
        {
            if (!properties.Select(p => p.Compile().Invoke(_instanceToValidate)).Any(v => v.Equals(equalValue)))
            {
                AddErrorToMultipleProperty(properties, equalValue, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.AtLeastOneRequiredEqual;
                    opt.ErrorMessage = errorMessage;
                });

                return false;
            }

            return true;
        }

        protected bool MustBeSame<TProperty>(IEnumerable<Expression<Func<T, TProperty>>> properties, string? errorMessage = null)
        {
            var allValues = properties.Select(p => p.Compile().Invoke(_instanceToValidate));
            if (allValues.Distinct().Count() > 1)
            {
                AddErrorToMultipleProperty(properties, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.MustBeSame;
                    opt.ErrorMessage = errorMessage;
                });

                return false;
            }

            return true;
        }

        protected bool ExclusiveBetween<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, TProperty from, TProperty to, string? errorMessage = null) where TProperty : struct
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);
            var valueExpression = Expression.Constant(value);
            var compareFromValueExpression = Expression.Constant(from);
            var compareToValueExpression = Expression.Constant(to);
            var greaterThanExpr = Expression.GreaterThan(valueExpression, compareFromValueExpression);

            var lessThanExpr = Expression.LessThan(valueExpression, compareToValueExpression);
            var lambda = Expression.Lambda<Func<bool>>(Expression.And(greaterThanExpr, lessThanExpr)).Compile();


            if (!lambda.Invoke())
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.InvalidRange;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object>
                        {
                            { "EXCLUSIVE_FROM_RANGE", from },
                            { "EXCLUSIVE_TO_RANGE", to },
                        }
                    };
                });

                return false;
            }

            return true;
        }

        protected bool InclusiveBetween<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, TProperty from, TProperty to, string? errorMessage = null) where TProperty : struct
        {
            var value = propertyNameSelector.Compile().Invoke(_instanceToValidate);
            var valueExpression = Expression.Constant(value);
            var compareFromValueExpression = Expression.Constant(from);
            var compareToValueExpression = Expression.Constant(to);
            var greaterThanEqExpr = Expression.GreaterThanOrEqual(valueExpression, compareFromValueExpression);

            var lessThanEqExpr = Expression.LessThanOrEqual(valueExpression, compareToValueExpression);
            var lambda = Expression.Lambda<Func<bool>>(Expression.And(greaterThanEqExpr, lessThanEqExpr)).Compile();


            if (!lambda.Invoke())
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.InvalidRange;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object>
                        {
                            { "INCLUSIVE_FROM_RANGE", from },
                            { "INCLUSIVE_TO_RANGE", to },
                        }
                    };
                });

                return false;
            }

            return true;
        }

        public bool IsInEnum<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, string? errorMessage = null) where TProperty : Enum
        {
            var value = GetPropertyValue(propertyNameSelector);

            if (!Enum.IsDefined(typeof(TProperty), value))
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.InvalidRange;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object>
                        {
                            { "AVAILABLE_VALUES", string.Join(",", Enum.GetValues(typeof(TProperty))) }
                        }
                    };
                });

                return false;
            }

            return true;
        }

        public bool IsInEnum<TProperty>(Expression<Func<T, TProperty?>> propertyNameSelector, string? errorMessage = null) where TProperty : struct, Enum
        {
            var value = GetPropertyValue(propertyNameSelector);

            if (!Enum.IsDefined(typeof(TProperty), value))
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.InvalidRange;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        MessageContext = new Dictionary<string, object>
                        {
                            { "AVAILABLE_VALUES", string.Join(",", Enum.GetValues(typeof(TProperty))) }
                        }
                    };
                });

                return false;
            }

            return true;
        }

        protected bool Contains<TProperty>(Expression<Func<T, TProperty>> propertyNameSelector, IEnumerable<TProperty> expectedValues, string? errorMessage = null)
        {
            TProperty value = propertyNameSelector.Compile().Invoke(_instanceToValidate);

            if (!expectedValues.Contains(value))
            {
                AddError(propertyNameSelector, opt =>
                {
                    opt.ErrorCode = ErrorCodeValues.Contains;
                    opt.ErrorMessage = errorMessage;
                    opt.CustomState = new
                    {
                        ExpectedValue = expectedValues
                    };
                });

                return false;
            }

            return true;
        }

        public IValidator<T> CombineSetPrefix(params string[] names)
        {
            Prefix = string.Join(".", names.Where(x => !string.IsNullOrWhiteSpace(x))).Replace(".[", "[");
            return this;
        }

        public IValidator<T> SetThrowOnException()
        {
            ThrowOnException = true;
            return this;
        }

        public IValidator<T> SetThrowOffException()
        {
            ThrowOnException = false;
            return this;
        }
    }
}