﻿using System.Linq.Expressions;

namespace Datasoft.HR.ValidationInfrastructure
{
    public static class PredicateBuilder
    {
        public static string GetExpressionPropertyChain<TModel, TProperty>(this Expression<Func<TModel, TProperty>> expression)
        {
            var propNames = new List<string>();

            Expression body = expression.Body;
            while (body != null)
            {
                if (body is ParameterExpression)
                    break;

                if (body is MemberExpression)
                {
                    MemberExpression memberExpression = (MemberExpression)body;
                    propNames.Add(memberExpression.Member.Name);
                    body = memberExpression.Expression;
                }
                else if (body is MethodCallExpression)
                {
                    var methodCallExpression = (MethodCallExpression)body;

                    MemberExpression indexArgument = null;
                    MemberExpression callObject = null;

                    if (methodCallExpression.Method.Name.Equals(nameof(Enumerable.ElementAt)))
                    {
                        indexArgument = methodCallExpression.Arguments[1] as MemberExpression;
                        callObject = methodCallExpression.Arguments[0] as MemberExpression;
                    }
                    else if (methodCallExpression.Method.DeclaringType?.Name.StartsWith("List") == true
                        && methodCallExpression.Method.DeclaringType?.Namespace?.Equals("System.Collections.Generic") == true)
                    {
                        indexArgument = methodCallExpression.Arguments[0] as MemberExpression;
                        callObject = methodCallExpression.Object as MemberExpression;
                    }
                    else
                        throw new NotSupportedException(methodCallExpression.Method.Name);

                    if (indexArgument == null)
                        throw new ArgumentNullException(nameof(indexArgument));

                    var index = Expression.Lambda(indexArgument).Compile().DynamicInvoke();
                    propNames.Add((callObject?.Member?.Name ?? string.Empty) + "[" + index + "]");
                    body = callObject?.Expression;
                }
                else
                    throw new NotSupportedException(body.NodeType.ToString());
            }

            propNames.Reverse();
            return string.Join(".", propNames);
        }

        public static string GetPropertyPathWithoutIndex<TModel, TProperty>(Expression<Func<TModel, TProperty>> expression, string prefix = "")
        {
            return prefix + expression.ToString();
        }
    }
}