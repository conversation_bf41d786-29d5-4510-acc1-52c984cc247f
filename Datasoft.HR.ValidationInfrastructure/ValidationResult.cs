﻿namespace Datasoft.HR.ValidationInfrastructure
{
    //
    // Summary:
    //     The result of running a validator
    [Serializable]
    public class ValidationResult
    {
        private readonly List<ValidationFailure> _errors;

        //
        // Summary:
        //     Whether validation succeeded
        public virtual bool IsValid => Errors.Count == 0;

        //
        // Summary:
        //     A collection of errors
        public List<ValidationFailure> Errors => _errors;

        public string[] RuleSetsExecuted
        {
            get;
            internal set;
        }

        //
        // Summary:
        //     Creates a new validationResult
        public ValidationResult()
        {
            _errors = new List<ValidationFailure>();
        }

        //
        // Summary:
        //     Creates a new ValidationResult from a collection of failures
        //
        // Parameters:
        //   failures:
        //     List of FluentValidation.Results.ValidationFailure which is later available through
        //     FluentValidation.Results.ValidationResult.Errors. This list get's copied.
        //
        // Remarks:
        //     Every caller is responsible for not adding null to the list.
        public ValidationResult(IEnumerable<ValidationFailure> failures)
        {
            _errors = failures.Where((ValidationFailure failure) => failure != null).ToList();
        }

        internal ValidationResult(List<ValidationFailure> errors)
        {
            _errors = errors;
        }

        //
        // Summary:
        //     Generates a string representation of the error messages separated by new lines.
        public override string ToString()
        {
            return ToString(Environment.NewLine);
        }

        //
        // Summary:
        //     Generates a string representation of the error messages separated by the specified
        //     character.
        //
        // Parameters:
        //   separator:
        //     The character to separate the error messages.
        public string ToString(string separator)
        {
            return string.Join(separator, _errors.Select((ValidationFailure failure) => failure.ErrorMessage));
        }

        public void ClearErrors()
        {
            _errors.Clear();
        }
    }
}