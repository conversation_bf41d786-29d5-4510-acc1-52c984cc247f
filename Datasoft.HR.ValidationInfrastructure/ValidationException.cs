﻿namespace Datasoft.HR.ValidationInfrastructure
{
    //
    // Summary:
    //     An exception that represents failed validation
    [Serializable]
    public class ValidationException : Exception
    {
        //
        // Summary:
        //     Validation errors
        public IEnumerable<ValidationFailure>? Errors
        {
            get;
            private set;
        }

        //
        // Summary:
        //     Creates a new ValidationException
        //
        // Parameters:
        //   message:
        public ValidationException(string message)
            : this(message, Enumerable.Empty<ValidationFailure>())
        {
        }

        //
        // Summary:
        //     Creates a new ValidationException
        //
        // Parameters:
        //   message:
        //
        //   errors:
        public ValidationException(string message, IEnumerable<ValidationFailure> errors)
            : base(message)
        {
            Errors = errors;
        }

        //
        // Summary:
        //     Creates a new ValidationException
        //
        // Parameters:
        //   message:
        //
        //   errors:
        //
        //   appendDefaultMessage:
        //     appends default validation error message to message
        public ValidationException(string message, IEnumerable<ValidationFailure> errors, bool appendDefaultMessage)
            : base(appendDefaultMessage ? (message + " " + BuildErrorMessage(errors)) : message)
        {
            Errors = errors;
        }

        //
        // Summary:
        //     Creates a new ValidationException
        //
        // Parameters:
        //   errors:
        public ValidationException(IEnumerable<ValidationFailure> errors)
            : base(BuildErrorMessage(errors))
        {
            Errors = errors;
        }

        public static ValidationException Error(string errorCode, string message)
        {
            var failure = new ValidationFailure
            {
                ErrorCode = errorCode,
                ErrorMessage = message
            };
            return new ValidationException(new[] { failure });
        }
        public static ValidationException Error(Action<ValidationFailure> failureOption)
        {
            var failure = new ValidationFailure();
            failureOption.Invoke(failure);
            return new ValidationException(new[] { failure });
        }

        private static string BuildErrorMessage(IEnumerable<ValidationFailure> errors)
        {
            IEnumerable<string> values = errors.Select((ValidationFailure x) => Environment.NewLine + " -- " + x.PropertyName + ": " + x.ErrorMessage + " Severity: " + x.Severity);
            return "Validation failed: " + string.Join(string.Empty, values);
        }

        public static ValidationException FailWithErrorCode(string errorCode, string message)
        {
            return FailWithErrorCode(new ValidationFailure
            {
                ErrorCode = errorCode,
                ErrorMessage = message
            });
        }
        public static ValidationException FailWithErrorCode(params ValidationFailure[] failures)
        {
            return new ValidationException(failures);
        }
    }
}