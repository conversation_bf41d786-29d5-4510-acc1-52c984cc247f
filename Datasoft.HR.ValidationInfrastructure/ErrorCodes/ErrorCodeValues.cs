﻿namespace Datasoft.HR.ValidationInfrastructure.ErrorCodes
{
    public class ErrorCodeValues
    {
        public const string Required = "REQUIRED";

        /// <summary>
        /// Belirli parametre veya parametreler boş olmadığında ilgili alan da zorunludur
        /// </summary>
        public const string IsRequiredWhenOthersAreNotNull = "IS_REQUIRED_WHEN_OTHERS_ARE_NOT_NULL";
        /// <summary>
        /// Belirli parametre veya parametreler değer almış ise ilgili alan da zorunludur
        /// </summary>
        public const string IsRequiredWhenOthersAreContainsValue = "IS_REQUIRED_WHEN_OTHERS_ARE_CONTAINS_VALUE";
        public const string RequiredArrayItem = "REQUIRED_ARRAY_ITEM";
        public const string ArrayItemMustBeNullOrEmpty = "ARRAY_ITEM_MUST_BE_NULL_OR_EMPTY";
        public const string MustBeNull = "MUST_BE_NULL";
        public const string MustBeNullOrNone = "MUST_BE_NULL_OR_NONE";
        public const string Equal = "EQUAL";
        public const string NotEqual = "NOT_EQUAL";
        public const string Contains = "CONTAINS";
        public const string CanNotContains = "CAN_NOT_CONTAINS";
        public const string ArrayMustContainThatItem = "ARRAY_MUST_CONTAINS_THAT_ITEM";
        public const string AtLeastOneRequired = "AT_LEAST_ONE_REQUIRED";
        public const string AtLeastOneRequiredEqual = "AT_LEAST_ONE_REQUIRED_EQUAL";
        public const string AllRequiredEqual = "ALL_REQUIRED_EQUAL";
        public const string ExactLength = "EXACT_LENGTH";
        public const string MinimumLength = "MINIMUM_LENGTH";
        public const string MaximumLength = "MAXIMUM_LENGTH";
        public const string MustBe = "MUST_BE";
        public const string MustBeSame = "MUST_BE_SAME";
        public const string MustBeGreaterThan = "MUST_BE_GREATER_THAN";
        public const string MustBeGreaterThanOrEqualTo = "MUST_BE_GREATER_THAN_OR_EQUAL_TO";
        public const string MustBeLessThan = "MUST_BE_LESS_THAN";
        public const string MustBeLessThanOrEqualTo = "MUST_BE_LESS_THAN_OR_EQUAL_TO";
        public const string MustBeDigit = "MUST_BE_DIGIT";
        public const string MustBeAlphaNumeric = "MUST_BE_ALPHA_NUMERIC";
        public const string InvalidFormat = "INVALID_FORMAT";
        public const string AlreadyExist = "ALREADY_EXIST";
        public const string CombinationAlreadyExist = "COMBINATION_ALREADY_EXIST";
        public const string NotExist = "NOT_EXIST";
        public const string InconsistentData = "INCONSISTENT_DATA";
        public const string InvalidImageData = "INVALID_IMAGE_DATA";
        public const string InvalidHourData = "INVALID_HOUR_DATA";
        public const string InvalidMinuteData = "INVALID_MINUTE_DATA";
        public const string AccessDenied = "ACCESS_DENIED";
        public const string InvalidRange = "INVALID_RANGE";
        public const string InvalidOperation = "INVALID_OPERATION";
        public const string InvalidPeriodChoice = "INVALID_PERIOD_CHOICE";
        public const string CanNotUpdate = "CAN_NOT_UPDATE";
        public const string CanNotStartWith = "CAN_NOT_START_WITH";
        public const string MustBeLetters = "THE_VALUE_MUST_CONTAIN_ONLY_LETTERS";

        /// <summary>
        /// Sadece bir input değer alabilir
        /// </summary>
        public const string AbleToFillOnlyOneInput = "ABLE_TO_FILL_ONLY_ONE_INPUT";

        /// <summary>
        /// Liste içerisinde default item belirtilmeli
        /// </summary>
        public const string NeedDefaultChoice = "NEED_DEFAULT_CHOICE";

        /// <summary>
        /// Liste içerisinde sadece bir tane default item olabilir
        /// </summary>
        public const string DefaultChoiceCanSelectedOneTime = "DEFAULT_CHOICE_CAN_SELECTED_ONE_TIME";

        /// <summary>
        /// Bu operasyonu sadece owner kullanıcı yapabilir
        /// </summary>
        public const string YouCantDoThisOperationBecauseYouAreNotOwner = "YOU_CANT_DO_THIS_OPERATION_BECAUSE_YOU_ARE_NOT_OWNER";

        public const string NotFound = "NOT_FOUND";
        public const string UserNotFound = "USER_NOT_FOUND";
        public const string CompanyNotFound = "COMPANY_NOT_FOUND";
        public const string BranchNotFound = "BRANCH_NOT_FOUND";
        public const string BranchRequired = "BRANCH_REQUIRED";
        public const string RolesNotFound = "ROLES_NOT_FOUND";
        public const string UserApprovalNotFound = "USER_APPROVAL_NOT_FOUND";
        public const string CanNotRemoveOwnerUser = "CAN_NOT_REMOVE_OWNER_USER";
        public const string EmailOptionDefinationMissing = "EMAIL_OPTION_DEFINATION_MISSING";
        public const string CreateValidationError = "CREATE_VALIDATION_ERROR";
        public const string PasswordResetKeyNotFound = "PASSWORD_RESET_KEY_NOT_FOUND";
        public const string InvalidCompanyType = "INVALID_COMPANY_TYPE";
        public const string InvalidUserType = "INVALID_USER_TYPE";
        public const string UserAlreadyAssignedAsAgentBefore = "USER_ALREADY_ASSIGNED_AS_AGENT";
        public const string UserAlreadyAssignedAsDistributorBefore = "USER_ALREADY_ASSIGNED_AS_DISTRIBUTOR";
        public const string UserAlreadyAssignedAsChannelBefore = "USER_ALREADY_ASSIGNED_AS_CHANNEL";
        public const string UserAlreadyAssignedAsFinancialAdvisorBefore = "USER_ALREADY_ASSIGNED_AS_FINANCIAL_ADVISOR";
        public const string InvalidRefreshToken = "INVALID_REFRESH_TOKEN";
        public const string UserDontHaveAccessToCompanyPackage = "USER_DONT_HAVE_ACCESS_TO_COMPANY_PACKAGE";
        public const string UserDontHaveAccessToCompanyBranchDelete = "USER_DONT_HAVE_ACCESS_TO_COMPANY_BRANCH_DELETE";
        public const string OnlyOwnerUserAvailable = "ONLY_OWNER_USER_AVAILABLE";
        public const string PeriodProccessNeedCompanyAuth = "PERIOD_PROCCESS_NEED_COMPANY_AUTH";

        public const string PeriodStartDateCannotBeGreaterThanFinalizedReceiptDate = "PERIOD_START_DATE_CAN_NOT_BE_GREATER_THAN_FINALIZED_RECEIPT_DATE";

        public const string PeriodEndDateCannotBeLessThanFinalizedReceiptDate = "PERIOD_END_DATE_CAN_NOT_BE_LESS_THAN_FINALIZED_RECEIPT_DATE";
        public const string PeriodStartDateCannotBeGreaterThanBeforePeriodFinalizedReceiptDate = "PERIOD_START_DATE_CAN_NOT_BE_GREATER_THAN_BEFORE_PERIOD_FINALIZED_RECEIPT_DATE";
        public const string PeriodEndDateCannotBeLessThanNextPeriodFinalizedReceiptDate = "PERIOD_END_DATE_CANNOT_BE_LESS_THAN_NEXT_PERIOD_FINALIZED_RECEIPT_DATE";
        public const string NotAvailable = "NOT_AVAILABLE";
        public const string InvalidParentPeriodChoice = "INVALID_PARENT_PERIOD_CHOICE";
        public const string PeriodOnlyLastAndFirstCanBeRemoveable = "PERIOD_ONLY_LAST_AND_FIRST_CAN_BE_REMOVABLE";
        public const string PeriodCanNotBeRemoveHaveDatas = "PERIOD_CANNOT_BE_REMOVED_HAVE_DATAS";
        public const string PeriodEndDateMustBeGreaterThanStartDate = "PERIOD_END_DATE_MUST_BE_GREATER_THAN_START_DATE";
        public const string PeriodIntervalCannotBeGreaterThanOneYear = "PERIOD_INTERVAL_CANNOT_BE_GREATER_THAN_ONE_YEAR";
        public const string PeriodStartDateCannotBeLessThanEDefterTransitionDate = "PERIOD_START_DATE_CANNOT_BE_LESS_THAN_E_DEFTER_TRANSITION_DATE";
        public const string PeriodStartDateCannotBeLessThanFoundationDate = "PERIOD_START_DATE_CANNOT_BE_LESS_THAN_FOUNDATION_DATE";
        public const string PeriodEndDateCannotBeGreaterThanLeaveDate = "PERIOD_END_DATE_CANNOT_BE_GREATER_THAN_LEAVE_DATE";
        public const string PeriodEndDateMustBeEqualToLeaveDate = "PERIOD_END_DATE_MUST_BE_EQUAL_TO_LEAVE_DATE";
        public const string PeriodStartDateMustBeStartWithFirstDayOfMonth = "PERIOD_START_DATE_MUST_BE_START_WITH_FIRST_DAY_OF_MONTH";
        public const string PeriodStartDateMustBeStartWithJenuary = "PERIOD_START_DATE_MUST_BE_START_WITH_JANUARY";
        public const string PeriodEndDateMustBeEndWithLastDayOfMonth = "PERIOD_END_DATE_MUST_BE_END_WITH_LAST_DAY_OF_MONTH";
        public const string PeriodEndDateMustBeEndWithDecember = "PERIOD_END_DATE_MUST_BE_END_WITH_DECEMBER";
        public const string PeriodDateUsed = "PERIOD_DATE_USED";
        public const string PeriodAreYouSureForBeforeEndDateUpdate = "PERIOD_ARE_YOU_SURE_FOR_BEFORE_END_DATE_UPDATE";
        public const string PeriodAreYouSureForNextStartDateUpdate = "PERIOD_ARE_YOU_SURE_FOR_NEXT_START_DATE_UPDATE";
        public const string PeriodStartDateMustBeEqualToDateOfFoundation = "PERIOD_START_DATE_MUST_BE_EQUAL_TO_DATE_OF_FOUNDATION";

        /// <summary>
        /// Kimlik ya da vergi numarası farklı şirkette kullanıldı.
        /// </summary>
        public const string IdentityOrTaxNumberUsedInDifferentCompany = "IDENTITY_OR_TAX_NUMBER_USED_IN_DIFFERENT_COMPANY";

        /// <summary>
        /// Paket bulunamadı
        /// </summary>
        public const string PackageNotFoundOrYouDontHaveAcceses = "PACKAGE_NOT_FOUND_OR_YOU_DONT_HAVE_ACCESS";

        /// <summary>
        /// Paket aboneliği banka tarafında sonlandırıldığı için devam ettirilemezbulunamadı
        /// </summary>
        public const string PackageCanNotContinueWhenCanceledOnBank = "PACKAGE_CAN_NOT_CONTINUE_WHEN_CANCELED_ON_BANK";

        /// <summary>
        /// Kullanıcı paket içerisinde bulunamadı
        /// </summary>
        public const string UserNotFoundInPackage = "USER_NOT_FOUND_IN_PACKAGE";

        /// <summary>
        /// Kullanıcı daveti için paket bulunamadı
        /// </summary>
        public const string PackageNotFoundForUserInvitation = "PACKAGE_NOT_FOUND_FOR_USER_INVITATION";

        /// <summary>
        /// Kullanıcının zaten davet edilen lisansa erişimi var
        /// </summary>
        public const string UserAlreadyHasAccessInPackage = "USER_ALREADY_HAS_ACCESS_IN_PACKAGE";

        /// <summary>
        /// Paket kullanıcı limiti doldu
        /// </summary>
        public const string PackageUserLimitIsFull = "PACKAGE_USER_LIMIT_IS_FULL";

        /// <summary>
        /// Sadece onaylı mali müşavir ve personeli davet edilebilir
        /// </summary>
        public const string OnlyFinancialAdvisorsCanBeInvited = "ONLY_FINANCIAL_ADVISORS_CAN_BE_INVITED";

        /// <summary>
        /// Mali müşavir kullanıcısı bulunamadı
        /// </summary>
        public const string FinancialAdvisorUserNotFound = "FINANCIAL_ADVISOR_USER_NOT_FOUND";

        /// <summary>
        /// Mali müşaavir kullanıcısı onaylanmadı
        /// </summary>
        public const string FinancialAdvisorNotApproved = "FINANCIAL_ADVISOR_NOT_APPROVED";

        /// <summary>
        /// Ödeme sırasında bir hata oluştu
        /// </summary>
        public const string PaymentIntegrationError = "PAYMENT_INTEGRATION_ERROR";

        // Satın alınmış paketi var.
        public const string AlreadyHasPackage = "ALREADY_HAS_PACKAGE";

        /// <summary>
        /// Geçersiz değer hatası
        /// </summary>
        public const string InvalidValueError = "INVALID_VALUE_ERROR";

        /// <summary>
        /// Model Uyumsuz Hatası
        /// </summary>
        public const string InputModelValidationError = "INPUT_MODEL_VALİDATİON_ERROR";

        public const string IntegrationCompanyNotDefined = "_INTEGRATION_COMPANY_NOT_DEFINED";

        public const string IntegrationCompanyEntegratorInfoNotFound = "_INTEGRATION_COMPANY_ENTEGRATOR_INFO_NOT_FOUND";

        /// <summary>
        /// Kullanıcı sözleşme versiyonu boş olamaz
        /// </summary>
        public const string UserTermsOfUsageCanNotBeNull = "USER_TERMS_OF_USAGE_CAN_NOT_BE_NULL";

        /// <summary>
        /// Geçersiz Kullanıcı sözleşme versiyonu
        /// </summary>
        public const string UserTermsOfUsageIsInvalid = "USER_TERMS_OF_USAGE_IS_INVALID";

        /// <summary>
        /// Müşteri sözleşme versiyonu boş olamaz
        /// </summary>
        public const string CustomerTermsOfUsageCanNotBeNull = "CUSTOMER_TERMS_OF_USAGE_CAN_NOT_BE_NULL";

        /// <summary>
        /// Geçersiz Müşteri sözleşme versiyonu
        /// </summary>
        public const string CustomerTermsOfUsageIsInvalid = "CUSTOMER_TERMS_OF_USAGE_IS_INVALID";

        /// <summary>
        /// Kullanıcı aydınlatma metni versiyonu boş olamaz
        /// </summary>
        public const string UserSoftwareServiceMembershipInformationTextVersionCanNotBeNull = "USER_SOFTWARE_SERVICE_MEMBERSHIP_INFORMATION_TEXT_CAN_NOT_BE_NULL";

        /// <summary>
        /// Geçersiz kullanıcı aydınlatma metni versiyonu
        /// </summary>
        public const string UserSoftwareServiceMembershipInformationTextVersionIsInvalid = "USER_SOFTWARE_SERVICE_MEMBERSHIP_INFORMATION_TEXT_IS_INVALID";

        /// <summary>
        /// Fatura hatası
        /// </summary>
        public const string InvoiceError = "INVOICE_ERROR";

        /// <summary>
        /// Gerçek ya da atanan paket bulunamadı hatası
        /// </summary>
        public const string RealOrAssignedPackageNotFound = "REAL_OR_ASSIGNED_PACKAGE_NOT_FOUND";

        /// <summary>
        /// Geçersiz kredi kartı numarası
        /// </summary>
        public const string InvalidCreditCardNumber = "INVALID_CREDIT_CARD_NUMBER";

        // En az 1 ürün veya ek kullanıcı seçilmeli
        public const string AtLeastOneProductOrAdditionalUserMustBe = "AT_LEAST_ONE_PRODUCT_OR_ADDITIONAL_USER_MUST_BE";

        // Paket geçişi son ayda yapılmalı
        public const string PackageTransitionShouldBeDoneInTheLastMonth = "PACKAGE_TRANSITION_SHOULD_BE_DONE_IN_THE_LAST_MONTH";

        /// <summary>
        /// Kimlik numarası geçersiz
        /// </summary>
        public const string IdentityNumberIsNotValid = "IDENTITY_NUMBER_IS_NOT_VALID";

        /// <summary>
        /// Vergi numarası geçersiz
        /// </summary>
        public const string TaxNumberIsNotValid = "TAX_NUMBER_IS_NOT_VALID";

        /// <summary>
        /// Sözleşme içerisinde Sözleşme numarası bulunamadı
        /// </summary>
        public const string AggrementNumberNotFound = "AGGREMENT_NUMBER_NOT_FOUND";
        /// <summary>
        /// Sözleşme içerisinde Sözleşme numarası eşleşmedi
        /// </summary>
        public const string AggrementNumberNotMatch = "AGGREMENT_NUMBER_NOT_MATCH";

        /// <summary>
        /// Paket tanımı bulunamadı
        /// </summary>
        public const string PackageDefinationNotFound = "PACKAGE_DEFINATION_NOT_FOUND";

        /// <summary>
        /// "E-Defter" olmadan "E-Defter 2.nüsha gönderim ve saklama-24 berat" alınamaz.
        /// </summary>
        public const string ELedgerSendSecondCopyAndBackup24ReportCanNotPurchasedWithoutPurchaseELedgerIdentifier = "E_LEDGER_SEND_SECOND_COPY_AND_BACKUP_24_REPORT_CAN_NOT_PURCHASED_WITHOUT_PURCHASE_E_LEDGER_IDENTIFIER";

        /// <summary>
        /// Paketin süresi dolmuş, (Owner kullanıcı)
        /// </summary>
        public const string PackageExpiredForOWnerUser = "PACKAGE_EXPIRED_FOR_OWNER_USER";

        /// <summary>
        /// Paketin süresi dolmuş, (Davetli kullanıcı)
        /// </summary>
        public const string PackageExpiredForInvitedUser = "PACKAGE_EXPIRED_FOR_INVITED_USER";

        /// <summary>
        /// E-defter lisansı yenilenmeli
        /// </summary>
        public const string ELedgerPackageShouldBeRenew = "ELEDGER_PACKAGE_SHOULD_BE_RENEW";

        /// <summary>
        /// E-defter lisansı kredi kartından dolayı yenilenememiştir
        /// </summary>
        public const string ELedgerPackageShouldBeRenewBecauseCreditCard = "ELEDGER_PACKAGE_SHOULD_BE_RENEW_BECAUSE_CREDIT_CARD";
    }
}