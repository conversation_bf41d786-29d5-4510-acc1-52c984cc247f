﻿namespace Datasoft.HR.ValidationInfrastructure
{
    public class ValidationFailure
    {
        //
        // Summary:
        //     The name of the property.
        public string? PropertyName
        {
            get;
            set;
        }

        //
        // Summary:
        //     The error message
        public string? ErrorMessage
        {
            get;
            set;
        }

        //
        // Summary:
        //     The property value that caused the failure.
        public object? AttemptedValue
        {
            get;
            set;
        }

        //
        // Summary:
        //     Custom state associated with the failure.
        public object? CustomState
        {
            get;
            set;
        }


        //
        // Summary:
        //     Custom severity level associated with the failure.
        public ValidationSeverity Severity
        {
            get;
            set;
        }


        //
        // Summary:
        //     Gets or sets the error code.
        public string? ErrorCode
        {
            get;
            set;
        }

        //
        // Summary:
        //     Gets or sets the formatted message placeholder values.
        public Dictionary<string, object>? FormattedMessagePlaceholderValues
        {
            get;
            set;
        }

        public ValidationFailure()
        {
        }

        //
        // Summary:
        //     Creates a new validation failure.
        public ValidationFailure(string propertyName, string errorMessage)
            : this(propertyName, errorMessage, null)
        {
        }

        //
        // Summary:
        //     Creates a new ValidationFailure.
        public ValidationFailure(string propertyName, string errorMessage, object? attemptedValue)
        {
            PropertyName = propertyName;
            ErrorMessage = errorMessage;
            AttemptedValue = attemptedValue;
        }

        //
        // Summary:
        //     Creates a textual representation of the failure.
        public override string? ToString()
        {
            return ErrorMessage;
        }
    }
}