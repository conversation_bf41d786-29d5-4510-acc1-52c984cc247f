﻿using AutoMapper;
using Datasoft.HR.Core.Entities.Company;
using Datasoft.HR.Core.Entities.Parameters.Units;
using Datasoft.HR.DTO.Company;
using Datasoft.HR.DTO.Parameters.Units;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.CommonGeneralSetting;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Company;
using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Conversions.MapperProfiles
{
    public class GeneralSettingProfile : Profile
    {
        public GeneralSettingProfile()
        {
            CreateMap<PreAccountancyCompanyPeriod, PeriodInfoDto>().ReverseMap();
            CreateMap<Currency, CurrencyDto>().ReverseMap();
            CreateMap<GeneralSetting, GeneralSettingDto>().ReverseMap();

            CreateMap<GeneralSetting, DecimalSeparators>();

            CreateMap<SalesCommonGeneralSetting, GeneralSetting>()
                .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.CompanyName))
                .ForMember(dest => dest.IdentityNumber, opt => opt.MapFrom(src => src.IdentityNumber))
                .ForMember(dest => dest.LegalStructure, opt => opt.MapFrom(src => src.LegalStructure))
                .ForMember(dest => dest.MernisNumber, opt => opt.MapFrom(src => src.MernisNumber))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.PersonalTitle, opt => opt.MapFrom(src => src.PersonalTitle))
                .ForMember(dest => dest.Surname, opt => opt.MapFrom(src => src.Surname))
                .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.Title))
                .ForMember(dest => dest.TaxNumber, opt => opt.MapFrom(src => src.TaxNumber))
                .ForMember(dest => dest.MernisNumber, opt => opt.MapFrom(src => src.MernisNumber))
                .ForPath(dest => dest.Address.Country.Id, opt => opt.MapFrom(src => src.CountryId))
                .ForPath(dest => dest.Address.State.Id, opt => opt.MapFrom(src => src.StateId))
                .ForPath(dest => dest.Address.City.Id, opt => opt.MapFrom(src => src.CityId))
                .ForPath(dest => dest.Address.County.Id, opt => opt.MapFrom(src => src.CountyId))
                .ForPath(dest => dest.Address.Neighborhood, opt => opt.MapFrom(src => src.Neighborhood))
                .ForPath(dest => dest.Address.Avenue, opt => opt.MapFrom(src => src.Avenue))
                .ForPath(dest => dest.Address.Street, opt => opt.MapFrom(src => src.Street))
                .ForPath(dest => dest.Address.BuildingNo, opt => opt.MapFrom(src => src.BuildingNo))
                .ForPath(dest => dest.Address.ApartmentNumber, opt => opt.MapFrom(src => src.ApartmentNumber))
                .ForPath(dest => dest.Address.PostCode, opt => opt.MapFrom(src => src.PostCode))
                .ForPath(dest => dest.Address.AddressCode, opt => opt.MapFrom(src => src.AddressCode))
                .ForPath(dest => dest.Address.Annotation, opt => opt.MapFrom(src => src.Annotation))
                .ForPath(dest => dest.Language.Id, opt => opt.MapFrom(src => src.LanguageId))
                .ForPath(dest => dest.TaxLegislationCountry.Id, opt => opt.MapFrom(src => src.TaxLegislationCountryId))
                .ForPath(dest => dest.TaxOffice.Id, opt => opt.MapFrom(src => src.TaxOfficeId))
                //.ForPath(dest => dest.ActivityCode.Id, opt => opt.MapFrom(src => src.ActivityId))
                //.ForPath(dest => dest.ActivityCode.Code, opt => opt.MapFrom(src => src.ActivityCode))
                //.ForPath(dest => dest.ActivityCode.Description, opt => opt.MapFrom(src => src.ActivityDescription))
                //.ForPath(dest => dest.CommercialRegistryOffice.Id, opt => opt.MapFrom(src => src.CommercialRegistryOfficeId))
                //.ForPath(dest => dest.CommercialRegistryOffice.Code, opt => opt.MapFrom(src => src.CommercialRegistryOfficeCode))
                //.ForPath(dest => dest.CommercialRegistryOffice.Name, opt => opt.MapFrom(src => src.CommercialRegistryOfficeName))
                .ForPath(dest => dest.CompanyType, opt => opt.MapFrom(src => src.CorporateCompanyType))
                //.ForPath(dest => dest.EstablishmentInformation.Date, opt => opt.MapFrom(src => src.Date))
                //.ForPath(dest => dest.EstablishmentInformation.EstablishmentType, opt => opt.MapFrom(src => src.EstablishmentType))
                //.ForPath(dest => dest.EstablishmentInformation.Leave, opt => opt.MapFrom(src => src.Leave))
                //.ForPath(dest => dest.EstablishmentInformation.LeaveDate, opt => opt.MapFrom(src => src.LeaveDate))
                //.ForPath(dest => dest.EstablishmentInformation.LeaveReason, opt => opt.MapFrom(src => src.LeaveReason))
                //.ForPath(dest => dest.EstablishmentInformation.Number, opt => opt.MapFrom(src => src.Number))
                .ForPath(dest => dest.PartnerType, opt => opt.MapFrom(src => src.PartnerType))
                //.ForPath(dest => dest.TradeRegisterNumber, opt => opt.MapFrom(src => src.TradeRegisterNumber))
                .ReverseMap();

            CreateMap<SalesCommonGeneralSetting, GeneralSettingDto>()
                .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.CompanyName))
                .ForMember(dest => dest.IdentityNumber, opt => opt.MapFrom(src => src.IdentityNumber))
                .ForMember(dest => dest.LegalStructure, opt => opt.MapFrom(src => src.LegalStructure))
                .ForMember(dest => dest.MernisNumber, opt => opt.MapFrom(src => src.MernisNumber))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.PersonalTitle, opt => opt.MapFrom(src => src.PersonalTitle))
                .ForMember(dest => dest.Surname, opt => opt.MapFrom(src => src.Surname))
                .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.Title))
                .ForMember(dest => dest.TaxNumber, opt => opt.MapFrom(src => src.TaxNumber))
                .ForMember(dest => dest.MernisNumber, opt => opt.MapFrom(src => src.MernisNumber))
                .ForPath(dest => dest.Address.Country.Id, opt => opt.MapFrom(src => src.CountryId))
                .ForPath(dest => dest.Address.State.Id, opt => opt.MapFrom(src => src.StateId))
                .ForPath(dest => dest.Address.City.Id, opt => opt.MapFrom(src => src.CityId))
                .ForPath(dest => dest.Address.County.Id, opt => opt.MapFrom(src => src.CountyId))
                .ForPath(dest => dest.Address.Neighborhood, opt => opt.MapFrom(src => src.Neighborhood))
                .ForPath(dest => dest.Address.Avenue, opt => opt.MapFrom(src => src.Avenue))
                .ForPath(dest => dest.Address.Street, opt => opt.MapFrom(src => src.Street))
                .ForPath(dest => dest.Address.BuildingNo, opt => opt.MapFrom(src => src.BuildingNo))
                .ForPath(dest => dest.Address.ApartmentNumber, opt => opt.MapFrom(src => src.ApartmentNumber))
                .ForPath(dest => dest.Address.PostCode, opt => opt.MapFrom(src => src.PostCode))
                .ForPath(dest => dest.Address.AddressCode, opt => opt.MapFrom(src => src.AddressCode))
                .ForPath(dest => dest.Address.Annotation, opt => opt.MapFrom(src => src.Annotation))
                .ForPath(dest => dest.Language.Id, opt => opt.MapFrom(src => src.LanguageId))
                .ForPath(dest => dest.TaxLegislationCountry.Id, opt => opt.MapFrom(src => src.TaxLegislationCountryId))
                .ForPath(dest => dest.TaxOffice.Id, opt => opt.MapFrom(src => src.TaxOfficeId))
                //.ForPath(dest => dest.ActivityCode.Id, opt => opt.MapFrom(src => src.ActivityId))
                //.ForPath(dest => dest.ActivityCode.Code, opt => opt.MapFrom(src => src.ActivityCode))
                //.ForPath(dest => dest.ActivityCode.Description, opt => opt.MapFrom(src => src.ActivityDescription))
                //.ForPath(dest => dest.CommercialRegistryOffice.Id, opt => opt.MapFrom(src => src.CommercialRegistryOfficeId))
                //.ForPath(dest => dest.CommercialRegistryOffice.Code, opt => opt.MapFrom(src => src.CommercialRegistryOfficeCode))
                //.ForPath(dest => dest.CommercialRegistryOffice.Name, opt => opt.MapFrom(src => src.CommercialRegistryOfficeName))
                .ForMember(dest => dest.CompanyType, opt => opt.MapFrom(src => src.CorporateCompanyType))
                //.ForPath(dest => dest.EstablishmentInformation.Date, opt => opt.MapFrom(src => src.Date))
                //.ForPath(dest => dest.EstablishmentInformation.EstablishmentType, opt => opt.MapFrom(src => src.EstablishmentType))
                //.ForPath(dest => dest.EstablishmentInformation.Leave, opt => opt.MapFrom(src => src.Leave))
                //.ForPath(dest => dest.EstablishmentInformation.LeaveDate, opt => opt.MapFrom(src => src.LeaveDate))
                //.ForPath(dest => dest.EstablishmentInformation.LeaveReason, opt => opt.MapFrom(src => src.LeaveReason))
                //.ForPath(dest => dest.EstablishmentInformation.Number, opt => opt.MapFrom(src => src.Number))
                .ForMember(dest => dest.PartnerType, opt => opt.MapFrom(src => src.PartnerType));
                //.ForMember(dest => dest.TradeRegisterNumber, opt => opt.MapFrom(src => src.TradeRegisterNumber));

        }
    }

}
