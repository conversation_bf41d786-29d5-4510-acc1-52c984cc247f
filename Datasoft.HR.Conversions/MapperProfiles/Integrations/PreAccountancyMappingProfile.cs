﻿using AutoMapper;
using Datasoft.HR.Core.Common;
using Datasoft.HR.Core.Entities.Common;
using Datasoft.HR.Core.Entities.Parameters;
using Datasoft.HR.Core.Entities.Parameters.Regions;
using Datasoft.HR.Core.Entities.Parameters.Resources;
using Datasoft.HR.Core.Entities.Parameters.Units;
using Datasoft.HR.DTO.Company;
using Datasoft.HR.DTO.Parameters;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.ActivityCode;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Address;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.CommercialRegistryOffice;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Common;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Company;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Contact;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Currency;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Language;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Region;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Tax;
using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Conversions.MapperProfiles.Integrations
{
    public class PreAccountancyMappingProfile : Profile
    {
        public PreAccountancyMappingProfile()
        {
            
            CreateMap<PreAccountancyActivityCodeResponse, ActivityCode>().ReverseMap();
            CreateMap<PreAccountancyActivityCodeSearchRequest, ActivityCodeSearchDto>().ReverseMap();
            /*
            CreateMap<PreAccountancyBriefKindResponse, BriefKindDto>().ReverseMap();
            CreateMap<PreAccountancyBriefKindRequest, BriefKindQueryDto>().ReverseMap();
            */
            CreateMap<PreAccountancyCommercialRegistryOfficeResponse, CommercialRegistryOffice>().ReverseMap();
            CreateMap<PreAccountancyCommercialRegistryOfficeSearchRequest, CommercialRegistryOfficeSearchDto>().ReverseMap();
            
            CreateMap<PreAccountancyTaxOfficeResponse, TaxOffice>().ReverseMap();
            CreateMap<PreAccountancyTaxOfficeSearchRequest, TaxOfficeSearchDto>().ReverseMap();
            /*
            CreateMap<GeneralSetting, PreAccountancyCompanyNameAndEstablishmentDto>()
                .ForMember(dest => dest.Leave, opt => opt.MapFrom(src => src.EstablishmentInformation != null ? src.EstablishmentInformation.Leave : false))
                .ForMember(dest => dest.LeaveDate, opt => opt.MapFrom(src => src.EstablishmentInformation != null ? src.EstablishmentInformation.LeaveDate : null))
                .ForMember(dest => dest.Date, opt => opt.MapFrom(src => src.EstablishmentInformation != null ? src.EstablishmentInformation.Date : null));
            */
            Func<PreAccountancyTranslation, string, LanguageTranslation> translationSelectStatement = (tr, propertyName) => new LanguageTranslation
            {
                Key = tr.Key,
                LanguageId = tr.LanguageId,
                Language = tr.Language,
                Value = tr.Value,
                PropertyName = propertyName
            };
             
            CreateMap<PreAccountancyCountrySearchResponse, Country>()
                .ForMember(dest => dest.HaveState, opt => opt.MapFrom(src => src.IsState))
                .ForMember(dest => dest.Translations, opt => opt.MapFrom(src => src.Translations.Select(tr => translationSelectStatement(tr, nameof(Country.Name)))));

            CreateMap<PreAccountancyRegionSearchResponse, State>()
                .ForMember(dest => dest.CountryId, opt => opt.MapFrom(src => src.RootCountryId ?? 0))
                .ForMember(dest => dest.Translations, opt => opt.MapFrom(src => src.Translations.Select(tr => translationSelectStatement(tr, nameof(State.Name)))));

            CreateMap<PreAccountancyRegionSearchResponse, City>()
                .ForMember(dest => dest.CountryId, opt => opt.MapFrom(src => src.RootCountryId ?? 0))
                .ForMember(dest => dest.StateId, opt => opt.MapFrom(src => src.RootStateId))
                .ForMember(dest => dest.Translations, opt => opt.MapFrom(src => src.Translations.Select(tr => translationSelectStatement(tr, nameof(City.Name)))));

            CreateMap<PreAccountancyRegionSearchResponse, County>()
                .ForMember(dest => dest.CityId, opt => opt.MapFrom(src => src.RootCityId))
                .ForMember(dest => dest.CityName, opt => opt.MapFrom(src => src.RootCityName))
                .ForMember(dest => dest.Translations, opt => opt.MapFrom(src => src.Translations.Select(tr => translationSelectStatement(tr, nameof(County.Name)))));

            CreateMap<PreAccountancyCountry, Country>()
                .ForMember(dest => dest.HaveState, opt => opt.MapFrom(src => src.IsState))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.LanguageTranslation))
                .ReverseMap();

            CreateMap<PreAccountancyState, State>()
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.LanguageTranslation))
                .ReverseMap();

            CreateMap<PreAccountancyCity, City>()
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.LanguageTranslation))
                .ReverseMap();

            CreateMap<PreAccountancyCounty, County>()
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.LanguageTranslation))
                .ReverseMap();
           
            CreateMap<RegionSearchDto, PreAccountancyCountrySearchRequest>();
            
            CreateMap<RegionSearchDto, PreAccountancyStateSearchRequest>()
                .ForMember(dest => dest.CountryId, opt => opt.MapFrom(src => src.RootCountryId ?? 0));

            CreateMap<RegionSearchDto, PreAccountancyCitySearchRequest>()
                .ForMember(dest => dest.CountryId, opt => opt.MapFrom(src => src.RootCountryId ?? 0))
                .ForMember(dest => dest.StateId, opt => opt.MapFrom(src => src.RootStateId));

            CreateMap<RegionSearchDto, PreAccountancyCountySearchRequest>()
                .ForMember(dest => dest.CityId, opt => opt.MapFrom(src => src.RootCityId ?? 0));
            /*

            CreateMap<TreeViewSearchRequestDto, PreAccountancyTreeViewSearchRequest>();

            Func<PreAccountancyTreeViewSearchResponse, ResolutionContext, CustomerGroup> threeViewResponseAccountPlanCustomerGroupConverter = (x, context) =>
            {
                x.NamesTree.Add(x.GroupName);

                return new CustomerGroup
                {
                    PreAccountCustomerGroupId = x.Id,
                    Code = x.GroupCode,
                    Name = string.Join(" > ", x.NamesTree),
                    TenantId = (long?)context.Items["TenantId"]
                };
            };

            CreateMap<PreAccountancyTreeViewSearchResponse, CustomerGroup>()
                .ConvertUsing((x, a, c) => threeViewResponseAccountPlanCustomerGroupConverter(x, c));

            CreateMap<CustomerSupplierSearchRequestDto, PreAccountancyCustomerSupplierSearchRequest>()
               .ForMember(dest => dest.SearchWord, opt => opt.MapFrom(src => src.SearchTerm))
               .ForMember(dest => dest.IdList, opt => opt.MapFrom(src => src.Ids));

            CreateMap<PreAccountancyOptionalWithholdingDto, OptionalWithholding>().ReverseMap();

            CreateMap<PreAccountancyCustomerSupplierInformation, CustomerSupplierInfo>()
                .ForMember(dest => dest.LegalStructure, opt => opt.MapFrom(src => src.LegalStracture))
                .ForMember(dest => dest.TenantId, opt => opt.MapFrom((src, dest, tenantId, context) => context.Items.ContainsKey("TenantId") ? context.Items["TenantId"] : null))
                .ReverseMap();
            */
            CreateMap<PreAccountancyContact, Contact>().ReverseMap();
            CreateMap<PreAccountancyAddress, Address>();
            
            CreateMap<Address, PreAccountancyAddress>()
                .ForMember(dest => dest.CountryId, opt => opt.MapFrom(src => src.Country != null ? src.Country.Id : 0))
                .ForMember(dest => dest.CountryName, opt => opt.MapFrom(src => src.Country != null ? src.Country.Name : null))
                .ForMember(dest => dest.CountryNumberCode, opt => opt.MapFrom(src => src.Country != null ? src.Country.NumberCode : null))

                .ForMember(dest => dest.StateId, opt => opt.MapFrom(src => src.State != null ? src.State.Id : (long?)null))

                .ForMember(dest => dest.CityId, opt => opt.MapFrom(src => src.City != null ? src.City.Id : (long?)null))
                .ForMember(dest => dest.CityCode, opt => opt.MapFrom(src => src.City != null ? src.City.Code : null))
                .ForMember(dest => dest.CityName, opt => opt.MapFrom(src => src.City != null ? src.City.Name : null))

                .ForMember(dest => dest.CountyId, opt => opt.MapFrom(src => src.County != null ? src.County.Id : (long?)null))
                .ForMember(dest => dest.CountyCode, opt => opt.MapFrom(src => src.County != null ? src.County.Code : null))
                .ForMember(dest => dest.CountyName, opt => opt.MapFrom(src => src.County != null ? src.County.Name : null))
                .ForMember(dest => dest.CountyHalCode, opt => opt.MapFrom(src => src.County != null ? src.County.HalCode : null));
            /*
            CreateMap<PreAccountancyCustomerSupplierSearchResponse, CustomerSupplier>()
                    .ForMember(dest => dest.GeneralInfo, opt => opt.MapFrom(src => src.Customer))
                    .ForMember(dest => dest.TenantId, opt => opt.MapFrom((src, dest, tenantId, context) => context.Items["TenantId"]));

            CreateMap<PreAccountancyCustomerSupplierSearchResponse, CustomerSupplierIntegrationDto>()
                    .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Customer.Code))
                    .ForMember(dest => dest.IdentityNumber, opt => opt.MapFrom(src => src.Customer.IdentityNumber))
                    .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Customer.Name))
                    .ForMember(dest => dest.PersonalTitle, opt => opt.MapFrom(src => src.Customer.PersonalTitle))
                    .ForMember(dest => dest.PreAccountCustomerId, opt => opt.MapFrom(src => src.Customer.Id))
                    .ForMember(dest => dest.Surname, opt => opt.MapFrom(src => src.Customer.Surname))
                    .ForMember(dest => dest.TaxNumber, opt => opt.MapFrom(src => src.Customer.TaxNumber))
                    .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.Customer.Title))
                    .ForMember(dest => dest.OperatorTypeId, opt => opt.MapFrom(src => src.Customer.OperatorTypeId))
                    .ForMember(dest => dest.ScenarioId, opt => opt.MapFrom(src => src.Customer.ScenarioId))
                    .ForMember(dest => dest.TenantId, opt => opt.MapFrom((src, dest, tenantId, context) => context.Items["TenantId"]))
                    .ForMember(dest => dest.Origin, opt => opt.MapFrom(src => src.Customer.Origin));

            CreateMap<CustomerSupplier, PreAccountancyCustomerSupplierDto>()
                .ForMember(dest => dest.Information, opt => opt.MapFrom(src => src.GeneralInfo))
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.GeneralInfo.Id))
                .AfterMap((src, dest, context) =>
                {
                    if (context.Options.Items.ContainsKey("OldData") || (src.Addresses?.Any() ?? false))
                    {
                        dest.AddressBatch = new PreAccountancyBatch<PreAccountancyAddress>();

                        var addresses = src.Addresses != null
                        ? context.Mapper.Map<List<PreAccountancyAddress>>(src.Addresses)
                        : new List<PreAccountancyAddress>();

                        if (addresses.Count == 1)
                            addresses[0].IsDefault = true;

                        dest.AddressBatch.AddedItems = addresses.Where(x => x.Id <= 0).ToList();
                        dest.AddressBatch.UpdatedItems = addresses.Where(x => x.Id >= 1).ToList();
                        if (context.Options.Items.ContainsKey("OldData"))
                        {
                            var oldData = (CustomerSupplier)context.Options.Items["OldData"];
                            if (oldData?.Addresses?.Any() ?? false)
                            {
                                var newIds = addresses.Select(x => x.Id).Where(x => x > 0);
                                var oldIds = oldData.Addresses.Select(x => x.Id.Value);
                                dest.AddressBatch.RemovedItems = oldIds.Except(newIds).Select(Id => new PreAccountancyAddress { Id = Id }).ToList();
                            }
                        }
                    }

                    if (context.Options.Items.ContainsKey("OldData") || (src.Contacts?.Any() ?? false))
                    {
                        dest.ContactInfoBatch = new PreAccountancyBatch<PreAccountancyContact>();

                        var contacts = src.Contacts != null
                        ? context.Mapper.Map<List<PreAccountancyContact>>(src.Contacts)
                        : new List<PreAccountancyContact>();

                        dest.ContactInfoBatch.AddedItems = contacts.Where(x => x.Id <= 0).ToList();
                        dest.ContactInfoBatch.UpdatedItems = contacts.Where(x => x.Id >= 1).ToList();
                        if (context.Options.Items.ContainsKey("OldData"))
                        {
                            var oldData = (CustomerSupplier)context.Options.Items["OldData"];
                            if (oldData?.Contacts?.Any() ?? false)
                            {
                                var newIds = contacts.Select(x => x.Id).Where(x => x > 0);
                                var oldIds = oldData.Addresses.Select(x => x.Id.Value);
                                dest.ContactInfoBatch.RemovedItems = oldIds.Except(newIds).Select(Id => new PreAccountancyContact { Id = Id }).ToList();
                            }
                        }
                    }
                });

            CreateMap<MeasurementSearchRequestDto, PreAccountancyMeasurementSearchRequest>();
            CreateMap<PreAccountancyMeasurementResponse, Measurement>()
                .ForMember(dest => dest.Translations, opt => opt.MapFrom(src => src.Translations.Select(tr => translationSelectStatement(tr, nameof(Measurement.Name)))));
            */
            CreateMap<CurrencySearchRequestDto, PreAccountancyCurrencySearchRequest>();
            
            CreateMap<PreAccountancyCurrencyResponse, Currency>()
                .ForMember(dest => dest.Translations, opt => opt.MapFrom(src => src.Translations.Select(tr => translationSelectStatement(tr, nameof(Currency.Name)))));
            /*
            CreateMap<PreAccountancyCurrencyResponse, ReceiptCurrency>().ForMember(dest => dest.Value, opt => opt.Ignore());
            */
            CreateMap<LanguageSearchRequestDto, PreAccountancyLanguageSearchRequest>();
 
            CreateMap<PreAccountancyLanguageResponse, Language>()
                .ForMember(dest => dest.Translations, opt => opt.MapFrom(src => src.Translations.Select(tr => translationSelectStatement(tr, nameof(Language.Name)))));
            /*

            CreateMap(typeof(PagingInput<>), typeof(TenantRequest<>))
                .ForMember(nameof(TenantRequest.BranchId), src => src.Ignore())
                .ForMember(nameof(TenantRequest.PeriodId), src => src.Ignore())
                .ReverseMap();
            CreateMap(typeof(PagingInput<>), typeof(PagingInput<>)).ReverseMap();
            CreateMap(typeof(TenantRequest<>), typeof(TenantRequest<>)).ReverseMap();
            CreateMap<PagingInput, PagingInput>();
            */
            CreateMap<PreAccountancyPagingInput, PagingInput>().ReverseMap();
            /*
            CreateMap<SalesPagingOutput, PagingInput>().ReverseMap();
            */
            CreateMap(typeof(PreAccountancyPagingInput<>), typeof(PagingInput<>)).ReverseMap();
            /*
            CreateMap(typeof(PreAccountancyResult<>), typeof(PagingOutput<>))
                        .ForMember("PageIndex", (src) => src.MapFrom("PagingOutput.PageIndex"))
                        .ForMember("PageCount", (src) => src.MapFrom("PagingOutput.PageCount"))
                        .ForMember("RecordCount", (src) => src.MapFrom("PagingOutput.RecordCount"));

            CreateMap(typeof(SalesDatasoftResult<>), typeof(PagingOutput<>))
                        .ForMember("PageIndex", (src) => src.MapFrom("PagingOutput.PageIndex"))
                        .ForMember("PageCount", (src) => src.MapFrom("PagingOutput.PageCount"))
                        .ForMember("RecordCount", (src) => src.MapFrom("PagingOutput.RecordCount"));
            
            CreateMap<ExchangeRateRequetDto, PreAccountancyExchangeQueryDto>();
            CreateMap<PreAccountancyExchangeRateDto, ExchangeRateResponseDto>();
            */
            CreateMap<PreAccountancyCompanyPeriod, CompanyPeriodDto>();
            

        }
    }

}
