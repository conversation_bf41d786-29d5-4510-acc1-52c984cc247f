﻿using AutoMapper;
using Datasoft.HR.DTO.Company;
using DatasoftCommon.ApiClients.DatasoftPreAccountancy.Models.Company;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Datasoft.HR.Conversions.MapperProfiles.Integrations
{
    public class PreAccountancyCompanyMappingProfile : Profile
    {
        public PreAccountancyCompanyMappingProfile()
        {
            /*
            CreateMap<PreAccountancyUserCompany, IntegratedCompany>()
                  .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.CompanyId))
                  .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.CompanyName))
                  .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.CompanyCode));
            */
            //CreateMap<PreAccountancyUserCompany, CompanySearchResponseDto>();
            CreateMap<PreAccountancyUserCompany, CompanyBranchSearchResponseDto>();
            CreateMap<PreAccountancyBranchFilterDto, BranchFilterDto>();
            //CreateMap<CompanySearchQueryDto, PreAccountancyCompanySearchQuery>();


            CreateMap<PreAccountancyCompanyBranchInfoDto, BranchInfoDto>();
        }
    }
}
