﻿using AutoMapper;
using Datasoft.HR.Core.Common;
using MongoDB.Driver.Linq;

namespace Datasoft.HR.Conversions
{
    public static class ConversionExtension
    {
        public static TDestination Map<TDestination>(this object source)
        {
            return ServiceLocator.ResolveService<IMapper>().Map<TDestination>(source);
        }
        public static TDestination Map<TDestination>(this object source, Action<IMappingOperationOptions<object, TDestination>> opts)
        {
            return ServiceLocator.ResolveService<IMapper>().Map(source, opts);
        }

        public static async Task<TDestination> MapAsync<TSource, TDestination>(this Task<TSource> source) where TSource : class where TDestination : class
        {
            return ServiceLocator.ResolveService<IMapper>().Map<TDestination>(await source);
        }

        public static IMongoQueryable<TDestination> ProjectTo<TDestination>(this IMongoQueryable sources)
        {
            var mapper = ServiceLocator.ResolveService<IMapper>();
            return (IMongoQueryable<TDestination>)mapper.ProjectTo<TDestination>(sources);
        }
    }
}
