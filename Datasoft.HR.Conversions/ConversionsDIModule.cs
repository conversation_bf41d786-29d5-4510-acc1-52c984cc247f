﻿using Datasoft.HR.Conversions.MapperProfiles;
using Datasoft.HR.Conversions.MapperProfiles.Integrations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Datasoft.HR.Conversions
{
    public static class ConversionsDIModule
    {
        public static void Inject(IServiceCollection services, IConfiguration configuration)
        {
            // TODO: Auto injection yapılacak
            services.AddAutoMapper(cfg =>
            {
                cfg.AddProfile<ExampleProfile>();
                cfg.AddProfile<PreAccountancyMappingProfile>();
                cfg.AddProfile<GeneralSettingProfile>();
                cfg.AddProfile<PreAccountancyCompanyMappingProfile>();
                cfg.AddProfile<BranchProfile>();

            });
        }
    }
}
